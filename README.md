# KtoOstatni - System Reklamowy i Kolejkowy

## Struktura projektu po refaktoringu

Aplikacja została podzielona na następujące katalogi:

### `/admin/` - Główna aplikacja
Zawiera całą logikę aplikacji zgodnie z wzorcem MVC:
- `controllers/` - Kontrolery aplikacji
- `models/` - <PERSON>e danych
- `views/` - <PERSON><PERSON><PERSON> (szablony)
- `core/` - <PERSON><PERSON><PERSON> ap<PERSON> (Router, Database, Controller)
- `assets/` - Pliki staty<PERSON>ne (CSS, JS, obrazy)
- `index.php` - Główny punkt wejścia aplikacji
- `.htaccess` - Konfiguracja serwera Apache
- `favicon.ico` - Ikona aplikacji
- `init_db.php` - Inicjalizacja bazy danych
- `migrate_*.php` - Pliki migracji

### `/database/` - <PERSON><PERSON> danych
Zawiera wszystkie pliki baz danych SQLite:
- `reklama.db` - Główna baza danych
- `reklama2.db` - Dodatkowa baza danych
- `database.sqlite3` - Baza danych systemu kolejkowego

### `/uploads/` - Pliki wgrywane
Zawiera wszystkie pliki wgrywane przez użytkowników:
- `campaigns/` - Pliki kampanii reklamowych
- `doctors/` - Zdjęcia lekarzy
- `*.csv` - Pliki CSV do importu

### `/docs/` - Dokumentacja
Zawiera wszystkie pliki dokumentacji:
- `README.md` - Główna dokumentacja
- `QUEUE_SYSTEM_UPDATE.md` - Aktualizacje systemu kolejkowego
- `CSV_IMPORT_SUMMARY.md` - Podsumowanie importu CSV
- `IMPORT_CSV_GUIDE.md` - Instrukcja importu CSV

### `/pwa/` - Progressive Web App
Zawiera pliki PWA dla lekarzy:
- `index.html` - Główna strona PWA
- `app.js` - Logika PWA
- `styles.css` - Style PWA
- `manifest.json` - Manifest PWA
- `sw.js` - Service Worker
- `icons/` - Ikony PWA

## Uruchomienie aplikacji

1. **Główna aplikacja**: Przejdź do `/admin/` w przeglądarce
2. **PWA dla lekarzy**: Przejdź do `/pwa/` w przeglądarce
3. **Dostęp publiczny**: Główny katalog `/` automatycznie przekierowuje do `/admin/`

## Konfiguracja serwera

Aplikacja wymaga serwera Apache z włączonym modułem `mod_rewrite`.

### Wymagania:
- PHP 8.0+
- Apache z mod_rewrite
- SQLite3

### Uprawnienia:
- Katalog `uploads/` musi mieć uprawnienia do zapisu
- Katalog `database/` musi mieć uprawnienia do zapisu

## Struktura URL

Po refaktoringu wszystkie URL-e aplikacji pozostają bez zmian:
- `/admin/` - Panel administratora
- `/client/` - Panel klienta
- `/login` - Logowanie
- `/register` - Rejestracja
- `/pwa/` - Aplikacja PWA dla lekarzy

## Migracja

Refaktoring zachowuje pełną kompatybilność z istniejącymi danymi. Wszystkie ścieżki w bazie danych i konfiguracjach zostały zaktualizowane automatycznie. 