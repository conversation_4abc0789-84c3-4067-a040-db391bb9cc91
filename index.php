<?php
// Główny router dla systemu wieloaplikacyjnego
$request_uri = $_SERVER['REQUEST_URI'];

// Debug - wyświetl informacje o routingu (tymczasowo)
if (isset($_GET['debug'])) {
    echo "Main Router Debug:<br>";
    echo "Original URI: " . $_SERVER['REQUEST_URI'] . "<br>";
    echo "Script Name: " . $_SERVER['SCRIPT_NAME'] . "<br>";
    echo "Current Dir: " . __DIR__ . "<br>";
    echo "Request Method: " . $_SERVER['REQUEST_METHOD'] . "<br>";
    exit;
}

// Routing dla aplikacji admin
if (strpos($request_uri, '/admin/') === 0 || $request_uri === '/admin') {
    // Przekieruj do aplikacji admin
    $admin_path = __DIR__ . '/admin/index.php';
    if (file_exists($admin_path)) {
        include $admin_path;
        return;
    } else {
        http_response_code(404);
        echo "Aplikacja admin nie została znaleziona.";
        return;
    }
}

// Routing dla aplikacji TV (przyszłość)
if (strpos($request_uri, '/tv/') === 0 || $request_uri === '/tv') {
    // Przekieruj do aplikacji TV (jeszcze nie istnieje)
    http_response_code(404);
    echo "Aplikacja TV jeszcze nie została utworzona.";
    return;
}

// Routing dla plików PWA
if (strpos($request_uri, '/pwa/') === 0) {
    $file_path = __DIR__ . $request_uri;
    if (file_exists($file_path) && is_file($file_path)) {
        $extension = pathinfo($file_path, PATHINFO_EXTENSION);
        
        // Ustaw odpowiednie nagłówki MIME
        $mime_types = [
            'html' => 'text/html',
            'css' => 'text/css',
            'js' => 'application/javascript',
            'json' => 'application/json',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon',
            'webp' => 'image/webp'
        ];
        
        if (isset($mime_types[$extension])) {
            header('Content-Type: ' . $mime_types[$extension]);
        }
        
        readfile($file_path);
        return;
    }
}

// Routing dla plików uploads
if (strpos($request_uri, '/uploads/') === 0) {
    $file_path = __DIR__ . $request_uri;
    if (file_exists($file_path) && is_file($file_path)) {
        $extension = pathinfo($file_path, PATHINFO_EXTENSION);
        
        // Ustaw odpowiednie nagłówki MIME
        $mime_types = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'mp4' => 'video/mp4',
            'webm' => 'video/webm',
            'ogv' => 'video/ogg',
            'pdf' => 'application/pdf',
            'csv' => 'text/csv'
        ];
        
        if (isset($mime_types[$extension])) {
            header('Content-Type: ' . $mime_types[$extension]);
        }
        
        readfile($file_path);
        return;
    }
}

// Routing dla plików assets (tylko jeśli nie są w aplikacjach)
if (strpos($request_uri, '/assets/') === 0) {
    $file_path = __DIR__ . $request_uri;
    if (file_exists($file_path) && is_file($file_path)) {
        $extension = pathinfo($file_path, PATHINFO_EXTENSION);
        
        // Ustaw odpowiednie nagłówki MIME
        $mime_types = [
            'css' => 'text/css',
            'js' => 'application/javascript',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon',
            'webp' => 'image/webp'
        ];
        
        if (isset($mime_types[$extension])) {
            header('Content-Type: ' . $mime_types[$extension]);
        }
        
        readfile($file_path);
        return;
    }
}

// Strona główna - wyświetl listę dostępnych aplikacji
if ($request_uri === '/' || $request_uri === '') {
    ?>
    <!DOCTYPE html>
    <html lang="pl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>System Wieloaplikacyjny</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .app { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
            .app h2 { margin-top: 0; }
            .app a { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 3px; }
            .app a:hover { background: #0056b3; }
        </style>
    </head>
    <body>
        <h1>System Wieloaplikacyjny</h1>
        <p>Wybierz aplikację, którą chcesz uruchomić:</p>
        
        <div class="app">
            <h2>Panel Administracyjny</h2>
            <p>Zarządzanie kolejką, reklamami i użytkownikami</p>
            <a href="/admin/">Uruchom aplikację</a>
        </div>
        
        <div class="app">
            <h2>Aplikacja TV (w przygotowaniu)</h2>
            <p>Wyświetlanie systemu kolejkowego i reklam na telewizorach</p>
            <a href="/tv/">Uruchom aplikację</a>
        </div>
        
        <div class="app">
            <h2>PWA dla Lekarzy</h2>
            <p>Progressive Web App dla lekarzy</p>
            <a href="/pwa/">Uruchom aplikację</a>
        </div>
    </body>
    </html>
    <?php
    return;
}

// Jeśli nie znaleziono żądanej ścieżki
http_response_code(404);
echo "404 - Strona nie została znaleziona.";
?> 