<?php
// Router dla wbudowanego serwera PHP
$request_uri = $_SERVER['REQUEST_URI'];

// Przekieruj żądania do katalogu admin
if (strpos($request_uri, '/admin/') === 0 || $request_uri === '/admin') {
    $file_path = __DIR__ . '/admin' . $request_uri;
    
    // Jeś<PERSON> to katalog, dodaj index.php
    if (is_dir($file_path)) {
        $file_path .= '/index.php';
    }
    
    // Jeśli plik nie istnieje, użyj index.php
    if (!file_exists($file_path)) {
        $file_path = __DIR__ . '/admin/index.php';
    }
    
    if (file_exists($file_path)) {
        return false; // Pozwól serwerowi obsłużyć plik
    }
}

// Przekieruj główny katalog do admin
if ($request_uri === '/' || $request_uri === '') {
    $_SERVER['REQUEST_URI'] = '/admin/';
    include __DIR__ . '/admin/index.php';
    return true;
}

// Dla innych żądań, sprawdź czy plik istnieje
$file_path = __DIR__ . $request_uri;
if (file_exists($file_path) && is_file($file_path)) {
    return false; // Pozwól serwerowi obsłużyć plik
}

// Dla PWA
if (strpos($request_uri, '/pwa/') === 0) {
    $file_path = __DIR__ . $request_uri;
    if (file_exists($file_path) && is_file($file_path)) {
        return false; // Pozwól serwerowi obsłużyć plik
    }
}

// Dla uploads
if (strpos($request_uri, '/uploads/') === 0) {
    $file_path = __DIR__ . $request_uri;
    if (file_exists($file_path) && is_file($file_path)) {
        return false; // Pozwól serwerowi obsłużyć plik
    }
}

// Dla database (tylko odczyt)
if (strpos($request_uri, '/database/') === 0) {
    $file_path = __DIR__ . $request_uri;
    if (file_exists($file_path) && is_file($file_path)) {
        return false; // Pozwól serwerowi obsłużyć plik
    }
}

// Domyślnie przekieruj do admin
$_SERVER['REQUEST_URI'] = '/admin/';
include __DIR__ . '/admin/index.php';
return true;
?> 