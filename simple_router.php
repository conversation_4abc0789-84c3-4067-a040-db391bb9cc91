<?php
// Uproszczony router dla wbudowanego serwera PHP
$request_uri = $_SERVER['REQUEST_URI'];

// Przekieruj wszystko do admin/index.php
if (strpos($request_uri, '/admin/') === 0 || $request_uri === '/admin' || $request_uri === '/') {
    $_SERVER['REQUEST_URI'] = '/admin/';
    include __DIR__ . '/admin/index.php';
    return true;
}

// Dla plików statycznych (PWA, uploads, database)
if (strpos($request_uri, '/pwa/') === 0 || 
    strpos($request_uri, '/uploads/') === 0 || 
    strpos($request_uri, '/database/') === 0) {
    return false; // Pozwól serwerowi obsłużyć plik
}

// Domyślnie przekieruj do admin
$_SERVER['REQUEST_URI'] = '/admin/';
include __DIR__ . '/admin/index.php';
return true;
?> 