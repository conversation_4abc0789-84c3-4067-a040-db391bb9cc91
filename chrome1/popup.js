// Popup script dla <PERSON>ni - Automatyczna Synchronizacja
document.addEventListener('DOMContentLoaded', function () {
    console.log('KtoOstatni: Popup załadowany');

    // Elementy UI
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    const toggleSwitch = document.getElementById('toggleSwitch');
    const syncCodeInput = document.getElementById('syncCode');
    const syncIntervalSelect = document.getElementById('syncInterval');
    const manualSyncButton = document.getElementById('manualSyncButton');
    const pauseButton = document.getElementById('pauseButton');
    const viewDataButton = document.getElementById('viewDataButton');
    const downloadDataButton = document.getElementById('downloadDataButton');
    const lastSyncElement = document.getElementById('lastSync');
    const statusElement = document.getElementById('status');
    const progressBar = document.getElementById('progressBar');
    const loadingElement = document.getElementById('loading');

    let currentConfig = {};

    // Inicjalizacja
    init();

    async function init() {
        try {
            console.log('KtoOstatni: Inicjalizacja popup...');
            await loadConfig();
            setupEventListeners();
            updateUI();
            console.log('KtoOstatni: Popup zainicjalizowany pomyślnie');
        } catch (error) {
            console.error('KtoOstatni: Błąd inicjalizacji popup:', error);
            updateStatus('Błąd inicjalizacji: ' + error.message);
        }
    }

    // Załaduj konfigurację z background script
    async function loadConfig() {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({ action: 'getConfig' }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else if (response.success) {
                    currentConfig = response.config;
                    resolve();
                } else {
                    reject(new Error(response.error || 'Nieznany błąd'));
                }
            });
        });
    }

    // Zapisz konfigurację
    async function saveConfig() {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({
                action: 'saveConfig',
                config: currentConfig
            }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else if (response.success) {
                    resolve();
                } else {
                    reject(new Error(response.error || 'Nieznany błąd'));
                }
            });
        });
    }

    // Konfiguracja event listenerów
    function setupEventListeners() {
        // Toggle switch
        toggleSwitch.addEventListener('click', async () => {
            currentConfig.isEnabled = !currentConfig.isEnabled;
            currentConfig.autoSync = currentConfig.isEnabled;
            await saveConfig();
            updateUI();
        });

        // Kod synchronizacji
        syncCodeInput.addEventListener('change', async () => {
            currentConfig.syncCode = syncCodeInput.value.trim();
            await saveConfig();
        });

        // Interwał synchronizacji
        syncIntervalSelect.addEventListener('change', async () => {
            currentConfig.syncInterval = parseInt(syncIntervalSelect.value);
            await saveConfig();
        });

        // Ręczna synchronizacja
        manualSyncButton.addEventListener('click', async () => {
            await performManualSync();
        });

        // Pauza
        pauseButton.addEventListener('click', async () => {
            currentConfig.isEnabled = false;
            currentConfig.autoSync = false;
            await saveConfig();
            updateUI();
        });

        // Podgląd danych
        viewDataButton.addEventListener('click', async () => {
            await viewLastSyncData();
        });

        // Pobierz JSON
        downloadDataButton.addEventListener('click', async () => {
            await downloadLastSyncData();
        });

        // Nasłuchuj na wiadomości z background script
        chrome.runtime.onMessage.addListener((message) => {
            if (message.action === 'syncCompleted') {
                updateStatus('Synchronizacja zakończona pomyślnie');
                loadConfig().then(() => updateUI());
            }
        });
    }

    // Aktualizuj interfejs użytkownika
    function updateUI() {
        // Status
        if (currentConfig.isEnabled && currentConfig.syncCode) {
            statusDot.classList.add('active');
            statusText.textContent = 'Aktywny';
            toggleSwitch.classList.add('active');
        } else {
            statusDot.classList.remove('active');
            statusText.textContent = 'Wyłączony';
            toggleSwitch.classList.remove('active');
        }

        // Pola formularza
        syncCodeInput.value = currentConfig.syncCode || '';
        syncIntervalSelect.value = currentConfig.syncInterval || 15;

        // Ostatnia synchronizacja
        if (currentConfig.lastSync) {
            const lastSyncDate = new Date(currentConfig.lastSync);
            lastSyncElement.textContent = `Ostatnia synchronizacja: ${lastSyncDate.toLocaleString('pl-PL')}`;
        } else {
            lastSyncElement.textContent = 'Ostatnia synchronizacja: nigdy';
        }

        // Przyciski
        const hasValidConfig = currentConfig.syncCode && currentConfig.syncCode.length > 0;
        manualSyncButton.disabled = !hasValidConfig;
        pauseButton.disabled = !currentConfig.isEnabled;
    }

    // Wykonaj ręczną synchronizację
    async function performManualSync() {
        try {
            setButtonsEnabled(false);
            showLoading(true);
            updateStatus('Rozpoczynam ręczną synchronizację...');
            updateProgress(50);

            const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({ action: 'manualSync' }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });

            if (response.success) {
                updateStatus('Ręczna synchronizacja zakończona pomyślnie');
                updateProgress(100);
                await loadConfig();
                updateUI();
            } else {
                updateStatus('Błąd ręcznej synchronizacji: ' + response.error);
            }

        } catch (error) {
            console.error('KtoOstatni: Błąd ręcznej synchronizacji:', error);
            updateStatus('Błąd: ' + error.message);
        } finally {
            setButtonsEnabled(true);
            showLoading(false);
            setTimeout(() => updateProgress(0), 2000);
        }
    }

    // Podgląd ostatnich danych synchronizacji
    async function viewLastSyncData() {
        try {
            updateStatus('Pobieranie danych do podglądu...');

            // Sprawdź czy jesteśmy na stronie iGabinet
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab.url.includes('igabinet.pl') && !tab.url.includes('test_page.html')) {
                updateStatus('Przejdź na stronę iGabinet lub test_page.html');
                return;
            }

            // Najpierw sprawdź czy content script jest załadowany
            try {
                const pingResponse = await new Promise((resolve, reject) => {
                    chrome.tabs.sendMessage(tab.id, { action: 'ping' }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });

                if (!pingResponse || !pingResponse.success) {
                    updateStatus('Content script nie jest załadowany na tej stronie');
                    return;
                }

                console.log('KtoOstatni: Content script ping OK:', pingResponse);
            } catch (pingError) {
                updateStatus('Content script nie odpowiada: ' + pingError.message);
                return;
            }

            // Pobierz dane z content script
            const response = await new Promise((resolve, reject) => {
                chrome.tabs.sendMessage(tab.id, { action: 'extractScheduleData' }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });

            if (response && response.success && response.data) {
                // Otwórz nowe okno z podglądem JSON
                const jsonWindow = window.open('', '_blank', 'width=800,height=600');
                jsonWindow.document.write(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>KtoOstatni - Podgląd danych</title>
                        <style>
                            body { font-family: monospace; margin: 20px; background: #1e1e1e; color: #d4d4d4; }
                            pre { background: #2d2d2d; padding: 20px; border-radius: 8px; overflow: auto; }
                            .header { background: #007acc; color: white; padding: 15px; margin: -20px -20px 20px -20px; }
                        </style>
                    </head>
                    <body>
                        <div class="header">
                            <h2>🚀 KtoOstatni - Podgląd danych synchronizacji</h2>
                            <p>Dane wyciągnięte z harmonogramu iGabinet</p>
                        </div>
                        <pre>${JSON.stringify(response.data, null, 2)}</pre>
                    </body>
                    </html>
                `);
                updateStatus('Podgląd danych otwarty w nowym oknie');
            } else {
                updateStatus('Błąd pobierania danych: ' + (response?.error || 'Nieznany błąd'));
            }

        } catch (error) {
            console.error('KtoOstatni: Błąd podglądu danych:', error);
            updateStatus('Błąd: ' + error.message);
        }
    }

    // Pobierz ostatnie dane jako JSON
    async function downloadLastSyncData() {
        try {
            updateStatus('Przygotowywanie pliku JSON...');

            // Sprawdź czy jesteśmy na stronie iGabinet
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab.url.includes('igabinet.pl') && !tab.url.includes('test_page.html')) {
                updateStatus('Przejdź na stronę iGabinet lub test_page.html');
                return;
            }

            // Najpierw sprawdź czy content script jest załadowany
            try {
                const pingResponse = await new Promise((resolve, reject) => {
                    chrome.tabs.sendMessage(tab.id, { action: 'ping' }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });

                if (!pingResponse || !pingResponse.success) {
                    updateStatus('Content script nie jest załadowany na tej stronie');
                    return;
                }

                console.log('KtoOstatni: Content script ping OK:', pingResponse);
            } catch (pingError) {
                updateStatus('Content script nie odpowiada: ' + pingError.message);
                return;
            }

            // Pobierz dane z content script
            const response = await new Promise((resolve, reject) => {
                chrome.tabs.sendMessage(tab.id, { action: 'extractScheduleData' }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });

            if (response && response.success && response.data) {
                // Wyślij do background script do pobrania
                chrome.runtime.sendMessage({
                    action: 'downloadData',
                    data: response.data
                }, (downloadResponse) => {
                    if (downloadResponse && downloadResponse.success) {
                        updateStatus('Plik JSON został pobrany pomyślnie');
                    } else {
                        updateStatus('Błąd pobierania pliku: ' + (downloadResponse?.error || 'Nieznany błąd'));
                    }
                });
            } else {
                updateStatus('Błąd pobierania danych: ' + (response?.error || 'Nieznany błąd'));
            }

        } catch (error) {
            console.error('KtoOstatni: Błąd pobierania JSON:', error);
            updateStatus('Błąd: ' + error.message);
        }
    }

    // Funkcje pomocnicze
    function updateStatus(message) {
        statusElement.textContent = message;
        console.log('KtoOstatni Status:', message);
    }

    function updateProgress(percent) {
        progressBar.style.width = percent + '%';
    }

    function showLoading(show) {
        loadingElement.style.display = show ? 'block' : 'none';
    }

    function setButtonsEnabled(enabled) {
        manualSyncButton.disabled = !enabled;
        viewDataButton.disabled = !enabled;
        downloadDataButton.disabled = !enabled;
    }
});
