# 🚀 KtoOstatni - Automatyczna Synchronizacja v2.1

## 📋 Wymagania
- Google Chrome (wersja 88 lub nowsza)
- Lokalny serwer PHP działający na `localhost:8080`
- Kod synchronizacji otrzymany od administratora systemu KtoOstatni

## 🔧 Instalacja rozszerzenia

### Krok 1: Otwórz zarządzanie rozszerzeniami
1. Otwórz Google Chrome
2. Wpisz w pasku adresu: `chrome://extensions/`
3. Naciśnij Enter

### Krok 2: Włącz tryb dewelopera
1. W prawym górnym rogu znajdź przełącznik "Tryb dewelopera"
2. <PERSON><PERSON><PERSON><PERSON>, aby go włączyć

### Krok 3: Załaduj rozszerzenie
1. <PERSON>lik<PERSON>j przycisk "Załaduj rozpakowane"
2. <PERSON><PERSON><PERSON><PERSON> katalog `chrome1/` z tego projektu
3. <PERSON><PERSON><PERSON><PERSON> "Wybierz folder"

### Krok 4: Sprawdź instalację
1. Rozszerzenie powinno pojawić się na liście
2. Sprawdź czy nie ma błędów (czerwone ikony)
3. Kliknij ikonę rozszerzenia w pasku narzędzi Chrome

## ⚙️ Konfiguracja automatycznej synchronizacji

### Krok 1: Otwórz ustawienia rozszerzenia
1. Kliknij prawym przyciskiem na ikonę rozszerzenia
2. Wybierz **"Opcje"** lub przejdź do `chrome://extensions/`
3. Znajdź rozszerzenie i kliknij **"Szczegóły"**
4. Kliknij **"Opcje rozszerzenia"**

### Krok 2: Skonfiguruj automatyczną synchronizację
1. **Kod synchronizacji:** Wprowadź kod otrzymany od administratora
2. **Interwał synchronizacji:** Wybierz częstotliwość (domyślnie: co 15 minut)
3. **Włącz automatyczną synchronizację:** Zaznacz checkbox
4. **Dodatek aktywny:** Zaznacz checkbox
5. Kliknij **"Zapisz ustawienia"**

### Krok 3: Aktywuj synchronizację w popup
1. Kliknij ikonę rozszerzenia w pasku narzędzi
2. Wprowadź kod synchronizacji (jeśli nie został zapisany)
3. Kliknij przełącznik **ON/OFF** aby aktywować
4. Status powinien zmienić się na **"Aktywny"**

## 🧪 Testowanie rozszerzenia

### Test 1: Test na stronie testowej
1. Otwórz plik `chrome1/test_page.html` w przeglądarce
2. Otwórz rozszerzenie (kliknij ikonę w pasku narzędzi)
3. Wprowadź kod synchronizacji: `igab000000000001`
4. Kliknij "🚀 Synchronizuj wizyty"
5. Sprawdź czy pojawia się komunikat o sukcesie
6. Powinno wyciągnąć 3 lekarzy i 4 wizyty

### Test 2: Test na prawdziwej stronie iGabinet
1. Przejdź na stronę iGabinet.pl i zaloguj się
2. Otwórz harmonogram wizyt
3. Otwórz rozszerzenie (kliknij ikonę w pasku narzędzi)
4. Wprowadź kod synchronizacji: `igab000000000001`
5. Kliknij "🚀 Synchronizuj wizyty"
6. Sprawdź czy pojawia się komunikat o sukcesie

### Test 3: Sprawdzenie importu
1. Przejdź do: `http://localhost:8080/admin/test_chrome_extension.html`
2. Kliknij "🧪 Przetestuj Import API"
3. Sprawdź wyniki importu - powinny być widoczne dane pacjentek z PESEL, telefonem i emailem

### Test 4: Panel administracyjny
1. Przejdź do: `http://localhost:8080/admin/client/import`
2. Sprawdź czy widzisz ustawienia importu
3. Sprawdź mapowania lekarzy

## 🔍 Rozwiązywanie problemów

### Problem: Rozszerzenie nie ładuje się
**Rozwiązanie:**
- Sprawdź czy wszystkie pliki są w katalogu `chrome1/`
- Sprawdź konsolę błędów w `chrome://extensions/`
- Upewnij się, że tryb dewelopera jest włączony

### Problem: Błąd "Could not establish connection"
**Rozwiązanie:**
- Odśwież stronę w Chrome
- Przeładuj rozszerzenie (kliknij ikonę odświeżania)
- Sprawdź czy content script jest załadowany

### Problem: "Nie udało się wyciągnąć danych z harmonogramu"
**Rozwiązanie:**
- Sprawdź czy jesteś na stronie harmonogramu w iGabinet
- Otwórz konsolę deweloperską (F12) i sprawdź logi
- Przetestuj na stronie testowej: `chrome1/test_page.html`
- Sprawdź czy content script jest załadowany w zakładce Extensions

## 🚀 Nowe funkcje w wersji 2.1

### ⏰ Automatyczna synchronizacja w tle
- **Interwały:** Od 1 minuty do 4 godzin
- **Działanie w tle:** Synchronizacja nawet gdy popup jest zamknięty
- **Wymaga:** Otwartą kartę z iGabinet w przeglądarce

### 🎮 Ulepszone sterowanie
- **Toggle ON/OFF:** Szybkie włączanie/wyłączanie
- **Status w czasie rzeczywistym:** Aktywny/Wyłączony
- **Ręczna synchronizacja:** Przycisk "Synchronizuj teraz"
- **Pauza:** Tymczasowe zatrzymanie automatycznej synchronizacji

### 🛠️ Narzędzia deweloperskie
- **Podgląd danych:** Okno z JSON w czasie rzeczywistym
- **Pobierz JSON:** Zapisz dane synchronizacji do pliku
- **Logi szczegółowe:** Debugowanie w konsoli deweloperskiej

### ⚙️ Strona ustawień
- **Pełna konfiguracja:** Dostępna przez `chrome://extensions/`
- **Statystyki:** Ostatnia synchronizacja, licznik, następna synchronizacja
- **Test synchronizacji:** Sprawdź połączenie z systemem
- **Reset ustawień:** Przywróć domyślne wartości

### 🔄 Rzeczywiste ID lekarzy
- **Pobieranie z API iGabinet:** Automatyczne mapowanie ID
- **Format:** `igab_10`, `igab_105` zamiast `igab_doctor_1`
- **Inteligentne dopasowanie:** Mapowanie po nazwisku i imieniu

### Problem: Timeout podczas importu
**Rozwiązanie:**
- Sprawdź czy serwer działa na `localhost:8080`
- Sprawdź czy endpoint `/admin/api/import` odpowiada
- Zwiększ timeout w kodzie rozszerzenia

### Problem: Błąd 401 - Nieprawidłowy kod synchronizacji
**Rozwiązanie:**
- Sprawdź czy kod `igab000000000001` jest poprawny
- Sprawdź bazę danych: `SELECT * FROM import_settings`
- Utwórz nowe ustawienie importu w panelu admin

### Problem: Lekarze nie są mapowani
**Rozwiązanie:**
- Przejdź do mapowań lekarzy w panelu admin
- Zmapuj ręcznie lekarzy z Chrome na lekarzy w systemie
- Użyj funkcji automatycznego mapowania

## 📊 Sprawdzanie wyników

### Baza danych
```sql
-- Sprawdź zaimportowane wizyty
SELECT * FROM queue_appointments WHERE external_id LIKE 'test_%';

-- Sprawdź mapowania lekarzy
SELECT * FROM external_doctor_mappings;

-- Sprawdź logi synchronizacji
SELECT * FROM sync_logs ORDER BY started_at DESC;
```

### Panel administracyjny
- **Ustawienia importu:** `/admin/client/import`
- **Mapowania lekarzy:** `/admin/client/import/doctor-mappings/1`
- **System kolejkowy:** `/admin/client/queue`
- **Test API:** `/admin/test_chrome_extension.html`

## 🎯 Oczekiwane rezultaty

Po udanej synchronizacji powinieneś zobaczyć:
1. ✅ Komunikat o sukcesie w rozszerzeniu
2. ✅ Nowe wizyty w tabeli `queue_appointments` z danymi pacjentek:
   - ID lekarza z iGabinet
   - Imię i nazwisko lekarza
   - Data umówionej wizyty
   - Imię i nazwisko pacjentki
   - PESEL pacjentki
   - Telefon pacjentki
   - Email pacjentki
3. ✅ Mapowania lekarzy w `external_doctor_mappings`
4. ✅ Logi w `sync_logs`
5. ✅ Statystyki w panelu administracyjnym

## 🔄 Aktualizacja rozszerzenia

Jeśli wprowadzisz zmiany w kodzie rozszerzenia:
1. Przejdź do `chrome://extensions/`
2. Znajdź swoje rozszerzenie
3. Kliknij ikonę odświeżania (🔄)
4. Przeładuj strony, na których używasz rozszerzenia

## 📞 Wsparcie

Jeśli masz problemy:
1. Sprawdź konsolę błędów w Chrome (F12)
2. Sprawdź logi serwera PHP
3. Przetestuj API bezpośrednio: `/admin/test_import_endpoint.php?sync_code=igab000000000001`
4. Sprawdź konfigurację bazy danych: `/admin/test_import_api.php`

---

**Powodzenia z testowaniem! 🚀**
