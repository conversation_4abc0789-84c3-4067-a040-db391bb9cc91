// Background script dla <PERSON>tatni - Automatyczna Synchronizacja
console.log('KtoOstatni: Background script zała<PERSON>wan<PERSON>');

// Konfiguracja domyślna
const DEFAULT_CONFIG = {
    syncInterval: 15, // minuty
    isEnabled: false,
    syncCode: '',
    lastSync: null,
    autoSync: false
};

// Inicjalizacja przy starcie
chrome.runtime.onStartup.addListener(async () => {
    console.log('KtoOstatni: Chrome startup - inicjalizacja');
    await initializeExtension();
});

chrome.runtime.onInstalled.addListener(async () => {
    console.log('KtoOstatni: Extension installed/updated - inicjalizacja');
    await initializeExtension();
});

// Inicjalizacja rozszerzenia
async function initializeExtension() {
    try {
        const config = await getConfig();
        console.log('KtoOstatni: Aktualna konfiguracja:', config);

        if (config.autoSync && config.isEnabled && config.syncCode) {
            await setupAutoSync(config.syncInterval);
        }
    } catch (error) {
        console.error('KtoOstatni: Błąd inicjalizacji:', error);
    }
}

// Pobierz konfigurację z storage
async function getConfig() {
    const result = await chrome.storage.local.get(DEFAULT_CONFIG);
    return { ...DEFAULT_CONFIG, ...result };
}

// Zapisz konfigurację
async function saveConfig(config) {
    await chrome.storage.local.set(config);
    console.log('KtoOstatni: Konfiguracja zapisana:', config);
}

// Konfiguracja automatycznej synchronizacji
async function setupAutoSync(intervalMinutes) {
    try {
        // Wyczyść istniejące alarmy
        await chrome.alarms.clear('autoSync');

        if (intervalMinutes > 0) {
            // Utwórz nowy alarm
            await chrome.alarms.create('autoSync', {
                delayInMinutes: intervalMinutes,
                periodInMinutes: intervalMinutes
            });
            console.log(`KtoOstatni: Auto-sync skonfigurowany na ${intervalMinutes} minut`);
        }
    } catch (error) {
        console.error('KtoOstatni: Błąd konfiguracji auto-sync:', error);
    }
}

// Obsługa alarmów
chrome.alarms.onAlarm.addListener(async (alarm) => {
    if (alarm.name === 'autoSync') {
        console.log('KtoOstatni: Uruchamiam automatyczną synchronizację');
        await performAutoSync();
    }
});

// Wykonaj automatyczną synchronizację
async function performAutoSync() {
    try {
        const config = await getConfig();

        if (!config.isEnabled || !config.syncCode) {
            console.log('KtoOstatni: Auto-sync wyłączony lub brak kodu synchronizacji');
            return;
        }

        console.log('KtoOstatni: Rozpoczynam automatyczną synchronizację...');

        // Znajdź kartę z iGabinet
        const tabs = await chrome.tabs.query({ url: '*://*.igabinet.pl/*' });

        if (tabs.length === 0) {
            console.log('KtoOstatni: Brak otwartych kart iGabinet - pomijam synchronizację');
            return;
        }

        const tab = tabs[0];
        console.log('KtoOstatni: Znaleziono kartę iGabinet:', tab.url);

        // Wyślij żądanie do content script
        const response = await new Promise((resolve, reject) => {
            chrome.tabs.sendMessage(tab.id, { action: 'extractScheduleData' }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });

        if (response && response.success && response.data) {
            // Wyślij dane do systemu KtoOstatni
            const syncResult = await sendDataToSystem(config.syncCode, response.data);

            if (syncResult.success) {
                // Zapisz czas ostatniej synchronizacji
                await saveConfig({
                    ...config,
                    lastSync: new Date().toISOString()
                });

                console.log('KtoOstatni: Automatyczna synchronizacja zakończona pomyślnie');

                // Wyślij notyfikację do popup (jeśli jest otwarty)
                chrome.runtime.sendMessage({
                    action: 'syncCompleted',
                    success: true,
                    data: syncResult
                }).catch(() => { }); // Ignoruj błąd jeśli popup nie jest otwarty

            } else {
                console.error('KtoOstatni: Błąd synchronizacji:', syncResult.error);
            }
        } else {
            console.error('KtoOstatni: Błąd wyciągania danych:', response?.error || 'Brak odpowiedzi od content script');
        }

    } catch (error) {
        console.error('KtoOstatni: Błąd automatycznej synchronizacji:', error);
    }
}

// Wyślij dane do systemu KtoOstatni
async function sendDataToSystem(syncCode, scheduleData) {
    try {
        console.log('KtoOstatni: Wysyłanie danych do systemu, kod synchronizacji:', syncCode);
        console.log('KtoOstatni: Dane do wysłania:', scheduleData);

        // Upewnij się, że dane mają ustawiony kod synchronizacji
        if (!scheduleData.syncCode) {
            scheduleData.syncCode = syncCode;
        }

        console.log('KtoOstatni: Dane do wysłania:', scheduleData);

        // Dodaj szczegółowe logowanie
        if (scheduleData.syncData && scheduleData.syncData.days) {
            let totalDoctors = 0;
            let totalAppointments = 0;

            scheduleData.syncData.days.forEach(day => {
                if (day.doctors && Array.isArray(day.doctors)) {
                    totalDoctors += day.doctors.length;

                    day.doctors.forEach((doctor, index) => {
                        const appointmentsCount = doctor.appointments ? doctor.appointments.length : 0;
                        totalAppointments += appointmentsCount;

                        console.log(`KtoOstatni: Dzień ${day.date}, Lekarz ${index + 1}:`, {
                            id: doctor.doctorId,
                            name: doctor.doctorName,
                            appointments: appointmentsCount
                        });

                        // Loguj pierwsze 3 wizyty dla tego lekarza (jeśli istnieją)
                        if (doctor.appointments && doctor.appointments.length > 0) {
                            console.log(`KtoOstatni: Przykładowe wizyty dla lekarza ${doctor.doctorName}:`);
                            doctor.appointments.slice(0, 3).forEach((appointment, i) => {
                                console.log(`KtoOstatni: Wizyta ${i + 1}:`, {
                                    id: appointment.appointmentId,
                                    patient: `${appointment.patientFirstName} ${appointment.patientLastName}`,
                                    time: `${appointment.appointmentStart} - ${appointment.appointmentEnd}`,
                                    duration: appointment.appointmentDuration
                                });
                            });
                        }
                    });
                }
            });

            console.log('KtoOstatni: Łączna liczba lekarzy:', totalDoctors);
            console.log('KtoOstatni: Łączna liczba wizyt:', totalAppointments);
        }

        const response = await fetch('http://localhost:8080/admin/api/import', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Extension-Version': '2.1.0'
            },
            body: JSON.stringify(scheduleData)
        });

        if (!response.ok) {
            const errorText = await response.text().catch(() => '');
            console.error(`KtoOstatni: Błąd HTTP ${response.status} podczas wysyłania danych:`, errorText);
            throw new Error(`HTTP error! status: ${response.status}, details: ${errorText}`);
        }

        const result = await response.json();
        console.log('KtoOstatni: Odpowiedź z API:', result);
        return { success: true, data: result };

    } catch (error) {
        console.error('KtoOstatni: Błąd wysyłania danych:', error);
        return { success: false, error: error.message };
    }
}

// Nasłuchuj na wiadomości z popup i content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('KtoOstatni: Background otrzymał wiadomość:', request);

    if (request.action === 'contentScriptReady') {
        console.log('KtoOstatni: Content script gotowy na:', request.url);
        sendResponse({ success: true, message: 'Background script otrzymał sygnał' });
        return true;
    }

    if (request.action === 'getConfig') {
        getConfig().then(config => {
            sendResponse({ success: true, config: config });
        }).catch(error => {
            sendResponse({ success: false, error: error.message });
        });
        return true;
    }

    if (request.action === 'saveConfig') {
        saveConfig(request.config).then(async () => {
            // Zaktualizuj auto-sync
            if (request.config.autoSync && request.config.isEnabled) {
                await setupAutoSync(request.config.syncInterval);
            } else {
                await chrome.alarms.clear('autoSync');
            }
            sendResponse({ success: true });
        }).catch(error => {
            sendResponse({ success: false, error: error.message });
        });
        return true;
    }

    if (request.action === 'manualSync') {
        performAutoSync().then(() => {
            sendResponse({ success: true });
        }).catch(error => {
            sendResponse({ success: false, error: error.message });
        });
        return true;
    }

    if (request.action === 'downloadData') {
        // Pobierz dane jako plik JSON
        const jsonData = JSON.stringify(request.data, null, 2);
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const filename = `ktoostatni_sync_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;

        chrome.downloads.download({
            url: url,
            filename: filename,
            saveAs: true
        }, (downloadId) => {
            if (chrome.runtime.lastError) {
                console.error('KtoOstatni: Błąd podczas pobierania:', chrome.runtime.lastError);
                sendResponse({ success: false, error: chrome.runtime.lastError.message });
            } else {
                console.log('KtoOstatni: Plik został pobrany z ID:', downloadId);
                sendResponse({ success: true, downloadId: downloadId });
            }

            // Zwolnij URL
            URL.revokeObjectURL(url);
        });

        return true;
    }
});
