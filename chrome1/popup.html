<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KtoOstatni - Automatyczna Synchronizacja</title>
    <style>
        body {
            width: 380px;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .header {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }

        .container {
            padding: 20px;
        }

        .status-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 12px 15px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ff4757;
        }

        .status-dot.active {
            background: #2ed573;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .toggle-switch.active {
            background: #2ed573;
        }

        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(26px);
        }

        .section {
            margin-bottom: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .section h3 {
            margin: 0 0 12px 0;
            font-size: 13px;
            font-weight: 500;
            opacity: 0.9;
        }

        .input-group {
            margin: 10px 0;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 11px;
            opacity: 0.9;
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 12px;
            box-sizing: border-box;
        }

        .input-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #4facfe;
            background: rgba(255, 255, 255, 0.15);
        }

        .input-group select option {
            background: #333;
            color: white;
        }

        .button-group {
            display: flex;
            gap: 8px;
            margin: 12px 0;
        }

        button {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 11px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        button.primary {
            background: #4facfe;
            border-color: #4facfe;
        }

        button.primary:hover {
            background: #3d8bfe;
        }

        button.danger {
            background: #ff4757;
            border-color: #ff4757;
        }

        button.danger:hover {
            background: #ff3742;
        }

        .last-sync {
            font-size: 10px;
            opacity: 0.7;
            text-align: center;
            margin-top: 8px;
        }

        .status {
            text-align: center;
            margin: 10px 0;
            padding: 8px;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            font-size: 11px;
            min-height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .progress-container {
            margin: 10px 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            overflow: hidden;
            height: 6px;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 8px;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 10px 0;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .footer {
            background: rgba(0, 0, 0, 0.2);
            padding: 10px 20px;
            text-align: center;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 10px;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 KtoOstatni - Auto Sync</h1>
    </div>

    <div class="container">
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">Wyłączony</span>
            </div>
            <div class="toggle-switch" id="toggleSwitch">
                <div class="toggle-slider"></div>
            </div>
        </div>

        <!-- Konfiguracja -->
        <div class="section">
            <h3>⚙️ Konfiguracja</h3>
            <div class="input-group">
                <label for="syncCode">Kod synchronizacji:</label>
                <input type="text" id="syncCode" placeholder="Wprowadź kod synchronizacji">
            </div>
            <div class="input-group">
                <label for="syncInterval">Interwał synchronizacji:</label>
                <select id="syncInterval">
                    <option value="5">Co 5 minut</option>
                    <option value="10">Co 10 minut</option>
                    <option value="15" selected>Co 15 minut</option>
                    <option value="30">Co 30 minut</option>
                    <option value="60">Co godzinę</option>
                </select>
            </div>
        </div>

        <!-- Kontrola -->
        <div class="section">
            <h3>🎮 Kontrola</h3>
            <div class="button-group">
                <button id="manualSyncButton" class="primary">🔄 Synchronizuj teraz</button>
                <button id="pauseButton" class="danger">⏸️ Pauza</button>
            </div>
            <div class="last-sync" id="lastSync">Ostatnia synchronizacja: nigdy</div>
        </div>

        <!-- Narzędzia deweloperskie -->
        <div class="section">
            <h3>🛠️ Narzędzia deweloperskie</h3>
            <div class="button-group">
                <button id="viewDataButton">📊 Podgląd danych</button>
                <button id="downloadDataButton">💾 Pobierz JSON</button>
            </div>
        </div>

        <div class="loading" id="loading"></div>
        
        <div class="progress-container">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div class="status" id="status">Gotowy do pracy</div>
    </div>

    <div class="footer">
        v2.1.0 | Automatyczna synchronizacja w tle
    </div>

    <script src="popup.js"></script>
</body>
</html>
