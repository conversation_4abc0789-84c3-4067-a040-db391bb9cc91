# KtoOstatni - Dodatek Chrome

Dodatek do Chrome do synchronizacji wizyt z iGabinet do systemu KtoOstatni.

**Wersja**: 2.0.0
**Status**: ✅ Gotowy do użycia

## Funkcje

### 🚀 Główne funkcje
- **Synchronizacja wizyt** - Automatyczne wyciąganie wizyt z harmonogramu iGabinet
- **<PERSON> pacjentek** - Pobieranie PESEL, telefonu i emaila pacjentek
- **Mapowanie lekarzy** - Automatyczne przypisywanie lekarzy z iGabinet do systemu KtoOstatni
- **Bezpieczna transmisja** - Szyfrowane połączenie z kodem synchronizacji

### 📋 Wyciągane dane
- **ID lekarza** nadane przez iGabinet
- **Imię i nazwisko lekarza**
- **Data umówionej wizyty**
- **Imię i nazwisko pacjentki**
- **PESEL pacjentki**
- **Telefon pacjentki**
- **Email pacje<PERSON>**

### 🔧 Funkcje deweloperskie
- 📊 Pobieranie wszystkich danych z API (debug)
- 🏥 Pobieranie produktów (debug)
- 🏢 Pobieranie placówek (debug)
- 📝 Pobieranie notatek (debug)
- 👥 Pobieranie encji (debug)

## Instalacja

1. Otwórz Chrome i przejdź do `chrome://extensions/`
2. Włącz "Tryb programisty" (Developer mode) w prawym górnym rogu
3. Kliknij "Załaduj rozpakowane" (Load unpacked)
4. Wybierz folder `chrome1` z tego projektu
5. Dodatek zostanie zainstalowany i pojawi się w pasku narzędzi

## Użycie

1. Przejdź na stronę `https://sonokard.igabinet.pl`
2. Zaloguj się do systemu (jeśli wymagane)
3. Kliknij ikonę dodatku w pasku narzędzi Chrome
4. Wybierz opcję:
   - **Pobierz wszystkie dane** - wykonuje wszystkie zapytania API
   - **Pobierz produkty** - tylko getAvailableProducts
   - **Pobierz placówki** - tylko getFacilitiesAndOffices
   - **Pobierz notatki** - tylko getNotes
   - **Pobierz encje** - tylko getEntities

5. Poczekaj na zakończenie operacji
6. Plik JSON zostanie automatycznie pobrany

## Struktura pliku wyjściowego

```json
{
  "timestamp": "2025-01-06T10:30:00.000Z",
  "requests": {
    "getAvailableProducts": {
      "success": true,
      "data": { ... },
      "requestName": "getAvailableProducts"
    },
    "getFacilitiesAndOffices": {
      "success": true,
      "data": { ... },
      "requestName": "getFacilitiesAndOffices"
    }
  },
  "summary": {
    "totalRequests": 4,
    "successfulRequests": 4,
    "failedRequests": 0
  }
}
```

## Wykonywane zapytania API

### 1. getAvailableProducts
```javascript
fetch("https://sonokard.igabinet.pl/admin/request/work_schedule_request.php", {
  method: "POST",
  body: JSON.stringify({ "section": "getAvailableProducts" })
});
```

### 2. getFacilitiesAndOffices
```javascript
fetch("https://sonokard.igabinet.pl/admin/request/work_schedule_request.php", {
  method: "POST",
  body: JSON.stringify({ "section": "getFacilitiesAndOffices" })
});
```

### 3. getNotes
```javascript
fetch("https://sonokard.igabinet.pl/admin/request/work_schedule_request.php", {
  method: "POST",
  body: JSON.stringify({ "section": "getNotes" })
});
```

### 4. getEntities
```javascript
fetch("https://sonokard.igabinet.pl/admin/request/work_schedule_request.php", {
  method: "POST",
  body: JSON.stringify({
    "section": "getEntities",
    "start_date": "2025-08-06T11:02:00+02:00",
    "end_date": "2025-08-06T11:02:00+02:00",
    "products": [114, 129, 72, 20, 1748423446]
  })
});
```

## Wymagania

- Google Chrome (wersja 88+)
- Dostęp do internetu
- Zalogowanie do systemu igabinet.pl (dla niektórych zapytań)

## Rozwiązywanie problemów

### Błąd URL.createObjectURL
- **Problem**: "URL.createObjectURL is not a function"
- **Rozwiązanie**: Dodatek został naprawiony i używa teraz data URL zamiast blob URL
- **Status**: ✅ Naprawione w wersji 1.0.1

### Błąd CORS
- Upewnij się, że jesteś zalogowany do systemu igabinet.pl
- Sprawdź czy masz odpowiednie uprawnienia

### Błąd pobierania pliku
- Sprawdź czy Chrome ma uprawnienia do pobierania plików
- Sprawdź czy folder docelowy nie jest chroniony

### Zapytania nie działają
- Sprawdź czy jesteś na stronie igabinet.pl
- Sprawdź czy jesteś zalogowany
- Sprawdź konsolę deweloperską (F12) pod kątem błędów

### Timeout zapytań
- Zapytania mają timeout 30 sekund
- Jeśli zapytanie trwa zbyt długo, sprawdź połączenie internetowe
- Spróbuj ponownie później

### Duże pliki
- Dla plików większych niż 1MB automatycznie używana jest kompresja
- Nazwa pliku będzie zawierać "_compressed" dla skompresowanych plików

## Struktura projektu

```
chrome1/
├── manifest.json          # Konfiguracja dodatku
├── popup.html            # Interfejs użytkownika
├── popup.js              # Logika interfejsu
├── background.js         # Skrypt w tle
├── content.js            # Skrypt na stronie
└── README.md             # Dokumentacja
```

## Licencja

Ten projekt jest przeznaczony do użytku prywatnego.

## Historia wersji

### Wersja 1.0.1 (2025-01-06)
- ✅ Naprawiono błąd "URL.createObjectURL is not a function"
- ✅ Dodano obsługę dużych plików (>1MB) z automatyczną kompresją
- ✅ Dodano timeout 30 sekund dla zapytań API
- ✅ Dodano lepszą obsługę błędów CORS i sieciowych
- ✅ Dodano lepszą obsługę błędów komunikacji z background script
- ✅ Zaktualizowano dokumentację

### Wersja 1.0.0 (2025-01-06)
- 🎉 Pierwsza wersja dodatku
- 📊 Podstawowe funkcje pobierania danych z API
- 🎨 Nowoczesny interfejs użytkownika
- 📁 Automatyczne zapisywanie plików JSON 