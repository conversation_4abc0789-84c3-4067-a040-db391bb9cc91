# Format danych importu z iGabinet

## Wprowadzenie

Ten dokument opisuje format danych używany do importu harmonogramu wizyt z systemu iGabinet do systemu KtoOstatni. Format został zaprojektowany, aby był prosty, czytelny i zawierał tylko niezbędne informacje potrzebne do synchronizacji wizyt.

## Format JSON

Dane są przesyłane w formacie JSON o następującej strukturze:

```json
{
  "exportDate": "2025-08-26T10:00:16.938Z",
  "syncCode": "igab000000000001",
  "syncData": {
    "days": [
      {
        "date": "2025-06-26",
        "doctors": [
          {
            "doctorId": "43",
            "doctorName": "<PERSON>",
            "appointments": [
              {
                "appointmentId": "106689",
                "patientFirstName": "<PERSON>",
                "patientLastName": "Nowak",
                "appointmentStart": "09:00",
                "appointmentEnd": "09:20",
                "appointmentDuration": 20
              },
              {
                "appointmentId": "106690",
                "patientFirstName": "Jan",
                "patientLastName": "<PERSON><PERSON><PERSON>",
                "appointmentStart": "09:30",
                "appointmentEnd": "09:50",
                "appointmentDuration": 20
              }
            ]
          }
        ]
      }
    ]
  }
}
```

## Opis pól

### Główne pola

| Pole | Typ | Opis |
|------|-----|------|
| `exportDate` | String (ISO 8601) | Data i czas eksportu danych |
| `syncCode` | String | Unikalny kod synchronizacji przypisany do klienta |
| `syncData` | Object | Obiekt zawierający dane synchronizacji |

### Struktura `syncData`

| Pole | Typ | Opis |
|------|-----|------|
| `days` | Array | Tablica dni z harmonogramem wizyt |

### Struktura elementu tablicy `days`

| Pole | Typ | Opis |
|------|-----|------|
| `date` | String (YYYY-MM-DD) | Data w formacie ISO |
| `doctors` | Array | Tablica lekarzy pracujących w danym dniu |

### Struktura elementu tablicy `doctors`

| Pole | Typ | Opis |
|------|-----|------|
| `doctorId` | String | Identyfikator lekarza w systemie źródłowym |
| `doctorName` | String | Imię i nazwisko lekarza |
| `appointments` | Array | Tablica wizyt dla danego lekarza |

### Struktura elementu tablicy `appointments`

| Pole | Typ | Opis |
|------|-----|------|
| `appointmentId` | String | Identyfikator wizyty w systemie źródłowym |
| `patientFirstName` | String | Imię pacjenta |
| `patientLastName` | String | Nazwisko pacjenta |
| `appointmentStart` | String (HH:MM) | Godzina rozpoczęcia wizyty |
| `appointmentEnd` | String (HH:MM) | Godzina zakończenia wizyty |
| `appointmentDuration` | Number | Czas trwania wizyty w minutach |

## Uwagi

1. Wszystkie identyfikatory są przechowywane jako ciągi znaków, nawet jeśli są liczbami.
2. Daty są w formacie ISO 8601 (YYYY-MM-DD).
3. Godziny są w formacie 24-godzinnym (HH:MM).
4. Pole `appointmentEnd` może być obliczone na podstawie `appointmentStart` i `appointmentDuration`.
5. Format nie zawiera żadnych dodatkowych informacji oprócz tych wymienionych powyżej.

## Przykład użycia

Dodatek do przeglądarki Chrome automatycznie pobiera dane z iGabinet i konwertuje je do opisanego formatu. Dane są następnie przesyłane do systemu KtoOstatni za pomocą API.

## Kompatybilność wsteczna

System obsługuje również starszy format danych dla zachowania kompatybilności wstecznej. Jednak zaleca się korzystanie z nowego formatu, który jest bardziej przejrzysty i zawiera tylko niezbędne informacje.

Api iGabinetu:
Pobranie slownika lekarzy:
curl 'https://sonokard.igabinet.pl/admin/request/work_schedule_request.php' \
  -H 'accept: application/json' \
  -H 'accept-language: pl-PL,pl;q=0.9,en-US;q=0.8,en;q=0.7,cy;q=0.6,pt;q=0.5' \
  -H 'content-type: application/json' \
  -b 'system=2c4706f63916ec3375e3672c7e1f844f; res=0eec48a2009ba74cbfa13946821e5e97' \
  -H 'dnt: 1' \
  -H 'origin: https://sonokard.igabinet.pl' \
  -H 'priority: u=1, i' \
  -H 'referer: https://sonokard.igabinet.pl/admin/common_work_schedule.php' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36' \
  --data-raw '{"section":"getAvailableProducts"}'


  Pobranie lekarzy przyjmujacych w danym dniu:
  curl 'https://sonokard.igabinet.pl/admin/request/work_schedule_request.php' \
  -H 'accept: application/json' \
  -H 'accept-language: pl-PL,pl;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-type: application/json' \
  -b 'system=2c4706f63916ec3375e3672c7e1f844f; res=0eec48a2009ba74cbfa13946821e5e97' \
  -H 'dnt: 1' \
  -H 'origin: https://sonokard.igabinet.pl' \
  -H 'priority: u=1, i' \
  -H 'referer: https://sonokard.igabinet.pl/admin/common_work_schedule.php' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36' \
  --data-raw '{"section":"getWorkingProducts","date":"2025-08-26","facility_id":[1]}'


  Pobranie wizyt dla lekarzy pracujacych w danym dniu:
  curl 'https://sonokard.igabinet.pl/admin/request/work_schedule_request.php' \
  -H 'accept: application/json' \
  -H 'accept-language: pl-PL,pl;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-type: application/json' \
  -b 'system=2c4706f63916ec3375e3672c7e1f844f; res=0eec48a2009ba74cbfa13946821e5e97' \
  -H 'dnt: 1' \
  -H 'origin: https://sonokard.igabinet.pl' \
  -H 'priority: u=1, i' \
  -H 'referer: https://sonokard.igabinet.pl/admin/common_work_schedule.php' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36' \
  --data-raw '{"section":"getEntities","start_date":"2025-08-26","end_date":"2025-08-26","products":[43,10,20,83,1748423446]}'