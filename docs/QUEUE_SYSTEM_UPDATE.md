# Aktualizacja systemu kolejkowego - Edycja, usuwanie i zarządzanie datami wizyt

## Nowe funkcjonalności

Dodano możliwość edycji i usuwania wizyt w panelu klienta systemu kolejkowego oraz zarządzania wizytami na różne dni.

### Funkcjonalności edycji wizyt

1. **Edycja wizyty** - możliwość zmiany godziny wizyty, daty i danych pacjenta
2. **Usuwanie wizyty** - mo<PERSON>liwość usunięcia wizyty z systemu
3. **Lista wszystkich wizyt** - przegląd wszystkich wizyt na wybrany dzień
4. **Zarządzanie datami** - możliwość dodawania wizyt na przyszłe dni

### Dostępne akcje

#### W panelu lekarza (`/client/queue/appointments/{roomId}`):

- **<PERSON><PERSON><PERSON><PERSON><PERSON> daty** - formularz do wyboru daty wizyt
- **Przyciski edycji** - ikona ołówka przy każdej wizycie
- **Przyciski usuwania** - ikona kosza przy każdej wizycie (tylko dla wizyt nie zakończonych)
- **Lista wszystkich wizyt** - tabela z wszystkimi wizytami na wybrany dzień
- **Dodawanie wizyt na przyszłe dni** - formularz z wyborem daty

#### W formularzu edycji (`/client/queue/appointments/edit/{appointmentId}`):

- **Edycja daty wizyty** - pole date input
- **Edycja godziny wizyty** - pole time input
- **Edycja danych pacjenta** - pole tekstowe
- **Informacje o wizycie** - status, data utworzenia, ostatnia aktualizacja
- **Przycisk usuwania** - bezpośrednie usunięcie wizyty

### Bezpieczeństwo

- Sprawdzanie uprawnień - tylko właściciel sali może edytować/usuwać wizyty
- Potwierdzenie usuwania - okno dialogowe przed usunięciem
- Walidacja danych - sprawdzanie wymaganych pól
- Walidacja dat - nie można dodawać wizyt w przeszłości

### Routing

Dodano nowe ścieżki:
- `GET /client/queue/appointments/edit/{id}` - formularz edycji
- `POST /client/queue/appointments/update/{id}` - zapisanie zmian
- `GET /client/queue/appointments/delete/{id}` - usunięcie wizyty

### Model QueueSystem

Dodano/zmodyfikowano metody:
- `getAppointment($appointmentId)` - pobieranie wizyty po ID
- `updateAppointment($appointmentId, $appointmentTime, $patientName, $appointmentDate)` - aktualizacja wizyty z datą
- `deleteAppointment($appointmentId)` - usuwanie wizyty
- `getAllAppointments($roomId, $date)` - pobieranie wszystkich wizyt dla sali i daty
- `getWaitingAppointments($roomId, $limit, $date)` - pobieranie oczekujących wizyt dla daty
- `addAppointment($clientId, $roomId, $appointmentTime, $patientName, $appointmentDate)` - dodawanie wizyty z datą

### Kontroler QueueController

Dodano/zmodyfikowano metody:
- `editAppointment($appointmentId)` - wyświetlanie formularza edycji
- `updateAppointment($appointmentId)` - zapisywanie zmian z datą
- `deleteAppointment($appointmentId)` - usuwanie wizyty
- `doctorAppointments($roomId)` - obsługa parametru daty

### Pliki widoków

- `views/client/queue/edit_appointment.php` - zaktualizowany z polem daty
- `views/client/queue/doctor_appointments.php` - zaktualizowany z wyborem daty i przyciskami edycji/usuwania

## Użycie

1. Przejdź do panelu systemu kolejkowego klienta
2. Wybierz gabinet/lekarkę
3. W panelu lekarza:
   - Wybierz datę z formularza "Wybór daty"
   - Zobaczysz listę wszystkich wizyt z wybranego dnia
   - Użyj przycisków edycji (ołówek) i usuwania (kosz) przy każdej wizycie
4. Aby dodać nową wizytę:
   - Wypełnij formularz "Dodaj nową wizytę" z datą i godziną
   - Kliknij "Dodaj"
5. Aby edytować wizytę:
   - Kliknij ikonę ołówka przy wizycie
   - Zmień datę, godzinę lub dane pacjenta
   - Kliknij "Zapisz zmiany"

## Uwagi

- Nie można edytować ani usuwać wizyt zakończonych lub anulowanych
- Nie można dodawać wizyt w przeszłości
- Aktualna wizyta jest pokazywana tylko dla dzisiejszej daty
- Wszystkie operacje są logowane w systemie
- Zmiany są natychmiast widoczne w interfejsie
- Po dodaniu wizyty następuje przekierowanie na stronę z wybraną datą 