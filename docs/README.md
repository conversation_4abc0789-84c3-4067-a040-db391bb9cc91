# System zarządzania reklamami i kolejką

System do zarządzania kampaniami reklamowymi, w<PERSON><PERSON>wi<PERSON>laczami, statystykami oraz systemem kolejkowym dla placówek medycznych.

## Wymagania systemowe

- PHP 8.0 lub nowszy
- SQLite 3
- Przeglądarka internetowa z obsługą JavaScript

## Instalacja

1. Sklonuj repozytorium na swój serwer
2. Upewnij się, że katalog `database` ma uprawnienia do zapisu
3. Upewnij się, że katalog `uploads` ma uprawnienia do zapisu
4. Uruchom skrypt inicjalizacyjny bazy danych:

```bash
php init_db.php
```

## Struktura projektu

Projekt jest oparty o architekturę MVC z podziałem na mniejsze, zarządzalne pliki:

### Models/ - Modele danych
- **User.php** - zarządzanie użytkownikami
- **Campaign.php** - zarządzanie kampaniami
- **Statistic.php** - zarządzanie statystykami
- **Category.php** - zarządzanie kategoriami
- **QueueSystem.php** - główny model systemu kolejkowego
- **Room.php** - zarządzanie salami
- **Doctor.php** - zarządzanie lekarzami
- **Appointment.php** - zarządzanie wizytami

### Views/ - Widoki
- **admin/** - widoki panelu administracyjnego
- **client/** - widoki panelu klienta
- **auth/** - widoki autoryzacji
- **home/** - widoki strony głównej
- **partials/** - częściowe widoki

### Controllers/ - Kontrolery
- **HomeController.php** - strona główna
- **AuthController.php** - autoryzacja
- **AdminController.php** - główny kontroler panelu administracyjnego
- **AdminUserController.php** - zarządzanie użytkownikami
- **AdminCampaignController.php** - zarządzanie kampaniami
- **AdminStatisticController.php** - zarządzanie statystykami
- **ClientController.php** - panel klienta
- **ApiController.php** - API dla wyświetlaczy
- **QueueController.php** - główny kontroler systemu kolejkowego
- **RoomController.php** - zarządzanie salami
- **DoctorController.php** - zarządzanie lekarzami
- **AppointmentController.php** - zarządzanie wizytami
- **DoctorApiController.php** - API dla lekarzy

### Assets/ - Zasoby statyczne
- **css/display.css** - style dla wyświetlacza
- **js/display.js** - logika JavaScript dla wyświetlacza

### Core/ - Główne klasy systemu
- **Controller.php** - bazowy kontroler
- **Router.php** - router URL
- **Database.php** - obsługa bazy danych

## Struktura bazy danych

### Tabele reklamowe
- **users** - tabela użytkowników
- **campaigns** - tabela kampanii reklamowych
- **categories** - tabela kategorii kampanii
- **ad_views** - tabela wyświetleń reklam
- **client_displays** - tabela wyświetlaczy klientów
- **campaign_assignments** - tabela przypisań kampanii do klientów
- **campaign_auto_accept** - tabela automatycznej akceptacji kategorii

### Tabele systemu kolejkowego
- **queue_config** - konfiguracja systemu kolejkowego dla klientów
- **queue_rooms** - sale/gabinety
- **queue_doctors** - lekarze
- **queue_appointments** - wizyty pacjentów

## Funkcjonalności

### System reklamowy
- Zarządzanie kampaniami reklamowymi (obrazy, wideo, YouTube)
- Automatyczne wyświetlanie reklam na wyświetlaczach
- Statystyki wyświetleń i kosztów
- System kategorii kampanii
- Automatyczna akceptacja kampanii przez klientów

### System kolejkowy
- Zarządzanie salami/gabinetami
- Zarządzanie lekarzami z możliwością dodania zdjęć
- System wizyt z harmonogramem
- Wyświetlacz kolejki z rotacją sal
- Panel lekarza dostępny przez PWA
- API dla integracji z zewnętrznymi systemami

## Role użytkowników

- **admin** - pełny dostęp do systemu
- **client** - klient wyświetlający reklamy i zarządzający kolejką

## Dane logowania domyślnego administratora

- Login: admin
- Hasło: admin123

## Refaktoryzacja

Projekt został poddany pełnej refaktoryzacji zgodnie z zasadami:

- **Podział dużych plików** - pliki powyżej 500 linii zostały podzielone na mniejsze
- **Zasada pojedynczej odpowiedzialności** - każdy kontroler i model ma jednoznacznie określoną odpowiedzialność
- **Separacja warstw** - CSS i JavaScript zostały wydzielone do osobnych plików
- **Usunięcie niepotrzebnych plików** - usunięto pliki testowe i duplikaty
- **Aktualizacja routingu** - routing został zaktualizowany dla nowych kontrolerów

## Licencja

Ten projekt jest udostępniony na licencji MIT.