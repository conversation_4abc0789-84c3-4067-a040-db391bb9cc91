# Przewodnik importu danych CSV do systemu kolejkowego

## Opis funkcjonalności

System kolejkowy został rozszerzony o możliwość importu danych wizyt z plików CSV eksportowanych z programu dr Eryk. Funkcjonalność pozwala na:

- Automatyczne wykrywanie separatorów (przecinek, średnik, pionowa kreska)
- Mapowanie lekarzy z pliku CSV na lekarzy w systemie
- Zapamiętywanie mapowań dla przyszłych importów
- Import wizyt z określeniem daty
- Pomijanie wizyt poza kolejką

## Jak korzystać z importu CSV

### Krok 1: Przejdź do systemu kolejkowego
1. Zaloguj się do systemu jako klient
2. Przejdź do **System kolejkowy** w menu
3. Kliknij przycisk **Import CSV** w nagłówku strony

### Krok 2: W<PERSON><PERSON>rz plik CSV
1. Kliknij **Wybierz plik CSV**
2. Wybierz plik eksportowany z programu dr Eryk
3. Kliknij **Prześlij i analizuj**

### Krok 3: Skonfiguruj import
1. **Wybierz datę wizyt** - data, na którą mają być zaimportowane wizyty
2. **Mapuj lekarzy** - dla każdego lekarza z pliku CSV wybierz odpowiadającego mu lekarza w systemie
3. **Opcjonalnie** - zaznacz "Zapamiętaj mapowania lekarzy" aby automatycznie wypełniać je przy kolejnych importach
4. Kliknij **Wykonaj import**

## Format pliku CSV

Plik CSV powinien mieć następującą strukturę:

```csv
,,,,,
Lista zarejestrowanych pacjentów,,,,,,,,,,,,
na dzień 2025-01-20,,,,,,,,,,,,
,,,,,,,,,,,,
Lp.,Godz.przyjęcia,Godz.zaplanowana,Godz.przyjęcia,Pacjent,Adres,,Komentarz,Kategoria wizyty,Telefon,Nr ID,,
Lekarz: NAZWA LEKARZA,,,,,,,,,,,,
1,10:51 ,10:00,10:51,IMIĘ NAZWISKO ,"adres",,komentarz, ,telefon,pesel,,,
```

### Wymagane kolumny:
- **Lp.** - numer porządkowy
- **Godz.zaplanowana** - godzina wizyty (format HH:MM)
- **Pacjent** - imię i nazwisko pacjenta

### Sekcje lekarzy:
Plik musi zawierać sekcje w formacie:
```
Lekarz: NAZWA LEKARZA
```

### Wizyty pomijane:
- Wizyty z godziną "poza kol."
- Wizyty z godziną "Powtórka Rp."
- Wizyty bez określonej godziny

## Obsługiwane separatory

System automatycznie wykrywa separator używany w pliku:
- **Przecinek** (,)
- **Średnik** (;)
- **Pionowa kreska** (|)

## Mapowanie lekarzy

### Pierwszy import:
1. Dla każdego lekarza z pliku CSV wybierz odpowiadającego mu lekarza w systemie
2. Mapowania są zapisywane w bazie danych

### Kolejne importy:
1. Jeśli zaznaczono "Zapamiętaj mapowania", system automatycznie wypełni mapowania
2. Można zmienić mapowania przed wykonaniem importu

## Walidacja danych

System sprawdza:
- Czy data wizyt nie jest z przeszłości
- Czy lekarz ma przypisany domyślny gabinet
- Czy nie ma konfliktów godzinowych dla lekarza
- Czy format godziny jest poprawny

## Komunikaty błędów

### Błędy podczas importu:
- **"Brak mapowania dla lekarza: NAZWA"** - nie wybrano lekarza w systemie
- **"Lekarz NAZWA nie ma przypisanego domyślnego gabinetu"** - lekarz nie ma przypisanego gabinetu
- **"Lekarz NAZWA ma już wizytę o godzinie HH:MM"** - konflikt godzinowy

### Rozwiązania:
1. Dodaj lekarza do systemu przed importem
2. Przypisz domyślny gabinet do lekarza
3. Sprawdź i usuń konfliktujące wizyty

## Przykład użycia

1. **Eksportuj dane** z programu dr Eryk do pliku CSV
2. **Przejdź do systemu kolejkowego** i kliknij "Import CSV"
3. **Wybierz plik** i prześlij go
4. **Skonfiguruj import**:
   - Data: 2025-01-20
   - Mapuj lekarzy: LUCYNA POLAŃSKA → dr Lucyna Polańska
   - Zapamiętaj mapowania: ✓
5. **Wykonaj import**
6. **Sprawdź wyniki** w systemie kolejkowym

## Uwagi techniczne

- Maksymalny rozmiar pliku: 10MB
- Obsługiwane formaty: tylko .csv
- Kodowanie: UTF-8
- Wizyty są importowane ze statusem "waiting"
- System automatycznie pomija duplikaty

## Wsparcie

W przypadku problemów z importem:
1. Sprawdź format pliku CSV
2. Upewnij się, że lekarze są dodani do systemu
3. Sprawdź czy lekarze mają przypisane gabinety
4. Skontaktuj się z administratorem systemu 