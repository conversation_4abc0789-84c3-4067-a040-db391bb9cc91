# KtoOstatni - Panel Lekarza (PWA)

## Opis

Aplikacja PWA (Progressive Web App) dla lekarzy do zarządzania kolejką wizyt. Aplikacja pozwala lekarzom na:

- Logowanie przez kod dostępu
- Przeglądanie aktualnej wizyty
- Przechodzenie do następnej wizyty
- Pomijanie wizyt
- Przeglądanie listy oczekujących pacjentów
- Sprawdzanie statystyk dziennych

## Wymagania

**Aplikacja wymaga stałego połączenia z internetem!** Wszystkie operacje są wykonywane w czasie rzeczywistym i wymagają połączenia z serwerem.

## Instalacja

1. Uruchom migrację bazy danych:
```bash
php migrate_add_access_code.php
```

2. Wygeneruj kody dostępu dla lekarzy w panelu administracyjnym

3. Otwórz aplikację w przeglądarce: `http://twoja-domena.pl/pwa/`

## Instalacja jako aplikacja

### Na komputerze:
1. Otwórz aplikację w przeglądarce Chrome/Edge
2. Kliknij ikonę instalacji w pasku adresu
3. Wybierz "Zainstaluj"

### Na telefonie:
1. Otwórz aplikację w przeglądarce Chrome/Safari
2. Dodaj do ekranu głównego
3. Aplikacja będzie działać jak natywna aplikacja

## Użytkowanie

### Logowanie
1. Wprowadź 12-znakowy kod dostępu w formacie: `xxxx-xxxx-xxxx`
2. Kod jest automatycznie formatowany podczas wpisywania
3. Po pomyślnym logowaniu aplikacja zapamięta sesję do północy bieżącego dnia

### Zarządzanie wizytami
- **Następna wizyta**: Kliknij przycisk "Następna wizyta"
- **Pomiń wizytę**: Kliknij przycisk "Pomiń"
- **Poprzednia wizyta**: Kliknij przycisk "Poprzednia"

### Funkcje
- **Automatyczne odświeżanie**: Dane są aktualizowane co 10 sekund (tylko gdy online)
- **Synchronizacja z wyświetlaczami**: Zmiany są natychmiast widoczne na wyświetlaczach w przychodni
- **Responsywność**: Dostosowuje się do różnych rozmiarów ekranów
- **Powiadomienia o braku połączenia**: Automatyczne informowanie o problemach z połączeniem

## Struktura plików

```
pwa/
├── index.html          # Główny plik HTML
├── styles.css          # Style CSS
├── app.js              # Logika aplikacji
├── manifest.json       # Manifest PWA
├── sw.js              # Service Worker
├── icons/             # Ikony aplikacji
└── README.md          # Ten plik
```

## Konfiguracja

### Generowanie kodów dostępu
Kody dostępu są generowane automatycznie dla każdego lekarza w systemie. Każdy kod:
- Składa się z 12 znaków (małe litery i cyfry)
- Ma format: `xxxx-xxxx-xxxx`
- Jest unikalny w całym systemie

### Bezpieczeństwo
- Sesja wygasa o północy bieżącego dnia
- Kody dostępu są przechowywane w bazie danych
- Brak dostępu do wrażliwych danych pacjentów
- Wszystkie operacje wymagają połączenia z serwerem

## Wymagania techniczne

- Przeglądarka z obsługą PWA (Chrome 67+, Firefox 67+, Safari 11.1+)
- **Stałe połączenie internetowe (wymagane)**
- HTTPS (wymagane dla PWA)

## Rozwiązywanie problemów

### Aplikacja nie ładuje się
- Sprawdź połączenie internetowe
- Wyczyść cache przeglądarki
- Sprawdź czy serwer działa

### Błąd logowania
- Sprawdź poprawność kodu dostępu
- Upewnij się, że lekarz jest aktywny w systemie
- Sprawdź połączenie internetowe
- Skontaktuj się z administratorem

### Dane się nie aktualizują
- Sprawdź połączenie internetowe
- Odśwież stronę
- Sprawdź czy system kolejkowy jest włączony

### Komunikat "Brak połączenia z internetem"
- Sprawdź połączenie sieciowe
- Sprawdź czy serwer jest dostępny
- Spróbuj ponownie za kilka sekund

## Synchronizacja z wyświetlaczami

Wszystkie zmiany w kolejce (wywołanie następnej wizyty, pominięcie wizyty) są natychmiast synchronizowane z wyświetlaczami w przychodni. Aplikacja działa w czasie rzeczywistym.

## Wsparcie

W przypadku problemów skontaktuj się z administratorem systemu lub sprawdź logi serwera. 