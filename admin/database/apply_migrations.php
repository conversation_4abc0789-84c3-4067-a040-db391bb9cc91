<?php
// Skrypt do uruchamiania migracji bazy danych

// Załaduj konfigurację bazy danych
require_once '../config.php';
require_once '../core/Database.php';

// Pobierz połączenie z bazą danych
$db = Database::getInstance()->getConnection();

// Katalog z migracjami
$migrationsDir = __DIR__ . '/migrations';

// Utwórz tabelę migracji, jeśli nie istnieje
$db->exec("
    CREATE TABLE IF NOT EXISTS migrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
");

// <PERSON><PERSON><PERSON> listę już zastosowanych migracji
$appliedMigrations = [];
$stmt = $db->query("SELECT name FROM migrations");
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $appliedMigrations[] = $row['name'];
}

// Pobierz wszystkie pliki migracji
$migrationFiles = glob($migrationsDir . '/*.sql');
sort($migrationFiles); // Sortuj, aby wykonać migracje w kolejności

$appliedCount = 0;
$errorCount = 0;

// Wykonaj każdą migrację, która nie została jeszcze zastosowana
foreach ($migrationFiles as $migrationFile) {
    $migrationName = basename($migrationFile);
    
    if (!in_array($migrationName, $appliedMigrations)) {
        echo "Applying migration: {$migrationName}...\n";
        
        try {
            // Odczytaj zawartość pliku migracji
            $sql = file_get_contents($migrationFile);
            
            // Rozpocznij transakcję
            $db->beginTransaction();
            
            // Wykonaj zapytania SQL
            $db->exec($sql);
            
            // Zapisz informację o zastosowanej migracji
            $stmt = $db->prepare("INSERT INTO migrations (name) VALUES (?)");
            $stmt->execute([$migrationName]);
            
            // Zatwierdź transakcję
            $db->commit();
            
            echo "Migration {$migrationName} applied successfully.\n";
            $appliedCount++;
            
        } catch (Exception $e) {
            // W przypadku błędu, cofnij transakcję
            $db->rollBack();
            echo "Error applying migration {$migrationName}: " . $e->getMessage() . "\n";
            $errorCount++;
        }
    } else {
        echo "Migration {$migrationName} already applied. Skipping.\n";
    }
}

echo "\nMigration summary:\n";
echo "- Applied: {$appliedCount}\n";
echo "- Errors: {$errorCount}\n";
echo "- Skipped: " . (count($migrationFiles) - $appliedCount - $errorCount) . "\n";
