CREATE TABLE IF NOT EXISTS sync_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    import_setting_id INTEGER NOT NULL,
    raw_data TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (import_setting_id) REFERENCES import_settings(id) ON DELETE CASCADE
);

-- Indeks dla szybszego wyszukiwania
CREATE INDEX IF NOT EXISTS idx_sync_data_import_setting_id ON sync_data(import_setting_id);
CREATE INDEX IF NOT EXISTS idx_sync_data_created_at ON sync_data(created_at);
