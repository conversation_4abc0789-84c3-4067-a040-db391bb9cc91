<?php
// Test endpointu API importu
header('Content-Type: text/html; charset=utf-8');

echo "<h1>Test Endpointu API Importu</h1>";

// Pobierz kod synchronizacji z parametru URL
$syncCode = $_GET['sync_code'] ?? '';

if (empty($syncCode)) {
    echo "<p style='color: red;'>❌ Brak kodu synchronizacji!</p>";
    echo "<p>Użyj: <code>test_import_endpoint.php?sync_code=TWOJ_KOD</code></p>";
    exit;
}

// Przygotuj testowe dane
$testData = [
    'sync_code' => $syncCode,
    'data' => [
        'date' => 'Środa, 06 sierpnia',
        'exportDate' => date('c'),
        'doctors' => [
            [
                'id' => 'test_doctor_1',
                'name' => 'dr n. med. Test Testowy',
                'columnIndex' => 0,
                'appointments' => [
                    [
                        'id' => 'test_appointment_1',
                        'doctorIndex' => 0,
                        'doctorName' => 'dr n. med. Test Testowy',
                        'time' => '09:00 - 09:30',
                        'patient' => '<PERSON>walski',
                        'service' => 'Konsultacja testowa',
                        'office' => 'Gabinet testowy',
                        'type' => 'wizyta'
                    ],
                    [
                        'id' => 'test_appointment_2',
                        'doctorIndex' => 0,
                        'doctorName' => 'dr n. med. Test Testowy',
                        'time' => '09:30 - 10:00',
                        'patient' => 'Anna Nowak',
                        'service' => 'Kontrola testowa',
                        'office' => 'Gabinet testowy',
                        'type' => 'rezerwacja'
                    ]
                ]
            ],
            [
                'id' => 'test_doctor_2',
                'name' => 'lek. Drugi Testowy',
                'columnIndex' => 1,
                'appointments' => [
                    [
                        'id' => 'test_appointment_3',
                        'doctorIndex' => 1,
                        'doctorName' => 'lek. Drugi Testowy',
                        'time' => '10:00 - 10:30',
                        'patient' => 'Piotr Wiśniewski',
                        'service' => 'Badanie testowe',
                        'office' => 'Gabinet 2',
                        'type' => 'wizyta'
                    ]
                ]
            ]
        ],
        'totalAppointments' => 3,
        'totalDoctors' => 2
    ]
];

echo "<h2>Dane testowe</h2>";
echo "<pre>" . json_encode($testData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

// Wyślij żądanie do API
$apiUrl = 'http://localhost:8080/admin/api/import';
$jsonData = json_encode($testData);

echo "<h2>Wysyłanie żądania do API</h2>";
echo "<p>URL: <code>$apiUrl</code></p>";
echo "<p>Metoda: POST</p>";
echo "<p>Content-Type: application/json</p>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($jsonData)
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 60);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<h2>Odpowiedź API</h2>";

if ($error) {
    echo "<p style='color: red;'>❌ Błąd cURL: $error</p>";
} else {
    echo "<p>Kod HTTP: <strong>$httpCode</strong></p>";

    if ($httpCode === 200) {
        echo "<p style='color: green;'>✅ Żądanie zakończone sukcesem!</p>";
    } else {
        echo "<p style='color: red;'>❌ Błąd HTTP: $httpCode</p>";
    }

    echo "<h3>Treść odpowiedzi:</h3>";

    $decodedResponse = json_decode($response, true);
    if ($decodedResponse) {
        echo "<pre>" . json_encode($decodedResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

        if (isset($decodedResponse['success']) && $decodedResponse['success']) {
            echo "<p style='color: green;'>✅ Import zakończony pomyślnie!</p>";

            if (isset($decodedResponse['stats'])) {
                $stats = $decodedResponse['stats'];
                echo "<h3>Statystyki importu:</h3>";
                echo "<ul>";
                echo "<li>Przetworzonych lekarzy: {$stats['doctors_processed']}</li>";
                echo "<li>Zmapowanych lekarzy: {$stats['doctors_mapped']}</li>";
                echo "<li>Przetworzonych wizyt: {$stats['appointments_processed']}</li>";
                echo "<li>Utworzonych wizyt: {$stats['appointments_created']}</li>";
                echo "<li>Zaktualizowanych wizyt: {$stats['appointments_updated']}</li>";
                if (!empty($stats['errors'])) {
                    echo "<li style='color: red;'>Błędy: " . implode(', ', $stats['errors']) . "</li>";
                }
                echo "</ul>";
            }
        } else {
            echo "<p style='color: red;'>❌ Import nie powiódł się!</p>";
            if (isset($decodedResponse['error'])) {
                echo "<p>Błąd: {$decodedResponse['error']}</p>";
            }
        }
    } else {
        echo "<pre>$response</pre>";
    }
}

// Test statusu importu
echo "<h2>Test statusu importu</h2>";
$statusUrl = "http://localhost:8080/admin/api/import/status/$syncCode";
echo "<p>URL: <code>$statusUrl</code></p>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $statusUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$statusResponse = curl_exec($ch);
$statusHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$statusError = curl_error($ch);
curl_close($ch);

if ($statusError) {
    echo "<p style='color: red;'>❌ Błąd cURL: $statusError</p>";
} else {
    echo "<p>Kod HTTP: <strong>$statusHttpCode</strong></p>";

    if ($statusHttpCode === 200) {
        $statusData = json_decode($statusResponse, true);
        if ($statusData) {
            echo "<h3>Status importu:</h3>";
            echo "<pre>" . json_encode($statusData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        }
    } else {
        echo "<p style='color: red;'>❌ Błąd HTTP: $statusHttpCode</p>";
        echo "<pre>$statusResponse</pre>";
    }
}

echo "<h2>Instrukcje</h2>";
echo "<ol>";
echo "<li>Sprawdź czy import się powiódł (powyżej)</li>";
echo "<li>Przejdź do panelu administracyjnego: <a href='/admin/client/import' target='_blank'>Ustawienia importu</a></li>";
echo "<li>Sprawdź mapowania lekarzy</li>";
echo "<li>Sprawdź czy wizyty zostały dodane do systemu kolejkowego</li>";
echo "</ol>";
