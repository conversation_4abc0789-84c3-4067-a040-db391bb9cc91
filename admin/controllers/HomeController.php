<?php

class HomeController extends Controller {
    
    public function index() {
        // Debug - sprawdź sesję
        if (isset($_GET['debug'])) {
            echo "Session debug:<br>";
            echo "user_id: " . ($_SESSION['user_id'] ?? 'nie ustawione') . "<br>";
            echo "user_role: " . ($_SESSION['user_role'] ?? 'nie ustawione') . "<br>";
            echo "Wszystkie zmienne sesji: <pre>" . print_r($_SESSION, true) . "</pre>";
            exit;
        }
        
        if (isset($_SESSION['user_id'])) {
            $role = $_SESSION['user_role'];
            switch ($role) {
                case 'admin':
                    $this->redirect('/admin');
                    break;
                case 'client':
                    $this->redirect('/client');
                    break;
            }
        }
        
        $this->view('home/index');
    }
} 