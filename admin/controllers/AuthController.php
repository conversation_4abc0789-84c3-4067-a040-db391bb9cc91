<?php

class AuthController extends Controller {
    
    public function loginForm() {
        if (isset($_SESSION['user_id'])) {
            $this->redirect('/');
        }
        $this->view('auth/login');
    }
    
    public function login() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/login');
        }
        
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';
        
        if (empty($email) || empty($password)) {
            $this->view('auth/login', ['error' => 'Wypełnij wszystkie pola']);
            return;
        }
        
        $stmt = $this->db->prepare("SELECT * FROM users WHERE email = ? AND is_active = 1");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password'])) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['username'] = $user['username'];
            
            // Aktualizuj ostatnią aktywność
            $stmt = $this->db->prepare("UPDATE users SET last_activity = CURRENT_TIMESTAMP WHERE id = ?");
            $stmt->execute([$user['id']]);
            
            $this->redirect('/');
        } else {
            $this->view('auth/login', ['error' => 'Nieprawidłowe dane logowania']);
        }
    }
    
    public function registerForm() {
        if (isset($_SESSION['user_id'])) {
            $this->redirect('/');
        }
        $this->view('auth/register');
    }
    
    public function register() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/register');
        }
        
        $username = $_POST['username'] ?? '';
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $role = $_POST['role'] ?? '';
        $company_name = $_POST['company_name'] ?? '';
        
        $errors = [];
        
        if (empty($username)) $errors[] = 'Nazwa użytkownika jest wymagana';
        if (empty($email)) $errors[] = 'Email jest wymagany';
        if (empty($password)) $errors[] = 'Hasło jest wymagane';
        if ($password !== $confirm_password) $errors[] = 'Hasła nie są identyczne';
        if ($role !== 'client') $errors[] = 'Nieprawidłowa rola';
        if (empty($company_name)) $errors[] = 'Nazwa firmy jest wymagana';
        
        // Sprawdź unikalność
        if (empty($errors)) {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM users WHERE username = ? OR email = ?");
            $stmt->execute([$username, $email]);
            if ($stmt->fetchColumn() > 0) {
                $errors[] = 'Użytkownik o takiej nazwie lub emailu już istnieje';
            }
        }
        
        if (!empty($errors)) {
            $this->view('auth/register', [
                'errors' => $errors,
                'data' => $_POST
            ]);
            return;
        }
        
        $stmt = $this->db->prepare("
            INSERT INTO users (username, email, password, role, company_name) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        if ($stmt->execute([
            $username,
            $email,
            password_hash($password, PASSWORD_DEFAULT),
            $role,
            $company_name
        ])) {
            $this->view('auth/login', ['success' => 'Konto zostało utworzone. Możesz się teraz zalogować.']);
        } else {
            $this->view('auth/register', [
                'errors' => ['Błąd podczas tworzenia konta'],
                'data' => $_POST
            ]);
        }
    }
    
    public function logout() {
        session_destroy();
        $this->redirect('/');
    }
} 