<?php

class ApiController extends Controller {
    
    public function getAds($clientId) {
        // Pobierz aktywne kampanie dla klienta
        $stmt = $this->db->prepare("
            SELECT c.id, c.name, c.media_type, c.media_url, c.youtube_id, 
                   c.duration, c.rate_per_second
            FROM campaign_assignments ca
            JOIN campaigns c ON ca.campaign_id = c.id
            WHERE ca.client_id = ? AND c.is_active = 1 
                  AND c.budget > c.spent
                  AND ca.is_accepted = 1
            ORDER BY RANDOM()
            LIMIT 1
        ");
        
        $stmt->execute([$clientId]);
        $campaign = $stmt->fetch();
        
        if (!$campaign) {
            $this->json(['ads' => []]);
            return;
        }
        
        $this->json([
            'ads' => [$campaign]
        ]);
    }
    
    public function recordView() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->json(['error' => 'Method not allowed']);
        }
        
        $campaign_id = $_POST['campaign_id'] ?? 0;
        $client_id = $_POST['client_id'] ?? 0;
        $duration_seconds = intval($_POST['duration_seconds'] ?? 0);
        
        if (!$campaign_id || !$client_id || !$duration_seconds) {
            $this->json(['error' => 'Missing required parameters']);
        }
        
        // Pobierz stawkę kampanii
        $stmt = $this->db->prepare("SELECT rate_per_second FROM campaigns WHERE id = ?");
        $stmt->execute([$campaign_id]);
        $campaign = $stmt->fetch();
        
        if (!$campaign) {
            $this->json(['error' => 'Campaign not found']);
        }
        
        $cost = $duration_seconds * $campaign['rate_per_second'];
        
        // Zapisz wyświetlenie
        $stmt = $this->db->prepare("
            INSERT INTO ad_views (campaign_id, client_id, duration_seconds, cost)
            VALUES (?, ?, ?, ?)
        ");
        
        if ($stmt->execute([$campaign_id, $client_id, $duration_seconds, $cost])) {
            // Aktualizuj wydane środki kampanii
            $stmt = $this->db->prepare("
                UPDATE campaigns 
                SET spent = spent + ? 
                WHERE id = ?
            ");
            $stmt->execute([$cost, $campaign_id]);
            
            $this->json(['success' => true, 'cost' => $cost]);
        } else {
            $this->json(['error' => 'Failed to record view']);
        }
    }
} 