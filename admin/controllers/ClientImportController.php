<?php

class ClientImportController extends Controller {
    private $importSettingModel;
    private $externalDoctorMappingModel;
    private $syncDataModel;

    public function __construct() {
        parent::__construct();

        require_once 'models/ImportSetting.php';
        require_once 'models/ExternalDoctorMapping.php';
        require_once 'models/SyncData.php';

        $this->importSettingModel = new ImportSetting();
        $this->externalDoctorMappingModel = new ExternalDoctorMapping();
        $this->syncDataModel = new SyncData();
    }

    /**
     * Lista ustawień importu dla zalogowanego klienta
     */
    public function index() {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        $importSettings = $this->importSettingModel->getByClientId($user['id']);

        $this->view('client/import/index', [
            'importSettings' => $importSettings
        ]);
    }

    /**
     * <PERSON>rz dodawania nowego ustawienia importu
     */
    public function create() {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $data = [
                'client_id' => $user['id'],
                'system_name' => $_POST['system_name'],
                'sync_code' => $_POST['sync_code'] ?: $this->importSettingModel->generateSyncCode(),
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'api_endpoint' => $_POST['api_endpoint'] ?: null,
                'sync_frequency' => $_POST['sync_frequency'] ?: 3600
            ];

            // Sprawdź unikalność kodu synchronizacji
            if (!$this->importSettingModel->isSyncCodeUnique($data['sync_code'])) {
                $this->setError('Kod synchronizacji już istnieje. Wygeneruj nowy kod.');
                $this->view('client/import/create', [
                    'data' => $data
                ]);
                return;
            }

            if ($this->importSettingModel->create($data)) {
                $this->setSuccess('Ustawienie importu zostało utworzone pomyślnie.');
                $this->redirect('client/import');
            } else {
                $this->setError('Wystąpił błąd podczas tworzenia ustawienia importu.');
            }
        }

        $this->view('client/import/create');
    }

    /**
     * Formularz edycji ustawienia importu
     */
    public function edit($id = null) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        if (!$id) {
            $this->redirect('client/import');
        }

        $importSetting = $this->importSettingModel->getById($id);
        if (!$importSetting || $importSetting['client_id'] != $user['id']) {
            $this->setError('Ustawienie importu nie zostało znalezione lub nie masz do niego dostępu.');
            $this->redirect('client/import');
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $data = [
                'system_name' => $_POST['system_name'],
                'sync_code' => $_POST['sync_code'],
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'api_endpoint' => $_POST['api_endpoint'] ?: null,
                'sync_frequency' => $_POST['sync_frequency'] ?: 3600
            ];

            // Sprawdź unikalność kodu synchronizacji
            if (!$this->importSettingModel->isSyncCodeUnique($data['sync_code'], $id)) {
                $this->setError('Kod synchronizacji już istnieje. Wybierz inny kod.');
                $this->view('client/import/edit', [
                    'importSetting' => $importSetting,
                    'data' => $data
                ]);
                return;
            }

            if ($this->importSettingModel->update($id, $data)) {
                $this->setSuccess('Ustawienie importu zostało zaktualizowane pomyślnie.');
                $this->redirect('client/import');
            } else {
                $this->setError('Wystąpił błąd podczas aktualizacji ustawienia importu.');
            }
        }

        $this->view('client/import/edit', [
            'importSetting' => $importSetting
        ]);
    }

    /**
     * Usuwanie ustawienia importu
     */
    public function delete($id = null) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        if (!$id) {
            $this->redirect('client/import');
        }

        $importSetting = $this->importSettingModel->getById($id);
        if (!$importSetting || $importSetting['client_id'] != $user['id']) {
            $this->setError('Ustawienie importu nie zostało znalezione lub nie masz do niego dostępu.');
            $this->redirect('client/import');
        }

        if ($this->importSettingModel->delete($id)) {
            $this->setSuccess('Ustawienie importu zostało usunięte pomyślnie.');
        } else {
            $this->setError('Wystąpił błąd podczas usuwania ustawienia importu.');
        }

        $this->redirect('client/import');
    }

    /**
     * Zarządzanie mapowaniami lekarzy dla ustawienia importu
     */
    public function doctorMappings($importSettingId = null) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        if (!$importSettingId) {
            $this->redirect('client/import');
        }

        $importSetting = $this->importSettingModel->getById($importSettingId);
        if (!$importSetting || $importSetting['client_id'] != $user['id']) {
            $this->setError('Ustawienie importu nie zostało znalezione lub nie masz do niego dostępu.');
            $this->redirect('client/import');
        }

        $mappings = $this->externalDoctorMappingModel->getByImportSettingId($importSettingId);
        $systemDoctors = $this->externalDoctorMappingModel->getAvailableSystemDoctors($user['id']);
        
        // Pobierz ostatnie dane synchronizacji
        $latestSyncData = $this->syncDataModel->getLatestData($importSettingId);
        $syncHistory = $this->syncDataModel->getHistory($importSettingId, 5);

        $this->view('client/import/doctor_mappings', [
            'importSetting' => $importSetting,
            'mappings' => $mappings,
            'systemDoctors' => $systemDoctors,
            'latestSyncData' => $latestSyncData,
            'syncHistory' => $syncHistory
        ]);
    }
    
    /**
     * Wyświetl szczegóły synchronizacji
     */
    public function viewSyncData($importSettingId = null, $syncDataId = null) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();
        
        if (!$importSettingId || !$syncDataId) {
            $this->redirect('client/import');
        }
        
        // Sprawdź czy użytkownik ma dostęp do ustawienia importu
        $importSetting = $this->importSettingModel->getById($importSettingId);
        if (!$importSetting || $importSetting['client_id'] != $user['id']) {
            $this->setError('Nie masz dostępu do tych danych synchronizacji.');
            $this->redirect('client/import');
        }
        
        // Pobierz dane synchronizacji
        $syncData = $this->syncDataModel->getById($importSettingId, $syncDataId);
        if (!$syncData) {
            $this->setError('Dane synchronizacji nie zostały znalezione.');
            $this->redirect('client/import/doctor-mappings/' . $importSettingId);
        }
        
        // Zdekoduj dane JSON
        $jsonData = json_decode($syncData['raw_data'], true);
        
        $this->view('client/import/view_sync_data', [
            'syncData' => $syncData,
            'importSetting' => $importSetting,
            'jsonData' => $jsonData
        ]);
    }

    /**
     * Aktualizacja mapowania lekarza
     */
    public function updateDoctorMapping() {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('client/import');
        }

        $mappingId = $_POST['mapping_id'];
        $importSettingId = $_POST['import_setting_id'];
        $systemDoctorId = $_POST['system_doctor_id'] ?: null;

        // Sprawdź czy ustawienie należy do zalogowanego klienta
        $importSetting = $this->importSettingModel->getById($importSettingId);
        if (!$importSetting || $importSetting['client_id'] != $user['id']) {
            $this->setError('Brak dostępu do tego ustawienia importu.');
            $this->redirect('client/import');
        }

        $data = [
            'system_doctor_id' => $systemDoctorId,
            'is_mapped' => $systemDoctorId ? 1 : 0
        ];

        if ($this->externalDoctorMappingModel->update($mappingId, $data)) {
            $this->setSuccess('Mapowanie lekarza zostało zaktualizowane pomyślnie.');
        } else {
            $this->setError('Wystąpił błąd podczas aktualizacji mapowania.');
        }

        $this->redirect('client/import/doctor-mappings/' . $importSettingId);
    }

    /**
     * Usuń mapowanie lekarza
     */
    public function deleteDoctorMapping() {
        $user = $this->getCurrentUser();
        if (!$user || $user['role'] !== 'client') {
            $this->redirect('auth/login');
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('client/import');
        }

        $mappingId = $_POST['mapping_id'];
        $importSettingId = $_POST['import_setting_id'];

        // Sprawdź czy ustawienie należy do zalogowanego klienta
        $importSetting = $this->importSettingModel->getById($importSettingId);
        if (!$importSetting || $importSetting['client_id'] != $user['id']) {
            $this->setError('Brak dostępu do tego ustawienia importu.');
            $this->redirect('client/import');
        }

        // Sprawdź czy mapowanie należy do tego ustawienia importu
        $mapping = $this->externalDoctorMappingModel->getById($mappingId);
        if (!$mapping || $mapping['import_setting_id'] != $importSettingId) {
            $this->setError('Mapowanie nie zostało znalezione.');
            $this->redirect('client/import/doctor-mappings/' . $importSettingId);
        }

        // Usuń mapowanie
        if ($this->externalDoctorMappingModel->delete($mappingId)) {
            $this->setSuccess('Mapowanie zostało usunięte.');
        } else {
            $this->setError('Nie udało się usunąć mapowania.');
        }

        $this->redirect('client/import/doctor-mappings/' . $importSettingId);
    }

    /**
     * Generowanie nowego kodu synchronizacji
     */
    public function generateSyncCode() {
        $this->requireAuth('client');

        $syncCode = $this->importSettingModel->generateSyncCode();

        header('Content-Type: application/json');
        echo json_encode(['sync_code' => $syncCode]);
    }

    /**
     * Test połączenia z systemem zewnętrznym
     */
    public function testConnection() {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('client/import');
        }

        $importSettingId = $_POST['import_setting_id'];
        $importSetting = $this->importSettingModel->getById($importSettingId);

        if (!$importSetting || $importSetting['client_id'] != $user['id']) {
            $this->setError('Brak dostępu do tego ustawienia importu.');
            $this->redirect('client/import');
        }

        // Tutaj można dodać logikę testowania połączenia
        // Na razie zwracamy sukces
        $this->setSuccess('Połączenie z systemem zewnętrznym zostało przetestowane pomyślnie.');
        $this->redirect('client/import/doctor-mappings/' . $importSettingId);
    }
}

