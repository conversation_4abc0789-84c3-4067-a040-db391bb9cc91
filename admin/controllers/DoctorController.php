<?php

class Doctor<PERSON><PERSON>roller extends Controller {
    private $queueSystem;
    
    public function __construct() {
        parent::__construct();
        $this->queueSystem = new QueueSystem();
    }
    
    // Lista lekarzy
    public function index() {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();
        
        $doctors = $this->queueSystem->getDoctors($user['id']);
        $rooms = $this->queueSystem->getRooms($user['id']);
        
        $this->view('client/queue/doctors', [
            'user' => $user,
            'doctors' => $doctors,
            'rooms' => $rooms
        ]);
    }
    
    // Formularz dodawania nowego lekarza
    public function create() {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();
        
        $rooms = $this->queueSystem->getRooms($user['id']);
        
        $this->view('client/queue/create_doctor', [
            'user' => $user,
            'rooms' => $rooms
        ]);
    }
    
    // Zapisywanie nowego lekarza
    public function store() {
        $this->requireAuth('client');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/client/queue/doctors');
            return;
        }
        
        $user = $this->getCurrentUser();
        $firstName = $_POST['first_name'] ?? '';
        $lastName = $_POST['last_name'] ?? '';
        $specialization = $_POST['specialization'] ?? '';
        $active = isset($_POST['active']) && $_POST['active'] == 1;
        $defaultRoomId = !empty($_POST['default_room_id']) ? $_POST['default_room_id'] : null;
        
        if (empty($firstName) || empty($lastName)) {
            $rooms = $this->queueSystem->getRooms($user['id']);
            $this->view('client/queue/create_doctor', [
                'user' => $user,
                'rooms' => $rooms,
                'error' => 'Imię i nazwisko są wymagane'
            ]);
            return;
        }
        
        // Obsługa zdjęcia
        $photoUrl = '';
        if (isset($_FILES['photo']) && $_FILES['photo']['error'] == 0) {
            $uploadDir = __DIR__ . '/../../uploads/doctors/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }
            
            $fileExtension = strtolower(pathinfo($_FILES['photo']['name'], PATHINFO_EXTENSION));
            $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            
            if (in_array($fileExtension, $allowedExtensions)) {
                $fileName = uniqid() . '.' . $fileExtension;
                $filePath = $uploadDir . $fileName;
                
                if (move_uploaded_file($_FILES['photo']['tmp_name'], $filePath)) {
                    $photoUrl = '/' . $filePath;
                }
            }
        }
        
        if ($this->queueSystem->addDoctor($user['id'], $firstName, $lastName, $specialization, $photoUrl, $active, $defaultRoomId)) {
            $_SESSION['flash_message'] = 'Lekarz został dodany pomyślnie.';
            $this->redirect('/client/queue/doctors');
        } else {
            $rooms = $this->queueSystem->getRooms($user['id']);
            $this->view('client/queue/create_doctor', [
                'user' => $user,
                'rooms' => $rooms,
                'error' => 'Wystąpił błąd podczas dodawania lekarza.'
            ]);
        }
    }
    
    // Formularz edycji lekarza
    public function edit($id) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();
        
        $doctor = $this->queueSystem->getDoctor($id);
        if (!$doctor || $doctor['client_id'] != $user['id']) {
            $_SESSION['flash_message'] = 'Lekarz nie istnieje lub nie masz do niego dostępu.';
            $this->redirect('/client/queue/doctors');
            return;
        }
        
        $rooms = $this->queueSystem->getRooms($user['id']);
        
        $this->view('client/queue/edit_doctor', [
            'user' => $user,
            'doctor' => $doctor,
            'rooms' => $rooms
        ]);
    }
    
    // Aktualizacja lekarza
    public function update($id) {
        $this->requireAuth('client');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/client/queue/doctors');
            return;
        }
        
        $user = $this->getCurrentUser();
        
        // Sprawdź czy lekarz należy do tego klienta
        $doctor = $this->queueSystem->getDoctor($id);
        if (!$doctor || $doctor['client_id'] != $user['id']) {
            $_SESSION['flash_message'] = 'Lekarz nie istnieje lub nie masz do niego dostępu.';
            $this->redirect('/client/queue/doctors');
            return;
        }
        
        $firstName = $_POST['first_name'] ?? '';
        $lastName = $_POST['last_name'] ?? '';
        $specialization = $_POST['specialization'] ?? '';
        $active = isset($_POST['active']) && $_POST['active'] == 1;
        $defaultRoomId = !empty($_POST['default_room_id']) ? $_POST['default_room_id'] : null;
        
        if (empty($firstName) || empty($lastName)) {
            $rooms = $this->queueSystem->getRooms($user['id']);
            $this->view('client/queue/edit_doctor', [
                'user' => $user,
                'doctor' => $doctor,
                'rooms' => $rooms,
                'error' => 'Imię i nazwisko są wymagane'
            ]);
            return;
        }
        
        // Obsługa zdjęcia - zachowaj istniejące lub uploaduj nowe
        $photoUrl = $doctor['photo_url'];
        if (isset($_FILES['photo']) && $_FILES['photo']['error'] == 0) {
            $uploadDir = __DIR__ . '/../../uploads/doctors/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }
            
            $fileExtension = strtolower(pathinfo($_FILES['photo']['name'], PATHINFO_EXTENSION));
            $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            
            if (in_array($fileExtension, $allowedExtensions)) {
                $fileName = uniqid() . '.' . $fileExtension;
                $filePath = $uploadDir . $fileName;
                
                if (move_uploaded_file($_FILES['photo']['tmp_name'], $filePath)) {
                    // Usuń stare zdjęcie jeśli istnieje
                    if ($doctor['photo_url'] && file_exists(ltrim($doctor['photo_url'], '/'))) {
                        unlink(ltrim($doctor['photo_url'], '/'));
                    }
                    $photoUrl = '/' . $filePath;
                }
            }
        }
        
        if ($this->queueSystem->updateDoctor($id, $firstName, $lastName, $specialization, $photoUrl, $active, $defaultRoomId)) {
            $_SESSION['flash_message'] = 'Lekarz został zaktualizowany pomyślnie.';
            $this->redirect('/client/queue/doctors');
        } else {
            $rooms = $this->queueSystem->getRooms($user['id']);
            $this->view('client/queue/edit_doctor', [
                'user' => $user,
                'doctor' => $doctor,
                'rooms' => $rooms,
                'error' => 'Wystąpił błąd podczas aktualizacji lekarza.'
            ]);
        }
    }
    
    // Usuwanie lekarza
    public function delete($id) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();
        
        // Sprawdź czy lekarz należy do tego klienta
        $doctor = $this->queueSystem->getDoctor($id);
        if (!$doctor || $doctor['client_id'] != $user['id']) {
            $_SESSION['flash_message'] = 'Lekarz nie istnieje lub nie masz do niego dostępu.';
            $this->redirect('/client/queue/doctors');
            return;
        }
        
        if ($this->queueSystem->deleteDoctor($id)) {
            $_SESSION['flash_message'] = 'Lekarz został usunięty pomyślnie.';
        } else {
            $_SESSION['flash_message'] = 'Wystąpił błąd podczas usuwania lekarza.';
        }
        
        $this->redirect('/client/queue/doctors');
    }
    
    // Generowanie kodu dostępu dla lekarza
    public function generateAccessCode($doctorId) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();
        
        // Sprawdź czy lekarz należy do tego klienta
        $doctor = $this->queueSystem->getDoctor($doctorId);
        if (!$doctor || $doctor['client_id'] != $user['id']) {
            $_SESSION['flash_message'] = 'Lekarz nie istnieje lub nie masz do niego dostępu.';
            $this->redirect('/client/queue/doctors');
            return;
        }
        
        $accessCode = $this->generateRandomCode();
        
        // Zapisz kod dostępu w bazie danych
        $stmt = $this->db->prepare("
            UPDATE queue_doctors 
            SET access_code = ?, access_code_expires_at = DATE_ADD(NOW(), INTERVAL 24 HOUR)
            WHERE id = ?
        ");
        
        if ($stmt->execute([$accessCode, $doctorId])) {
            $_SESSION['flash_message'] = 'Kod dostępu został wygenerowany pomyślnie.';
        } else {
            $_SESSION['flash_message'] = 'Wystąpił błąd podczas generowania kodu dostępu.';
        }
        
        $this->redirect('/client/queue/doctors');
    }
    
    private function generateRandomCode() {
        return strtoupper(substr(md5(uniqid()), 0, 8));
    }
} 