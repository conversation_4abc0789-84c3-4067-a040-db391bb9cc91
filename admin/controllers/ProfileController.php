<?php

class ProfileController extends Controller {
    public function profile() {
        $this->requireAuth();
        $user = $this->getCurrentUser();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $username = trim($_POST['username'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $companyName = trim($_POST['company_name'] ?? '');
            $password = $_POST['password'] ?? '';
            $passwordConfirm = $_POST['password_confirm'] ?? '';

            $errors = [];

            if ($username === '') {
                $errors['username'] = 'Nazwa użytkownika jest wymagana';
            }

            if ($email === '' || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $errors['email'] = 'Podaj poprawny adres email';
            }

            if ($password !== '' && strlen($password) < 6) {
                $errors['password'] = 'Hasło musi mieć co najmniej 6 znaków';
            }

            if ($password !== '' && $password !== $passwordConfirm) {
                $errors['password_confirm'] = 'Hasła nie są zgodne';
            }

            // Sprawdź unikalność username i email (z wyłączeniem bieżącego użytkownika)
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM users WHERE username = ? AND id != ?");
            $stmt->execute([$username, $user['id']]);
            if ($stmt->fetchColumn() > 0) {
                $errors['username'] = 'Ta nazwa użytkownika jest już zajęta';
            }

            $stmt = $this->db->prepare("SELECT COUNT(*) FROM users WHERE email = ? AND id != ?");
            $stmt->execute([$email, $user['id']]);
            if ($stmt->fetchColumn() > 0) {
                $errors['email'] = 'Ten adres email jest już zajęty';
            }

            if (empty($errors)) {
                if ($password !== '') {
                    $stmt = $this->db->prepare("UPDATE users SET username = ?, email = ?, company_name = ?, password = ? WHERE id = ?");
                    $ok = $stmt->execute([
                        $username,
                        $email,
                        $companyName,
                        password_hash($password, PASSWORD_DEFAULT),
                        $user['id']
                    ]);
                } else {
                    $stmt = $this->db->prepare("UPDATE users SET username = ?, email = ?, company_name = ? WHERE id = ?");
                    $ok = $stmt->execute([
                        $username,
                        $email,
                        $companyName,
                        $user['id']
                    ]);
                }

                if ($ok) {
                    $_SESSION['username'] = $username; // odśwież wyświetlaną nazwę
                    $_SESSION['flash_message'] = 'Profil został zaktualizowany.';
                    $this->redirect('/profile');
                }
            }

            // W razie błędów wyświetl formularz z danymi
            $this->view('profile/profile', [
                'user' => [
                    'username' => $username,
                    'email' => $email,
                    'company_name' => $companyName,
                    'role' => $user['role'],
                    'created_at' => $user['created_at'] ?? null,
                ],
                'errors' => $errors,
            ]);
            return;
        }

        $this->view('profile/profile', [ 'user' => $user ]);
    }

    public function settings() {
        $this->requireAuth();
        $user = $this->getCurrentUser();

        // Proste ustawienia interfejsu zapisywane w sesji (bez zmian w DB)
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $_SESSION['pref_language'] = $_POST['language'] ?? 'pl';
            $_SESSION['pref_dark_mode'] = isset($_POST['dark_mode']) ? 1 : 0;
            $_SESSION['flash_message'] = 'Ustawienia zostały zapisane.';
            $this->redirect('/settings');
        }

        $this->view('profile/settings', [
            'user' => $user,
            'language' => $_SESSION['pref_language'] ?? 'pl',
            'dark_mode' => $_SESSION['pref_dark_mode'] ?? 0,
        ]);
    }
}


