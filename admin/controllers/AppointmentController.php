<?php

class AppointmentController extends Controller {
    private $queueSystem;
    
    public function __construct() {
        parent::__construct();
        $this->queueSystem = new QueueSystem();
    }
    
    // Lista wizyt dla sali
    public function index($roomId) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();
        
        // Sprawdź czy sala należy do tego klienta
        $room = $this->queueSystem->getRoom($roomId, $user['id']);
        if (!$room) {
            $_SESSION['flash_message'] = 'Sala nie istnieje lub nie masz do niej dostępu.';
            $this->redirect('/client/queue');
            return;
        }
        
        $selectedDate = $_GET['date'] ?? date('Y-m-d');
        $appointments = $this->queueSystem->getAllAppointments($roomId, $selectedDate);
        $currentAppointment = $this->queueSystem->getCurrentAppointment($roomId);
        $waitingAppointments = $this->queueSystem->getWaitingAppointments($roomId, 10, $selectedDate);
        $doctors = $this->queueSystem->getDoctors($user['id']);
        
        $this->view('client/queue/appointments', [
            'user' => $user,
            'room' => $room,
            'appointments' => $appointments,
            'currentAppointment' => $currentAppointment,
            'waitingAppointments' => $waitingAppointments,
            'doctors' => $doctors,
            'selectedDate' => $selectedDate
        ]);
    }
    
    // Formularz dodawania nowej wizyty
    public function create($roomId) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();
        
        // Sprawdź czy sala należy do tego klienta
        $room = $this->queueSystem->getRoom($roomId, $user['id']);
        if (!$room) {
            $_SESSION['flash_message'] = 'Sala nie istnieje lub nie masz do niej dostępu.';
            $this->redirect('/client/queue');
            return;
        }
        
        $doctors = $this->queueSystem->getDoctors($user['id']);
        
        $this->view('client/queue/create_appointment', [
            'user' => $user,
            'room' => $room,
            'doctors' => $doctors
        ]);
    }
    
    // Zapisywanie nowej wizyty
    public function store($roomId) {
        $this->requireAuth('client');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/client/queue/appointments/' . $roomId);
            return;
        }
        
        $user = $this->getCurrentUser();
        
        // Sprawdź czy sala należy do tego klienta
        $room = $this->queueSystem->getRoom($roomId, $user['id']);
        if (!$room) {
            $_SESSION['flash_message'] = 'Sala nie istnieje lub nie masz do niej dostępu.';
            $this->redirect('/client/queue');
            return;
        }
        
        $appointmentTime = $_POST['appointment_time'] ?? '';
        $patientName = $_POST['patient_name'] ?? '';
        $appointmentDate = $_POST['appointment_date'] ?? date('Y-m-d');
        $doctorId = !empty($_POST['doctor_id']) ? $_POST['doctor_id'] : null;
        
        if (empty($appointmentTime)) {
            $doctors = $this->queueSystem->getDoctors($user['id']);
            $this->view('client/queue/create_appointment', [
                'user' => $user,
                'room' => $room,
                'doctors' => $doctors,
                'error' => 'Godzina wizyty jest wymagana'
            ]);
            return;
        }
        
        if ($this->queueSystem->addAppointment($user['id'], $roomId, $appointmentTime, $patientName, $appointmentDate, $doctorId)) {
            $_SESSION['flash_message'] = 'Wizyta została dodana pomyślnie.';
            $this->redirect('/client/queue/appointments/' . $roomId);
        } else {
            $doctors = $this->queueSystem->getDoctors($user['id']);
            $this->view('client/queue/create_appointment', [
                'user' => $user,
                'room' => $room,
                'doctors' => $doctors,
                'error' => 'Wystąpił błąd podczas dodawania wizyty.'
            ]);
        }
    }
    
    // Formularz edycji wizyty
    public function edit($appointmentId) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();
        
        $appointment = $this->queueSystem->getAppointment($appointmentId);
        if (!$appointment || $appointment['client_id'] != $user['id']) {
            $_SESSION['flash_message'] = 'Wizyta nie istnieje lub nie masz do niej dostępu.';
            $this->redirect('/client/queue');
            return;
        }
        
        $room = $this->queueSystem->getRoom($appointment['room_id'], $user['id']);
        $doctors = $this->queueSystem->getDoctors($user['id']);
        
        $this->view('client/queue/edit_appointment', [
            'user' => $user,
            'appointment' => $appointment,
            'room' => $room,
            'doctors' => $doctors
        ]);
    }
    
    // Aktualizacja wizyty
    public function update($appointmentId) {
        $this->requireAuth('client');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/client/queue');
            return;
        }
        
        $user = $this->getCurrentUser();
        
        // Sprawdź czy wizyta należy do tego klienta
        $appointment = $this->queueSystem->getAppointment($appointmentId);
        if (!$appointment || $appointment['client_id'] != $user['id']) {
            $_SESSION['flash_message'] = 'Wizyta nie istnieje lub nie masz do niej dostępu.';
            $this->redirect('/client/queue');
            return;
        }
        
        $appointmentTime = $_POST['appointment_time'] ?? '';
        $patientName = $_POST['patient_name'] ?? '';
        $appointmentDate = $_POST['appointment_date'] ?? date('Y-m-d');
        $doctorId = !empty($_POST['doctor_id']) ? $_POST['doctor_id'] : null;
        
        if (empty($appointmentTime)) {
            $room = $this->queueSystem->getRoom($appointment['room_id'], $user['id']);
            $doctors = $this->queueSystem->getDoctors($user['id']);
            $this->view('client/queue/edit_appointment', [
                'user' => $user,
                'room' => $room,
                'doctors' => $doctors,
                'error' => 'Godzina wizyty jest wymagana',
                'appointment' => [
                    'id' => $appointmentId,
                    'appointment_time' => $appointmentTime,
                    'patient_name' => $patientName,
                    'appointment_date' => $appointmentDate,
                    'doctor_id' => $doctorId
                ]
            ]);
            return;
        }
        
        if ($this->queueSystem->updateAppointment($appointmentId, $appointmentTime, $patientName, $appointmentDate, $doctorId)) {
            $_SESSION['flash_message'] = 'Wizyta została zaktualizowana pomyślnie.';
            $dateParam = $appointmentDate ? ('?date=' . urlencode($appointmentDate)) : '';
            $this->redirect('/client/queue/' . $dateParam);
        } else {
            $room = $this->queueSystem->getRoom($appointment['room_id'], $user['id']);
            $doctors = $this->queueSystem->getDoctors($user['id']);
            $this->view('client/queue/edit_appointment', [
                'user' => $user,
                'room' => $room,
                'doctors' => $doctors,
                'error' => 'Wystąpił błąd podczas aktualizacji wizyty.',
                'appointment' => [
                    'id' => $appointmentId,
                    'appointment_time' => $appointmentTime,
                    'patient_name' => $patientName,
                    'appointment_date' => $appointmentDate,
                    'doctor_id' => $doctorId
                ]
            ]);
        }
    }
    
    // Usuwanie wizyty
    public function delete($appointmentId) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();
        
        // Sprawdź czy wizyta należy do tego klienta
        $appointment = $this->queueSystem->getAppointment($appointmentId);
        if (!$appointment || $appointment['client_id'] != $user['id']) {
            $_SESSION['flash_message'] = 'Wizyta nie istnieje lub nie masz do niej dostępu.';
            $this->redirect('/client/queue');
            return;
        }
        
        if ($this->queueSystem->deleteAppointment($appointmentId)) {
            $_SESSION['flash_message'] = 'Wizyta została usunięta pomyślnie.';
        } else {
            $_SESSION['flash_message'] = 'Wystąpił błąd podczas usuwania wizyty.';
        }
        
        $dateParam = isset($appointment['appointment_date']) ? ('?date=' . urlencode($appointment['appointment_date'])) : '';
        $this->redirect('/client/queue/appointments/' . $appointment['room_id'] . $dateParam);
    }
    
    // Wywołanie następnej wizyty
    public function callNext($roomId) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();
        
        // Sprawdź czy sala należy do tego klienta
        $room = $this->queueSystem->getRoom($roomId, $user['id']);
        if (!$room) {
            $_SESSION['flash_message'] = 'Sala nie istnieje lub nie masz do niej dostępu.';
            $this->redirect('/client/queue');
            return;
        }
        
        if ($this->queueSystem->callNextAppointment($roomId)) {
            $_SESSION['flash_message'] = 'Następna wizyta została wywołana.';
        } else {
            $_SESSION['flash_message'] = 'Brak oczekujących wizyt lub wystąpił błąd.';
        }
        
        $this->redirect('/client/queue/appointments/' . $roomId);
    }
    
    // Pominięcie aktualnej wizyty
    public function skipCurrent($roomId) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();
        
        // Sprawdź czy sala należy do tego klienta
        $room = $this->queueSystem->getRoom($roomId, $user['id']);
        if (!$room) {
            $_SESSION['flash_message'] = 'Sala nie istnieje lub nie masz do niej dostępu.';
            $this->redirect('/client/queue');
            return;
        }
        
        if ($this->queueSystem->skipCurrentAppointment($roomId)) {
            $_SESSION['flash_message'] = 'Aktualna wizyta została pominięta.';
        } else {
            $_SESSION['flash_message'] = 'Wystąpił błąd podczas pomijania wizyty.';
        }
        
        $this->redirect('/client/queue/appointments/' . $roomId);
    }
} 