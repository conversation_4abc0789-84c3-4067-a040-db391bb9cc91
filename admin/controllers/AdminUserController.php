<?php

class AdminUserController extends Controller {
    private $userModel;
    
    public function __construct() {
        parent::__construct();
        require_once 'models/User.php';
        $this->userModel = new User();
    }
    
    public function index() {
        $this->requireAuth('admin');
        $users = $this->userModel->getAll();
        $this->view('admin/users', ['users' => $users]);
    }
    
    public function add() {
        $this->requireAuth('admin');
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $username = $_POST['username'] ?? '';
            $email = $_POST['email'] ?? '';
            $password = $_POST['password'] ?? '';
            $role = $_POST['role'] ?? '';
            $company_name = $_POST['company_name'] ?? '';
            $balance = floatval($_POST['balance'] ?? 0);
            
            // Walidacja
            $errors = [];
            
            if (empty($username)) {
                $errors['username'] = 'Nazwa użytkownika jest wymagana';
            }
            
            if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $errors['email'] = 'Podaj poprawny adres email';
            }
            
            if (empty($password) || strlen($password) < 6) {
                $errors['password'] = 'Hasło musi mieć co najmniej 6 znaków';
            }
            
            if (empty($role) || !in_array($role, ['client'])) {
                $errors['role'] = 'Wybierz poprawną rolę użytkownika';
            }
            
            if (empty($company_name)) {
                $errors['company_name'] = 'Nazwa firmy jest wymagana';
            }
            
            // Sprawdź czy nazwa użytkownika i email są unikalne
            if (!$this->userModel->isUsernameUnique($username)) {
                $errors['username'] = 'Ta nazwa użytkownika jest już zajęta';
            }
            
            if (!$this->userModel->isEmailUnique($email)) {
                $errors['email'] = 'Ten adres email jest już zajęty';
            }
            
            if (empty($errors)) {
                $userData = [
                    'username' => $username,
                    'email' => $email,
                    'password' => $password,
                    'role' => $role,
                    'company_name' => $company_name,
                    'balance' => $balance
                ];
                
                $result = $this->userModel->add($userData);
                
                if ($result) {
                    $_SESSION['flash_message'] = 'Użytkownik został dodany pomyślnie';
                    $this->redirect('/admin/users');
                } else {
                    $errors['general'] = 'Wystąpił błąd podczas dodawania użytkownika';
                }
            }
            
            $this->view('admin/add_user', [
                'errors' => $errors,
                'formData' => $_POST
            ]);
        } else {
            $this->view('admin/add_user');
        }
    }
    
    public function edit($id = null) {
        $this->requireAuth('admin');
        
        if (!$id) {
            $this->redirect('/admin/users');
        }
        
        $user = $this->userModel->getById($id);
        
        if (!$user) {
            $_SESSION['flash_message'] = 'Użytkownik nie istnieje';
            $this->redirect('/admin/users');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $email = $_POST['email'] ?? '';
            $role = $_POST['role'] ?? '';
            $company_name = $_POST['company_name'] ?? '';
            $balance = floatval($_POST['balance'] ?? 0);
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            $password = $_POST['password'] ?? '';
            
            // Walidacja
            $errors = [];
            
            if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $errors['email'] = 'Podaj poprawny adres email';
            }
            
            if (empty($role) || !in_array($role, ['admin', 'client'])) {
                $errors['role'] = 'Wybierz poprawną rolę użytkownika';
            }
            
            if (empty($company_name)) {
                $errors['company_name'] = 'Nazwa firmy jest wymagana';
            }
            
            // Sprawdź czy email jest unikalny (z wyjątkiem bieżącego użytkownika)
            if (!$this->userModel->isEmailUnique($email, $id)) {
                $errors['email'] = 'Ten adres email jest już zajęty';
            }
            
            if (empty($errors)) {
                $userData = [
                    'email' => $email,
                    'role' => $role,
                    'company_name' => $company_name,
                    'balance' => $balance,
                    'is_active' => $is_active,
                    'password' => $password
                ];
                
                $result = $this->userModel->update($id, $userData);
                
                if ($result) {
                    $_SESSION['flash_message'] = 'Użytkownik został zaktualizowany pomyślnie';
                    $this->redirect('/admin/users');
                } else {
                    $errors['general'] = 'Wystąpił błąd podczas aktualizacji użytkownika';
                }
            }
            
            $this->view('admin/edit_user', [
                'user' => $user,
                'errors' => $errors,
                'formData' => $_POST
            ]);
        } else {
            $this->view('admin/edit_user', ['user' => $user]);
        }
    }
    
    public function delete($id = null) {
        $this->requireAuth('admin');
        
        if (!$id) {
            $this->redirect('/admin/users');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Sprawdź czy użytkownik ma powiązane dane
            if ($this->userModel->hasCampaigns($id)) {
                $_SESSION['flash_message'] = 'Nie można usunąć użytkownika, ponieważ ma powiązane kampanie. Dezaktywuj konto zamiast usuwania.';
                $this->redirect('/admin/users');
            }
            
            if ($this->userModel->hasViews($id)) {
                $_SESSION['flash_message'] = 'Nie można usunąć użytkownika, ponieważ ma powiązane wyświetlenia. Dezaktywuj konto zamiast usuwania.';
                $this->redirect('/admin/users');
            }
            
            if ($this->userModel->hasDisplays($id)) {
                $_SESSION['flash_message'] = 'Nie można usunąć użytkownika, ponieważ ma powiązane wyświetlacze. Dezaktywuj konto zamiast usuwania.';
                $this->redirect('/admin/users');
            }
            
            $result = $this->userModel->delete($id);
            
            if ($result) {
                $_SESSION['flash_message'] = 'Użytkownik został usunięty pomyślnie';
            } else {
                $_SESSION['flash_message'] = 'Wystąpił błąd podczas usuwania użytkownika';
            }
            
            $this->redirect('/admin/users');
        } else {
            $user = $this->userModel->getById($id);
            
            if (!$user) {
                $_SESSION['flash_message'] = 'Użytkownik nie istnieje';
                $this->redirect('/admin/users');
            }
            
            $this->view('admin/delete_user', ['user' => $user]);
        }
    }
} 