<?php

class CategoriesController extends Controller {
    
    public function index() {
        $this->requireAuth('admin');
        
        $stmt = $this->db->query("
            SELECT c.*, 
                   (SELECT COUNT(*) FROM campaigns WHERE category_id = c.id) as campaign_count
            FROM categories c
            ORDER BY c.name ASC
        ");
        $categories = $stmt->fetchAll();
        
        $this->view('admin/categories/index', ['categories' => $categories]);
    }
    
    public function create() {
        $this->requireAuth('admin');
        $this->view('admin/categories/create');
    }
    
    public function store() {
        $this->requireAuth('admin');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/admin/categories');
        }
        
        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        
        $errors = [];
        
        if (empty($name)) {
            $errors['name'] = 'Nazwa kategorii jest wymagana';
        }
        
        // Sprawdź czy nazwa jest unikalna
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM categories WHERE name = ?");
        $stmt->execute([$name]);
        if ($stmt->fetchColumn() > 0) {
            $errors['name'] = 'Kategoria o tej nazwie już istnieje';
        }
        
        if (!empty($errors)) {
            $this->view('admin/categories/create', [
                'errors' => $errors,
                'formData' => $_POST
            ]);
            return;
        }
        
        $stmt = $this->db->prepare("INSERT INTO categories (name, description) VALUES (?, ?)");
        $result = $stmt->execute([$name, $description]);
        
        if ($result) {
            $_SESSION['flash_message'] = 'Kategoria została dodana pomyślnie';
        } else {
            $_SESSION['flash_message'] = 'Wystąpił błąd podczas dodawania kategorii';
        }
        
        $this->redirect('/admin/categories');
    }
    
    public function edit($id) {
        $this->requireAuth('admin');
        
        $stmt = $this->db->prepare("SELECT * FROM categories WHERE id = ?");
        $stmt->execute([$id]);
        $category = $stmt->fetch();
        
        if (!$category) {
            $_SESSION['flash_message'] = 'Kategoria nie istnieje';
            $this->redirect('/admin/categories');
        }
        
        $this->view('admin/categories/edit', ['category' => $category]);
    }
    
    public function update($id) {
        $this->requireAuth('admin');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/admin/categories');
        }
        
        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        
        $errors = [];
        
        if (empty($name)) {
            $errors['name'] = 'Nazwa kategorii jest wymagana';
        }
        
        // Sprawdź czy nazwa jest unikalna (z wyjątkiem bieżącej kategorii)
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM categories WHERE name = ? AND id != ?");
        $stmt->execute([$name, $id]);
        if ($stmt->fetchColumn() > 0) {
            $errors['name'] = 'Kategoria o tej nazwie już istnieje';
        }
        
        if (!empty($errors)) {
            $stmt = $this->db->prepare("SELECT * FROM categories WHERE id = ?");
            $stmt->execute([$id]);
            $category = $stmt->fetch();
            
            $this->view('admin/categories/edit', [
                'category' => $category,
                'errors' => $errors,
                'formData' => $_POST
            ]);
            return;
        }
        
        $stmt = $this->db->prepare("UPDATE categories SET name = ?, description = ? WHERE id = ?");
        $result = $stmt->execute([$name, $description, $id]);
        
        if ($result) {
            $_SESSION['flash_message'] = 'Kategoria została zaktualizowana pomyślnie';
        } else {
            $_SESSION['flash_message'] = 'Wystąpił błąd podczas aktualizacji kategorii';
        }
        
        $this->redirect('/admin/categories');
    }
    
    public function delete($id) {
        $this->requireAuth('admin');
        
        $stmt = $this->db->prepare("SELECT * FROM categories WHERE id = ?");
        $stmt->execute([$id]);
        $category = $stmt->fetch();
        
        if (!$category) {
            $_SESSION['flash_message'] = 'Kategoria nie istnieje';
            $this->redirect('/admin/categories');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Sprawdź czy kategoria ma przypisane kampanie
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM campaigns WHERE category_id = ?");
            $stmt->execute([$id]);
            $campaignCount = $stmt->fetchColumn();
            
            if ($campaignCount > 0) {
                $_SESSION['flash_message'] = 'Nie można usunąć kategorii, ponieważ ma przypisane kampanie';
                $this->redirect('/admin/categories');
            }
            
            // Usuń automatyczne akceptacje dla tej kategorii
            $stmt = $this->db->prepare("DELETE FROM campaign_auto_accept WHERE category_id = ?");
            $stmt->execute([$id]);
            
            // Usuń kategorię
            $stmt = $this->db->prepare("DELETE FROM categories WHERE id = ?");
            $result = $stmt->execute([$id]);
            
            if ($result) {
                $_SESSION['flash_message'] = 'Kategoria została usunięta pomyślnie';
            } else {
                $_SESSION['flash_message'] = 'Wystąpił błąd podczas usuwania kategorii';
            }
            
            $this->redirect('/admin/categories');
        }
        
        $this->view('admin/categories/delete', ['category' => $category]);
    }
} 