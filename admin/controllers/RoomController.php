<?php

class RoomController extends Controller {
    private $queueSystem;
    
    public function __construct() {
        parent::__construct();
        $this->queueSystem = new QueueSystem();
    }
    
    // Lista sal
    public function index() {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();
        
        $rooms = $this->queueSystem->getRooms($user['id']);
        $doctors = $this->queueSystem->getDoctors($user['id']);
        
        $this->view('client/queue/rooms', [
            'user' => $user,
            'rooms' => $rooms,
            'doctors' => $doctors
        ]);
    }
    
    // Formularz dodawania nowej sali
    public function create() {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();
        
        $doctors = $this->queueSystem->getDoctors($user['id']);
        
        $this->view('client/queue/create_room', [
            'user' => $user,
            'doctors' => $doctors
        ]);
    }
    
    // Zapisywanie nowej sali
    public function store() {
        $this->requireAuth('client');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/client/queue/rooms');
            return;
        }
        
        $user = $this->getCurrentUser();
        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $doctorId = !empty($_POST['doctor_id']) ? $_POST['doctor_id'] : null;
        $roomNumber = $_POST['room_number'] ?? '';
        
        if (empty($name)) {
            $doctors = $this->queueSystem->getDoctors($user['id']);
            $this->view('client/queue/create_room', [
                'user' => $user,
                'doctors' => $doctors,
                'error' => 'Nazwa sali jest wymagana'
            ]);
            return;
        }
        
        if ($this->queueSystem->addRoom($user['id'], $name, $description, $doctorId, $roomNumber)) {
            $_SESSION['flash_message'] = 'Sala została dodana pomyślnie.';
            $this->redirect('/client/queue/rooms');
        } else {
            $doctors = $this->queueSystem->getDoctors($user['id']);
            $this->view('client/queue/create_room', [
                'user' => $user,
                'doctors' => $doctors,
                'error' => 'Wystąpił błąd podczas dodawania sali.'
            ]);
        }
    }
    
    // Formularz edycji sali
    public function edit($id) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();
        
        $room = $this->queueSystem->getRoom($id, $user['id']);
        if (!$room) {
            $_SESSION['flash_message'] = 'Sala nie istnieje lub nie masz do niej dostępu.';
            $this->redirect('/client/queue/rooms');
            return;
        }
        
        $doctors = $this->queueSystem->getDoctors($user['id']);
        
        $this->view('client/queue/edit_room', [
            'user' => $user,
            'room' => $room,
            'doctors' => $doctors
        ]);
    }
    
    // Aktualizacja sali
    public function update($id) {
        $this->requireAuth('client');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/client/queue/rooms');
            return;
        }
        
        $user = $this->getCurrentUser();
        
        // Sprawdź czy sala należy do tego klienta
        $room = $this->queueSystem->getRoom($id, $user['id']);
        if (!$room) {
            $_SESSION['flash_message'] = 'Sala nie istnieje lub nie masz do niej dostępu.';
            $this->redirect('/client/queue/rooms');
            return;
        }
        
        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $active = isset($_POST['active']) && $_POST['active'] == 1;
        $doctorId = !empty($_POST['doctor_id']) ? $_POST['doctor_id'] : null;
        $roomNumber = $_POST['room_number'] ?? '';
        
        if (empty($name)) {
            $doctors = $this->queueSystem->getDoctors($user['id']);
            $this->view('client/queue/edit_room', [
                'user' => $user,
                'doctors' => $doctors,
                'error' => 'Nazwa sali jest wymagana',
                'room' => [
                    'id' => $id,
                    'name' => $name,
                    'description' => $description,
                    'active' => $active,
                    'doctor_id' => $doctorId,
                    'room_number' => $roomNumber
                ]
            ]);
            return;
        }
        
        if ($this->queueSystem->updateRoom($id, $name, $description, $active, $doctorId, $roomNumber)) {
            $_SESSION['flash_message'] = 'Sala została zaktualizowana pomyślnie.';
            $this->redirect('/client/queue/rooms');
        } else {
            $doctors = $this->queueSystem->getDoctors($user['id']);
            $this->view('client/queue/edit_room', [
                'user' => $user,
                'doctors' => $doctors,
                'error' => 'Wystąpił błąd podczas aktualizacji sali.',
                'room' => [
                    'id' => $id,
                    'name' => $name,
                    'description' => $description,
                    'active' => $active,
                    'doctor_id' => $doctorId,
                    'room_number' => $roomNumber
                ]
            ]);
        }
    }
    
    // Usuwanie sali
    public function delete($id) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();
        
        // Sprawdź czy sala należy do tego klienta
        $room = $this->queueSystem->getRoom($id, $user['id']);
        if (!$room) {
            $_SESSION['flash_message'] = 'Sala nie istnieje lub nie masz do niej dostępu.';
            $this->redirect('/client/queue/rooms');
            return;
        }
        
        if ($this->queueSystem->deleteRoom($id)) {
            $_SESSION['flash_message'] = 'Sala została usunięta pomyślnie.';
        } else {
            $_SESSION['flash_message'] = 'Wystąpił błąd podczas usuwania sali.';
        }
        
        $this->redirect('/client/queue/rooms');
    }
} 