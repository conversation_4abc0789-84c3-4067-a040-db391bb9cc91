<?php

class AdminStatisticController extends Controller {
    private $statisticModel;
    
    public function __construct() {
        parent::__construct();
        require_once 'models/Statistic.php';
        $this->statisticModel = new Statistic();
    }
    
    public function index() {
        $this->requireAuth('admin');
        
        // Statystyki wyświetleń w czasie
        $dailyStats = $this->statisticModel->getDailyStats();
        
        // Top kampanie
        $topCampaigns = $this->statisticModel->getTopCampaigns();
        
        // Top klienci
        $topClients = $this->statisticModel->getTopClients();
        
        $this->view('admin/statistics', [
            'dailyStats' => $dailyStats,
            'topCampaigns' => $topCampaigns,
            'topClients' => $topClients
        ]);
    }
} 