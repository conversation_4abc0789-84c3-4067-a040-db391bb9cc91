<?php

class AdminCampaignController extends Controller {
    private $campaignModel;
    private $userModel;
    private $categoryModel;
    
    public function __construct() {
        parent::__construct();
        require_once 'models/Campaign.php';
        require_once 'models/User.php';
        require_once 'models/Category.php';
        $this->campaignModel = new Campaign();
        $this->userModel = new User();
        $this->categoryModel = new Category();
    }
    
    public function index() {
        $this->requireAuth('admin');
        $campaigns = $this->campaignModel->getAll();
        $this->view('admin/campaigns', ['campaigns' => $campaigns]);
    }
    
    public function add() {
        $this->requireAuth('admin');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $advertisers = $this->userModel->getActiveClients();
            $categories = $this->categoryModel->getAll();
            
            $this->view('admin/add_campaign', [
                'advertisers' => $advertisers,
                'categories' => $categories
            ]);
            return;
        }
        
        $advertisers = $this->userModel->getActiveClients();
        $categories = $this->categoryModel->getAll();
        
        $campaignData = [
            'advertiser_id' => $_POST['advertiser_id'] ?? '',
            'name' => $_POST['name'] ?? '',
            'description' => $_POST['description'] ?? '',
            'media_type' => $_POST['media_type'] ?? '',
            'duration' => intval($_POST['duration'] ?? 30),
            'budget' => floatval($_POST['budget'] ?? 0),
            'max_frequency_per_hour' => intval($_POST['max_frequency_per_hour'] ?? 0),
            'start_date' => $_POST['start_date'] ?? '',
            'end_date' => $_POST['end_date'] ?? '',
            'is_active' => isset($_POST['is_active']) ? 1 : 0,
            'category_id' => $_POST['category_id'] ?? null
        ];
        
        // Walidacja
        $errors = [];
        
        if (empty($campaignData['advertiser_id'])) $errors['advertiser_id'] = 'Wybierz reklamodawcę';
        if (empty($campaignData['name'])) $errors['name'] = 'Nazwa kampanii jest wymagana';
        if (empty($campaignData['media_type'])) $errors['media_type'] = 'Wybierz typ mediów';
        if ($campaignData['budget'] <= 0) $errors['budget'] = 'Budżet musi być większy od zera';
        
        // Przetwarzanie pliku i URL YouTube
        $file = isset($_FILES['media_file']) ? $_FILES['media_file'] : null;
        $youtubeUrl = $_POST['youtube_url'] ?? '';
        
        $mediaInfo = $this->campaignModel->processMediaUpload(
            $campaignData['media_type'],
            $file,
            $youtubeUrl
        );
        
        if (!$mediaInfo && $campaignData['media_type'] === 'youtube') {
            $errors['youtube_url'] = 'Podaj prawidłowy adres URL filmu YouTube';
        } elseif (!$mediaInfo) {
            $errors['media_file'] = 'Wybierz prawidłowy plik';
        }
        
        if (empty($errors)) {
            $result = $this->campaignModel->add($campaignData, $mediaInfo);
            
            if ($result) {
                $_SESSION['flash_message'] = 'Kampania została dodana pomyślnie';
                $this->redirect('/admin/campaigns');
            } else {
                $errors['general'] = 'Wystąpił błąd podczas dodawania kampanii';
            }
        }
        
        $this->view('admin/add_campaign', [
            'advertisers' => $advertisers,
            'categories' => $categories,
            'errors' => $errors,
            'formData' => $_POST
        ]);
    }
    
    public function edit($id) {
        $this->requireAuth('admin');
        
        $campaign = $this->campaignModel->getById($id);
        
        if (!$campaign) {
            $_SESSION['flash_message'] = 'Kampania nie istnieje';
            $this->redirect('/admin/campaigns');
        }
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $advertisers = $this->userModel->getActiveClients();
            $categories = $this->categoryModel->getAll();
            
            $this->view('admin/edit_campaign', [
                'campaign' => $campaign,
                'advertisers' => $advertisers,
                'categories' => $categories
            ]);
            return;
        }
        
        $advertisers = $this->userModel->getActiveClients();
        $categories = $this->categoryModel->getAll();
        
        $campaignData = [
            'advertiser_id' => $_POST['advertiser_id'] ?? '',
            'name' => $_POST['name'] ?? '',
            'description' => $_POST['description'] ?? '',
            'media_type' => $_POST['media_type'] ?? $campaign['media_type'],
            'duration' => intval($_POST['duration'] ?? 30),
            'budget' => floatval($_POST['budget'] ?? 0),
            'max_frequency_per_hour' => intval($_POST['max_frequency_per_hour'] ?? 0),
            'start_date' => $_POST['start_date'] ?? '',
            'end_date' => $_POST['end_date'] ?? '',
            'is_active' => isset($_POST['is_active']) ? 1 : 0,
            'category_id' => $_POST['category_id'] ?? $campaign['category_id']
        ];
        
        // Walidacja
        $errors = [];
        
        if (empty($campaignData['advertiser_id'])) $errors['advertiser_id'] = 'Wybierz reklamodawcę';
        if (empty($campaignData['name'])) $errors['name'] = 'Nazwa kampanii jest wymagana';
        if (empty($campaignData['media_type'])) $errors['media_type'] = 'Wybierz typ mediów';
        if ($campaignData['budget'] <= 0) $errors['budget'] = 'Budżet musi być większy od zera';
        
        // Sprawdź czy media zostały zmienione
        $mediaChanged = false;
        $mediaInfo = null;
        
        if ($campaignData['media_type'] === 'youtube' && isset($_POST['youtube_url']) && !empty($_POST['youtube_url'])) {
            $youtubeUrl = $_POST['youtube_url'];
            $mediaInfo = $this->campaignModel->processMediaUpload('youtube', null, $youtubeUrl);
            
            if (!$mediaInfo) {
                $errors['youtube_url'] = 'Nieprawidłowy adres URL filmu YouTube';
            } else {
                $mediaChanged = true;
            }
        } elseif (isset($_FILES['media_file']) && $_FILES['media_file']['error'] === UPLOAD_ERR_OK) {
            $file = $_FILES['media_file'];
            $mediaInfo = $this->campaignModel->processMediaUpload($campaignData['media_type'], $file);
            
            if (!$mediaInfo) {
                $errors['media_file'] = 'Nieprawidłowy typ pliku';
            } else {
                $mediaChanged = true;
            }
        }
        
        if (empty($errors)) {
            $result = $this->campaignModel->update($id, $campaignData, $mediaChanged ? $mediaInfo : null);
            
            if ($result) {
                $_SESSION['flash_message'] = 'Kampania została zaktualizowana pomyślnie';
                $this->redirect('/admin/campaigns');
            } else {
                $errors['general'] = 'Wystąpił błąd podczas aktualizacji kampanii';
            }
        }
        
        $this->view('admin/edit_campaign', [
            'campaign' => array_merge($campaign, $_POST),
            'advertisers' => $advertisers,
            'categories' => $categories,
            'errors' => $errors
        ]);
    }
    
    public function delete($id = null) {
        $this->requireAuth('admin');
        
        if (!$id) {
            $this->redirect('/admin/campaigns');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Sprawdź czy kampania ma powiązane wyświetlenia
            if ($this->campaignModel->hasViews($id)) {
                $_SESSION['flash_message'] = 'Nie można usunąć kampanii, ponieważ ma powiązane wyświetlenia. Dezaktywuj kampanię zamiast usuwania.';
                $this->redirect('/admin/campaigns');
            }
            
            // Usuń przypisania kampanii
            $this->campaignModel->deleteAssignments($id);
            
            // Usuń kampanię
            $result = $this->campaignModel->delete($id);
            
            if ($result) {
                $_SESSION['flash_message'] = 'Kampania została usunięta pomyślnie';
            } else {
                $_SESSION['flash_message'] = 'Wystąpił błąd podczas usuwania kampanii';
            }
            
            $this->redirect('/admin/campaigns');
        } else {
            $campaign = $this->campaignModel->getById($id);
            
            if (!$campaign) {
                $_SESSION['flash_message'] = 'Kampania nie istnieje';
                $this->redirect('/admin/campaigns');
            }
            
            $this->view('admin/delete_campaign', ['campaign' => $campaign]);
        }
    }
    
    public function forceAccept($id = null) {
        $this->requireAuth('admin');
        
        if (!$id) {
            $this->redirect('/admin/campaigns');
        }
        
        // Sprawdź czy kampania istnieje
        $campaign = $this->campaignModel->getById($id);
        
        if (!$campaign) {
            $_SESSION['flash_message'] = 'Kampania nie istnieje';
            $this->redirect('/admin/campaigns');
        }
        
        // Pobierz klientów
        $clients = $this->userModel->getActiveClients();
        
        // Pobierz przypisania kampanii
        $assignments = $this->campaignModel->getCampaignAssignments($id);
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $action = $_POST['action'] ?? '';
            $selectedClients = $_POST['clients'] ?? [];
            $forceAll = isset($_POST['force_all']);
            
            if ($action === 'force_accept') {
                if ($forceAll) {
                    // Wymuś akceptację dla wszystkich klientów
                    // Najpierw usuń istniejące przypisania
                    $this->campaignModel->deleteAssignments($id);
                    
                    foreach ($clients as $client) {
                        $this->campaignModel->assignCampaignToClient($id, $client['id']);
                    }
                    
                    $_SESSION['flash_message'] = 'Kampania została wymuszona dla wszystkich klientów';
                } elseif (!empty($selectedClients)) {
                    // Dla każdego wybranego klienta
                    foreach ($selectedClients as $clientId) {
                        $this->campaignModel->assignCampaignToClient($id, $clientId);
                    }
                    
                    $_SESSION['flash_message'] = 'Kampania została wymuszona dla wybranych klientów';
                }
            } elseif ($action === 'force_category' && $campaign['category_id']) {
                $result = $this->campaignModel->forceAcceptCategory(
                    $campaign['category_id'],
                    $forceAll ? [] : $selectedClients,
                    $forceAll
                );
                
                if ($result) {
                    $target = $forceAll ? 'wszystkich klientów' : 'wybranych klientów';
                    $_SESSION['flash_message'] = "Kategoria została wymuszona dla $target";
                } else {
                    $_SESSION['flash_message'] = 'Wystąpił błąd podczas wymuszania akceptacji kategorii';
                }
            }
            
            $this->redirect('/admin/campaigns');
        }
        
        $this->view('admin/force_accept_campaign', [
            'campaign' => $campaign,
            'clients' => $clients,
            'assignments' => $assignments
        ]);
    }
} 