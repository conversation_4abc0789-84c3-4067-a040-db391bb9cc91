<?php

class ClientController extends Controller
{
    public function dashboard()
    {
        $this->requireAuth("client");
        $user = $this->getCurrentUser();

        // Statystyki systemu kolejkowego
        require_once "models/QueueSystem.php";
        $queueSystem = new QueueSystem();

        // Pobierz statystyki systemu kolejkowego
        // Używamy bezpośrednio zapytań zamiast funkcji anonimowych z powodu problemu z $this w closure
        $db = $this->db;

        // Pobierz liczbę gabinetów
        $stmt = $db->prepare(
            "SELECT COUNT(*) FROM queue_rooms WHERE client_id = ?",
        );
        $stmt->execute([$user["id"]]);
        $totalRooms = $stmt->fetchColumn();

        // Pobierz liczbę lekarzy
        $stmt = $db->prepare(
            "SELECT COUNT(*) FROM queue_doctors WHERE client_id = ?",
        );
        $stmt->execute([$user["id"]]);
        $totalDoctors = $stmt->fetchColumn();

        // Pobierz liczbę dzisiejszych wizyt
        $stmt = $db->prepare("
            SELECT COUNT(*) FROM queue_appointments
            WHERE client_id = ? AND date(appointment_date) = date('now')
        ");
        $stmt->execute([$user["id"]]);
        $todayAppointmentsCount = $stmt->fetchColumn();

        $queueStats = [
            "total_rooms" => $totalRooms,
            "total_doctors" => $totalDoctors,
            "today_appointments" => $todayAppointmentsCount,
        ];

        // Pobierz gabinety klienta
        $stmt = $this->db->prepare("
            SELECT r.*, d.first_name || ' ' || d.last_name as doctor_name
            FROM queue_rooms r
            LEFT JOIN queue_doctors d ON r.doctor_id = d.id
            WHERE r.client_id = ?
            ORDER BY r.name
        ");
        $stmt->execute([$user["id"]]);
        $rooms = $stmt->fetchAll();

        // Pobierz dzisiejsze wizyty
        $stmt = $this->db->prepare("
            SELECT a.*, r.name as room_name
            FROM queue_appointments a
            JOIN queue_rooms r ON a.room_id = r.id
            WHERE a.client_id = ? AND date(a.appointment_date) = date('now')
            ORDER BY a.appointment_time
            LIMIT 10
        ");
        $stmt->execute([$user["id"]]);
        $todayAppointments = $stmt->fetchAll();

        // Statystyki kampanii (jako reklamodawca)
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as total_campaigns,
                   SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_campaigns,
                   SUM(budget) as total_budget,
                   SUM(spent) as total_spent
            FROM campaigns
            WHERE advertiser_id = ?
        ");
        $stmt->execute([$user["id"]]);
        $campaignStats = $stmt->fetch();

        // Statystyki wyświetleń jako reklamodawca
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as total_views, SUM(cost) as total_cost
            FROM ad_views av
            JOIN campaigns c ON av.campaign_id = c.id
            WHERE c.advertiser_id = ?
        ");
        $stmt->execute([$user["id"]]);
        $advertiserViewStats = $stmt->fetch();

        // Statystyki reklam
        $adStats = [
            "available_campaigns" => $this->db
                ->query("SELECT COUNT(*) FROM campaigns WHERE is_active = 1")
                ->fetchColumn(),
        ];

        // Wyświetlacze klienta
        $stmt = $this->db->prepare("
            SELECT * FROM client_displays
            WHERE client_id = ?
        ");
        $stmt->execute([$user["id"]]);
        $displays = $stmt->fetchAll();

        // Wyświetlacze klienta
        $stmt = $this->db->prepare("
            SELECT * FROM client_displays
            WHERE client_id = ?
        ");
        $stmt->execute([$user["id"]]);
        $displays = $stmt->fetchAll();

        $this->view("client/dashboard", [
            "user" => $user,
            "campaignStats" => $campaignStats,
            "advertiserViewStats" => $advertiserViewStats,
            "queueStats" => $queueStats,
            "rooms" => $rooms,
            "todayAppointments" => $todayAppointments,
            "adStats" => $adStats,
            "displays" => $displays,
        ]);
    }

    // FUNKCJE ZWIĄZANE Z REKLAMAMI (JAKO REKLAMODAWCA)

    public function campaigns()
    {
        $this->requireAuth("client");
        $user = $this->getCurrentUser();

        $stmt = $this->db->prepare("
            SELECT c.*,
                   (SELECT COUNT(*) FROM ad_views av WHERE av.campaign_id = c.id) as views
            FROM campaigns c
            WHERE c.advertiser_id = ?
            ORDER BY c.created_at DESC
        ");
        $stmt->execute([$user["id"]]);
        $campaigns = $stmt->fetchAll();

        $this->view("client/campaigns", ["campaigns" => $campaigns]);
    }

    public function createCampaign()
    {
        $this->requireAuth("client");
        $user = $this->getCurrentUser();

        // Pobierz listę klientów z wyłączeniem siebie
        $stmt = $this->db->prepare("
            SELECT id, username, company_name
            FROM users
            WHERE role = 'client' AND id != ? AND is_active = 1
        ");
        $stmt->execute([$user["id"]]);
        $clients = $stmt->fetchAll();

        $this->view("client/create_campaign", ["clients" => $clients]);
    }

    public function storeCampaign()
    {
        $this->requireAuth("client");

        if ($_SERVER["REQUEST_METHOD"] !== "POST") {
            $this->redirect("/client/campaigns/create");
        }

        $user = $this->getCurrentUser();
        $name = $_POST["name"] ?? "";
        $description = $_POST["description"] ?? "";
        $media_type = $_POST["media_type"] ?? "";
        $budget = floatval($_POST["budget"] ?? 0);
        $rate_per_second = floatval($_POST["rate_per_second"] ?? 0.0001);
        $max_frequency_per_hour = intval($_POST["max_frequency_per_hour"] ?? 0);
        $start_date = $_POST["start_date"] ?? "";
        $end_date = $_POST["end_date"] ?? "";
        $client_ids = $_POST["client_ids"] ?? [];

        $errors = [];

        if (empty($name)) {
            $errors[] = "Nazwa kampanii jest wymagana";
        }
        if (empty($media_type)) {
            $errors[] = "Typ mediów jest wymagany";
        }
        if ($budget <= 0) {
            $errors[] = "Budżet musi być większy od 0";
        }
        if (empty($client_ids)) {
            $errors[] = "Wybierz przynajmniej jednego klienta";
        }

        if (!empty($errors)) {
            // Pobierz listę klientów z wyłączeniem siebie
            $stmt = $this->db->prepare("
                SELECT id, username, company_name
                FROM users
                WHERE role = 'client' AND id != ? AND is_active = 1
            ");
            $stmt->execute([$user["id"]]);
            $clients = $stmt->fetchAll();

            $this->view("client/create_campaign", [
                "clients" => $clients,
                "errors" => $errors,
                "data" => $_POST,
            ]);
            return;
        }

        // Zapisz kampanię
        $stmt = $this->db->prepare("
            INSERT INTO campaigns (advertiser_id, name, description, media_type, media_url,
                                 budget, rate_per_second, max_frequency_per_hour, start_date, end_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        if (
            $stmt->execute([
                $user["id"],
                $name,
                $description,
                $media_type,
                "temp_url",
                $budget,
                $rate_per_second,
                $max_frequency_per_hour,
                $start_date,
                $end_date,
            ])
        ) {
            $campaign_id = $this->db->lastInsertId();

            // Przypisz klientów
            $stmt = $this->db->prepare(
                "INSERT INTO campaign_assignments (campaign_id, client_id) VALUES (?, ?)",
            );
            foreach ($client_ids as $client_id) {
                $stmt->execute([$campaign_id, $client_id]);
            }

            $this->redirect("/client/campaigns");
        }
    }

    public function showCampaign($id)
    {
        $this->requireAuth("client");
        $user = $this->getCurrentUser();

        $stmt = $this->db->prepare("
            SELECT c.*,
                   (SELECT COUNT(*) FROM ad_views av WHERE av.campaign_id = c.id) as total_views
            FROM campaigns c
            WHERE c.id = ? AND c.advertiser_id = ?
        ");
        $stmt->execute([$id, $user["id"]]);
        $campaign = $stmt->fetch();

        if (!$campaign) {
            $this->redirect("/client/campaigns");
        }

        $this->view("client/show_campaign", ["campaign" => $campaign]);
    }

    // FUNKCJE ZWIĄZANE Z WYŚWIETLANIEM REKLAM (JAKO REKLAMOBIORCA)

    public function display()
    {
        $this->requireAuth("client");
        $user = $this->getCurrentUser();

        // Pobierz przypisane kampanie z uwzględnieniem ograniczeń częstotliwości
        $stmt = $this->db->prepare("
            SELECT c.*, u.company_name as advertiser_name,
                   (SELECT COUNT(*) FROM ad_views av
                    WHERE av.campaign_id = c.id
                    AND av.client_id = ?
                    AND av.timestamp >= datetime('now', '-1 hour')) as views_last_hour
            FROM campaign_assignments ca
            JOIN campaigns c ON ca.campaign_id = c.id
            JOIN users u ON c.advertiser_id = u.id
            WHERE ca.client_id = ? AND c.is_active = 1
            ORDER BY c.created_at
        ");
        $stmt->execute([$user["id"], $user["id"]]);
        $allCampaigns = $stmt->fetchAll();

        // Filtruj kampanie, które przekroczyły limit częstotliwości
        $campaigns = array_filter($allCampaigns, function ($campaign) {
            // Jeśli max_frequency_per_hour = 0, nie ma ograniczeń
            if ($campaign["max_frequency_per_hour"] == 0) {
                return true;
            }

            // Sprawdź czy liczba wyświetleń w ostatniej godzinie nie przekroczyła limitu
            return $campaign["views_last_hour"] <
                $campaign["max_frequency_per_hour"];
        });

        $this->view("client/display", [
            "campaigns" => $campaigns,
            "user" => $user,
        ]);
    }

    public function statistics()
    {
        $this->requireAuth("client");
        $user = $this->getCurrentUser();

        // Statystyki jako reklamodawca
        $stmt = $this->db->prepare("
            SELECT DATE(av.timestamp) as date, COUNT(*) as views, SUM(av.cost) as revenue
            FROM ad_views av
            JOIN campaigns c ON av.campaign_id = c.id
            WHERE c.advertiser_id = ? AND av.timestamp >= date('now', '-30 days')
            GROUP BY DATE(av.timestamp)
            ORDER BY date DESC
        ");
        $stmt->execute([$user["id"]]);
        $advertiserStats = $stmt->fetchAll();

        // Statystyki jako reklamobiorca
        $stmt = $this->db->prepare("
            SELECT DATE(timestamp) as date,
                   COUNT(*) as views,
                   SUM(cost) as earned
            FROM ad_views
            WHERE client_id = ? AND timestamp >= date('now', '-30 days')
            GROUP BY DATE(timestamp)
            ORDER BY date DESC
        ");
        $stmt->execute([$user["id"]]);
        $clientStats = $stmt->fetchAll();

        $this->view("client/statistics", [
            "advertiserStats" => $advertiserStats,
            "clientStats" => $clientStats,
        ]);
    }

    public function heartbeat()
    {
        if ($_SERVER["REQUEST_METHOD"] !== "POST") {
            $this->json(["error" => "Method not allowed"]);
            return;
        }

        $display_id = $_POST["display_id"] ?? null;

        if (!$display_id) {
            $this->json(["error" => "Missing display ID"]);
            return;
        }

        // Zaktualizuj timestamp i status wyświetlacza
        $stmt = $this->db->prepare("
            UPDATE client_displays
            SET is_online = 1, last_heartbeat = datetime('now')
            WHERE id = ?
        ");

        if ($stmt->execute([$display_id])) {
            $this->json(["success" => true]);
        } else {
            $this->json(["error" => "Failed to update display status"]);
        }
    }

    public function manageDisplays()
    {
        $this->requireAuth("client");
        $user = $this->getCurrentUser();

        $stmt = $this->db->prepare("
            SELECT
                id,
                client_id,
                display_name,
                display_code,
                last_heartbeat,
                CASE
                    WHEN last_heartbeat IS NOT NULL
                         AND datetime(last_heartbeat, '+5 minutes') >= datetime('now')
                    THEN 1 ELSE 0
                END AS is_online
            FROM client_displays
            WHERE client_id = ?
            ORDER BY display_name
        ");
        $stmt->execute([$user["id"]]);
        $displays = $stmt->fetchAll();

        $this->view("client/displays", ["displays" => $displays]);
    }

    public function addDisplay()
    {
        $this->requireAuth("client");

        if ($_SERVER["REQUEST_METHOD"] !== "POST") {
            $this->redirect("/client/displays");
        }

        $user = $this->getCurrentUser();
        $display_name = $_POST["display_name"] ?? "";

        if (empty($display_name)) {
            $this->view("client/displays", [
                "error" => "Nazwa wyświetlacza jest wymagana",
            ]);
            return;
        }

        // Generuj unikalny kod wyświetlacza
        $display_code = $this->generateDisplayCode();

        $stmt = $this->db->prepare("
            INSERT INTO client_displays (client_id, display_name, display_code, is_online)
            VALUES (?, ?, ?, 0)
        ");

        if ($stmt->execute([$user["id"], $display_name, $display_code])) {
            $_SESSION["flash_message"] = "Wyświetlacz został dodany pomyślnie.";
            $this->redirect("/client/displays");
        }
    }

    public function deleteDisplay($id)
    {
        $this->requireAuth("client");
        $user = $this->getCurrentUser();

        // Sprawdź czy wyświetlacz należy do bieżącego użytkownika
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM client_displays
            WHERE id = ? AND client_id = ?
        ");
        $stmt->execute([$id, $user["id"]]);

        if ($stmt->fetchColumn() == 0) {
            $_SESSION["flash_message"] =
                "Nie masz uprawnień do usunięcia tego wyświetlacza.";
            $this->redirect("/client/displays");
        }

        $stmt = $this->db->prepare("DELETE FROM client_displays WHERE id = ?");

        if ($stmt->execute([$id])) {
            $_SESSION["flash_message"] = "Wyświetlacz został usunięty.";
        } else {
            $_SESSION["flash_message"] =
                "Wystąpił błąd podczas usuwania wyświetlacza.";
        }

        $this->redirect("/client/displays");
    }

    public function displayPublic($kod)
    {
        // Kod powinien być zawsze małymi literami
        $kod = strtolower($kod);

        // Pobierz informacje o wyświetlaczu
        $stmt = $this->db->prepare("
            SELECT d.*, u.id as client_id, u.company_name
            FROM client_displays d
            JOIN users u ON d.client_id = u.id
            WHERE d.display_code = ?
        ");
        $stmt->execute([$kod]);
        $display = $stmt->fetch();

        if (!$display) {
            // Jeśli wyświetlacz nie istnieje, wyświetl informację o błędzie
            http_response_code(404);
            $this->view(
                "client/display_error",
                [
                    "message" => "Wyświetlacz o podanym kodzie nie istnieje.",
                ],
                false,
            ); // Nie pokazuj szablonu z menu
            return;
        }

        // Aktualizuj status wyświetlacza na online
        $stmt = $this->db->prepare("
            UPDATE client_displays
            SET is_online = 1, last_heartbeat = datetime('now')
            WHERE id = ?
        ");
        $stmt->execute([$display["id"]]);

        // Pobierz kampanie dostępne dla tego klienta
        $stmt = $this->db->prepare("
            SELECT c.*, u.company_name as advertiser_name
            FROM campaigns c
            JOIN users u ON c.advertiser_id = u.id
            JOIN campaign_assignments ca ON c.id = ca.campaign_id
            WHERE ca.client_id = ?
            AND ca.is_accepted = 1
            AND c.is_active = 1
            AND c.budget > c.spent
            AND c.start_date <= datetime('now')
            AND c.end_date >= datetime('now')
        ");
        $stmt->execute([$display["client_id"]]);
        $campaigns = $stmt->fetchAll();

        // Usuń sprawdzanie file_exists() - pozwól przeglądarce obsłużyć błędy
        $validCampaigns = $campaigns;

        // System kolejkowy zawsze włączony dla wyświetlaczy
        $queueEnabled = true;
        $queueSystem = new QueueSystem();
        // Sprawdzamy tylko dla statystyk, ale nie używamy wartości
        $systemStatus = $queueSystem->isEnabled($display["client_id"]);

        // Przekazujemy false jako trzeci argument, aby nie pokazywać szablonu z menu
        $this->view(
            "client/display_public",
            [
                "display" => $display,
                "campaigns" => $validCampaigns,
                "queueEnabled" => $queueEnabled,
            ],
            false,
        );
    }

    // Helper do generowania unikalnego kodu wyświetlacza
    private function generateDisplayCode($length = 6)
    {
        $characters = "abcdefghijklmnopqrstuvwxyz";
        $code = "";

        do {
            $code = "";
            for ($i = 0; $i < $length; $i++) {
                $code .= $characters[rand(0, strlen($characters) - 1)];
            }

            // Sprawdź czy kod jest unikalny
            $stmt = $this->db->prepare(
                "SELECT COUNT(*) FROM client_displays WHERE display_code = ?",
            );
            $stmt->execute([$code]);
        } while ($stmt->fetchColumn() > 0);

        return $code;
    }

    public function availableCampaigns()
    {
        $this->requireAuth("client");
        $user = $this->getCurrentUser();

        // Pobierz dostępne kampanie
        $stmt = $this->db->prepare("
            SELECT c.*, u.company_name as advertiser_name, cat.name as category_name,
                   ca.is_accepted
            FROM campaigns c
            JOIN users u ON c.advertiser_id = u.id
            LEFT JOIN categories cat ON c.category_id = cat.id
            LEFT JOIN campaign_assignments ca ON c.id = ca.campaign_id AND ca.client_id = ?
            WHERE c.is_active = 1 AND c.budget > c.spent
            ORDER BY c.created_at DESC
        ");
        $stmt->execute([$user["id"]]);
        $campaigns = $stmt->fetchAll();

        // Pobierz kategorie z automatyczną akceptacją
        $stmt = $this->db->prepare("
            SELECT category_id FROM campaign_auto_accept WHERE client_id = ?
        ");
        $stmt->execute([$user["id"]]);
        $autoAcceptCategories = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // Pobierz wszystkie kategorie
        $stmt = $this->db->query(
            "SELECT id, name FROM categories ORDER BY name",
        );
        $categories = $stmt->fetchAll();

        $this->view("client/available_campaigns", [
            "campaigns" => $campaigns,
            "categories" => $categories,
            "autoAcceptCategories" => $autoAcceptCategories,
        ]);
    }

    public function joinCampaign()
    {
        $this->requireAuth("client");

        if ($_SERVER["REQUEST_METHOD"] !== "POST") {
            $this->redirect("/client/available-campaigns");
        }

        $user = $this->getCurrentUser();
        $campaignId = $_POST["campaign_id"] ?? 0;

        if (empty($campaignId)) {
            $_SESSION["flash_message"] = "Nieprawidłowy identyfikator kampanii";
            $this->redirect("/client/available-campaigns");
        }

        // Sprawdź czy kampania istnieje i jest aktywna
        $stmt = $this->db->prepare("
            SELECT c.*, cat.id as category_id
            FROM campaigns c
            LEFT JOIN categories cat ON c.category_id = cat.id
            WHERE c.id = ? AND c.is_active = 1
        ");
        $stmt->execute([$campaignId]);
        $campaign = $stmt->fetch();

        if (!$campaign) {
            $_SESSION["flash_message"] =
                "Kampania nie istnieje lub jest nieaktywna";
            $this->redirect("/client/available-campaigns");
        }

        // Aktualizuj status akceptacji
        if (isset($_POST["accept"])) {
            $isAccepted = 1;
            $message = "Kampania została zaakceptowana";
        } elseif (isset($_POST["reject"])) {
            $isAccepted = 0;
            $message = "Kampania została odrzucona";
        } else {
            $this->redirect("/client/available-campaigns");
            return;
        }

        // Sprawdź czy przypisanie już istnieje
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM campaign_assignments
            WHERE campaign_id = ? AND client_id = ?
        ");
        $stmt->execute([$campaignId, $user["id"]]);
        $exists = $stmt->fetchColumn() > 0;

        if ($exists) {
            // Aktualizuj istniejące przypisanie
            $stmt = $this->db->prepare("
                UPDATE campaign_assignments
                SET is_accepted = ?
                WHERE campaign_id = ? AND client_id = ?
            ");
            $stmt->execute([$isAccepted, $campaignId, $user["id"]]);
        } else {
            // Dodaj nowe przypisanie
            $stmt = $this->db->prepare("
                INSERT INTO campaign_assignments (campaign_id, client_id, is_accepted)
                VALUES (?, ?, ?)
            ");
            $stmt->execute([$campaignId, $user["id"], $isAccepted]);
        }

        $_SESSION["flash_message"] = $message;
        $this->redirect("/client/available-campaigns");
    }

    public function updateAutoAccept()
    {
        $this->requireAuth("client");

        if ($_SERVER["REQUEST_METHOD"] !== "POST") {
            $this->redirect("/client/available-campaigns");
        }

        $user = $this->getCurrentUser();
        $autoAcceptCategories = $_POST["auto_accept_categories"] ?? [];

        // Usuń wszystkie dotychczasowe automatyczne akceptacje
        $stmt = $this->db->prepare(
            "DELETE FROM campaign_auto_accept WHERE client_id = ?",
        );
        $stmt->execute([$user["id"]]);

        // Dodaj nowe automatyczne akceptacje
        if (!empty($autoAcceptCategories)) {
            $stmt = $this->db->prepare("
                INSERT INTO campaign_auto_accept (client_id, category_id)
                VALUES (?, ?)
            ");

            foreach ($autoAcceptCategories as $categoryId) {
                $stmt->execute([$user["id"], $categoryId]);
            }
        }

        $_SESSION["flash_message"] =
            "Ustawienia automatycznej akceptacji zostały zaktualizowane";
        $this->redirect("/client/available-campaigns");
    }

    public function getDisplaysStatus()
    {
        $this->requireAuth("client");
        $user = $this->getCurrentUser();

        // Najpierw oznacz jako offline wszystkie wyświetlacze, które nie wysłały heartbeat w ciągu ostatnich 5 minut
        $stmt = $this->db->prepare("
            UPDATE client_displays
            SET is_online = 0
            WHERE client_id = ? AND (last_heartbeat IS NULL OR datetime(last_heartbeat, '+5 minutes') < datetime('now'))
        ");
        $stmt->execute([$user["id"]]);

        // Teraz pobierz aktualne statusy (wyliczane dynamicznie z last_heartbeat)
        $stmt = $this->db->prepare("
            SELECT
                id,
                CASE
                    WHEN last_heartbeat IS NOT NULL
                         AND datetime(last_heartbeat, '+5 minutes') >= datetime('now')
                    THEN 1 ELSE 0
                END AS is_online,
                CASE WHEN last_heartbeat IS NOT NULL
                     THEN datetime(last_heartbeat, 'localtime')
                     ELSE NULL
                END as last_heartbeat
            FROM client_displays
            WHERE client_id = ?
        ");
        $stmt->execute([$user["id"]]);
        $displays = $stmt->fetchAll();

        $this->json(["displays" => $displays]);
    }
}
