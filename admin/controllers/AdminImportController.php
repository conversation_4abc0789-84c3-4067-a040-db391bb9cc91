<?php

class AdminImportController extends Controller {
    private $importSettingModel;
    private $externalDoctorMappingModel;
    private $userModel;

    public function __construct() {
        parent::__construct();

        require_once 'models/ImportSetting.php';
        require_once 'models/ExternalDoctorMapping.php';
        require_once 'models/User.php';

        $this->importSettingModel = new ImportSetting();
        $this->externalDoctorMappingModel = new ExternalDoctorMapping();
        $this->userModel = new User();
    }

    /**
     * Lista wszystkich ustawień importu
     */
    public function index() {
        $this->requireAuth('admin');

        $importSettings = $this->importSettingModel->getWithClientInfo();

        $this->view('admin/import/index', [
            'importSettings' => $importSettings
        ]);
    }

    /**
     * Formularz dodawania nowego ustawienia importu
     */
    public function create() {
        $this->requireAuth('admin');

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $data = [
                'client_id' => $_POST['client_id'],
                'system_name' => $_POST['system_name'],
                'sync_code' => $_POST['sync_code'] ?: $this->importSettingModel->generateSyncCode(),
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'api_endpoint' => $_POST['api_endpoint'] ?: null,
                'sync_frequency' => $_POST['sync_frequency'] ?: 3600
            ];

            // Sprawdź unikalność kodu synchronizacji
            if (!$this->importSettingModel->isSyncCodeUnique($data['sync_code'])) {
                $this->setError('Kod synchronizacji już istnieje. Wygeneruj nowy kod.');
                $clients = $this->userModel->getClients();
                $this->view('admin/import/create', [
                    'clients' => $clients,
                    'data' => $data
                ]);
                return;
            }

            if ($this->importSettingModel->create($data)) {
                $this->setSuccess('Ustawienie importu zostało utworzone pomyślnie.');
                $this->redirect('admin/import');
            } else {
                $this->setError('Wystąpił błąd podczas tworzenia ustawienia importu.');
            }
        }

        $clients = $this->userModel->getClients();
        $this->view('admin/import/create', [
            'clients' => $clients
        ]);
    }

    /**
     * Formularz edycji ustawienia importu
     */
    public function edit($id = null) {
        $this->requireAuth('admin');

        if (!$id) {
            $this->redirect('admin/import');
        }

        $importSetting = $this->importSettingModel->getByIdWithClient($id);
        if (!$importSetting) {
            $this->setError('Ustawienie importu nie zostało znalezione.');
            $this->redirect('admin/import');
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $data = [
                'system_name' => $_POST['system_name'],
                'sync_code' => $_POST['sync_code'],
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'api_endpoint' => $_POST['api_endpoint'] ?: null,
                'sync_frequency' => $_POST['sync_frequency'] ?: 3600
            ];

            // Sprawdź unikalność kodu synchronizacji
            if (!$this->importSettingModel->isSyncCodeUnique($data['sync_code'], $id)) {
                $this->setError('Kod synchronizacji już istnieje. Wybierz inny kod.');
                $clients = $this->userModel->getClients();
                $this->view('admin/import/edit', [
                    'importSetting' => $importSetting,
                    'clients' => $clients,
                    'data' => $data
                ]);
                return;
            }

            if ($this->importSettingModel->update($id, $data)) {
                $this->setSuccess('Ustawienie importu zostało zaktualizowane pomyślnie.');
                $this->redirect('admin/import');
            } else {
                $this->setError('Wystąpił błąd podczas aktualizacji ustawienia importu.');
            }
        }

        $clients = $this->userModel->getClients();
        $this->view('admin/import/edit', [
            'importSetting' => $importSetting,
            'clients' => $clients
        ]);
    }

    /**
     * Usuwanie ustawienia importu
     */
    public function delete($id = null) {
        $this->requireAuth('admin');

        if (!$id) {
            $this->redirect('admin/import');
        }

        if ($this->importSettingModel->delete($id)) {
            $this->setSuccess('Ustawienie importu zostało usunięte pomyślnie.');
        } else {
            $this->setError('Wystąpił błąd podczas usuwania ustawienia importu.');
        }

        $this->redirect('admin/import');
    }

    /**
     * Zarządzanie mapowaniami lekarzy dla ustawienia importu
     */
    public function doctorMappings($importSettingId = null) {
        $this->requireAuth('admin');

        if (!$importSettingId) {
            $this->redirect('admin/import');
        }

        $importSetting = $this->importSettingModel->getByIdWithClient($importSettingId);
        if (!$importSetting) {
            $this->setError('Ustawienie importu nie zostało znalezione.');
            $this->redirect('admin/import');
        }

        $mappings = $this->externalDoctorMappingModel->getByImportSettingId($importSettingId);
        $systemDoctors = $this->externalDoctorMappingModel->getAvailableSystemDoctors($importSetting['client_id']);

        $this->view('admin/import/doctor_mappings', [
            'importSetting' => $importSetting,
            'mappings' => $mappings,
            'systemDoctors' => $systemDoctors
        ]);
    }

    /**
     * Aktualizacja mapowania lekarza
     */
    public function updateDoctorMapping() {
        $this->requireAuth('admin');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('admin/import');
        }

        $mappingId = $_POST['mapping_id'];
        $systemDoctorId = $_POST['system_doctor_id'] ?: null;

        $data = [
            'system_doctor_id' => $systemDoctorId,
            'is_mapped' => $systemDoctorId ? 1 : 0
        ];

        if ($this->externalDoctorMappingModel->update($mappingId, $data)) {
            $this->setSuccess('Mapowanie lekarza zostało zaktualizowane pomyślnie.');
        } else {
            $this->setError('Wystąpił błąd podczas aktualizacji mapowania.');
        }

        $this->redirect('admin/import/doctor-mappings/' . $_POST['import_setting_id']);
    }

    /**
     * Usuwanie mapowania lekarza
     */
    public function deleteDoctorMapping() {
        $this->requireAuth('admin');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('admin/import');
        }

        $mappingId = $_POST['mapping_id'];
        $importSettingId = $_POST['import_setting_id'];

        // Sprawdź czy mapowanie istnieje
        $mapping = $this->externalDoctorMappingModel->getById($mappingId);
        if (!$mapping || $mapping['import_setting_id'] != $importSettingId) {
            $this->setError('Mapowanie nie zostało znalezione.');
            $this->redirect('admin/import/doctor-mappings/' . $importSettingId);
        }

        // Usuń mapowanie
        if ($this->externalDoctorMappingModel->delete($mappingId)) {
            $this->setSuccess('Mapowanie zostało usunięte pomyślnie.');
        } else {
            $this->setError('Wystąpił błąd podczas usuwania mapowania.');
        }

        $this->redirect('admin/import/doctor-mappings/' . $importSettingId);
    }

    /**
     * Generowanie nowego kodu synchronizacji
     */
    public function generateSyncCode() {
        $this->requireAuth('admin');

        $syncCode = $this->importSettingModel->generateSyncCode();

        header('Content-Type: application/json');
        echo json_encode(['sync_code' => $syncCode]);
    }

    /**
     * Test połączenia z systemem zewnętrznym
     */
    public function testConnection() {
        $this->requireAuth('admin');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('admin/import');
        }

        $importSettingId = $_POST['import_setting_id'];
        $importSetting = $this->importSettingModel->getById($importSettingId);

        if (!$importSetting) {
            $this->setError('Ustawienie importu nie zostało znalezione.');
            $this->redirect('admin/import');
        }

        // Tutaj można dodać logikę testowania połączenia
        // Na razie zwracamy sukces
        $this->setSuccess('Połączenie z systemem zewnętrznym zostało przetestowane pomyślnie.');
        $this->redirect('admin/import/doctor-mappings/' . $importSettingId);
    }
}
