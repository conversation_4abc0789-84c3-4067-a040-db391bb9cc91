<?php

class AdvertiserController extends Controller {
    
    public function dashboard() {
        $this->requireAuth('advertiser');
        $user = $this->getCurrentUser();
        
        // Statystyki kampanii
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as total_campaigns, 
                   SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_campaigns,
                   SUM(budget) as total_budget,
                   SUM(spent) as total_spent
            FROM campaigns 
            WHERE advertiser_id = ?
        ");
        $stmt->execute([$user['id']]);
        $campaignStats = $stmt->fetch();
        
        // Statystyki wyświetleń
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as total_views, SUM(cost) as total_cost
            FROM ad_views av
            JOIN campaigns c ON av.campaign_id = c.id
            WHERE c.advertiser_id = ?
        ");
        $stmt->execute([$user['id']]);
        $viewStats = $stmt->fetch();
        
        $this->view('advertiser/dashboard', [
            'user' => $user,
            'campaignStats' => $campaignStats,
            'viewStats' => $viewStats
        ]);
    }
    
    public function campaigns() {
        $this->requireAuth('advertiser');
        $user = $this->getCurrentUser();
        
        $stmt = $this->db->prepare("
            SELECT c.*,
                   (SELECT COUNT(*) FROM ad_views av WHERE av.campaign_id = c.id) as views
            FROM campaigns c
            WHERE c.advertiser_id = ?
            ORDER BY c.created_at DESC
        ");
        $stmt->execute([$user['id']]);
        $campaigns = $stmt->fetchAll();
        
        $this->view('advertiser/campaigns', ['campaigns' => $campaigns]);
    }
    
    public function createCampaign() {
        $this->requireAuth('advertiser');
        
        $stmt = $this->db->query("
            SELECT id, username, company_name 
            FROM users 
            WHERE role = 'client' AND is_active = 1
        ");
        $clients = $stmt->fetchAll();
        
        $this->view('advertiser/create_campaign', ['clients' => $clients]);
    }
    
    public function storeCampaign() {
        $this->requireAuth('advertiser');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/advertiser/campaigns/create');
        }
        
        $user = $this->getCurrentUser();
        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $media_type = $_POST['media_type'] ?? '';
        $budget = floatval($_POST['budget'] ?? 0);
        $rate_per_second = floatval($_POST['rate_per_second'] ?? 0.0001);
        $max_frequency_per_hour = intval($_POST['max_frequency_per_hour'] ?? 0);
        $start_date = $_POST['start_date'] ?? '';
        $end_date = $_POST['end_date'] ?? '';
        $client_ids = $_POST['client_ids'] ?? [];
        
        $errors = [];
        
        if (empty($name)) $errors[] = 'Nazwa kampanii jest wymagana';
        if (empty($media_type)) $errors[] = 'Typ mediów jest wymagany';
        if ($budget <= 0) $errors[] = 'Budżet musi być większy od 0';
        if (empty($client_ids)) $errors[] = 'Wybierz przynajmniej jednego klienta';
        
        if (!empty($errors)) {
            $stmt = $this->db->query("SELECT id, username, company_name FROM users WHERE role = 'client' AND is_active = 1");
            $clients = $stmt->fetchAll();
            
            $this->view('advertiser/create_campaign', [
                'clients' => $clients,
                'errors' => $errors,
                'data' => $_POST
            ]);
            return;
        }
        
        // Zapisz kampanię
        $stmt = $this->db->prepare("
            INSERT INTO campaigns (advertiser_id, name, description, media_type, media_url, 
                                 budget, rate_per_second, max_frequency_per_hour, start_date, end_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        if ($stmt->execute([
            $user['id'], $name, $description, $media_type, 'temp_url',
            $budget, $rate_per_second, $max_frequency_per_hour, $start_date, $end_date
        ])) {
            $campaign_id = $this->db->lastInsertId();
            
            // Przypisz klientów
            $stmt = $this->db->prepare("INSERT INTO campaign_assignments (campaign_id, client_id) VALUES (?, ?)");
            foreach ($client_ids as $client_id) {
                $stmt->execute([$campaign_id, $client_id]);
            }
            
            $this->redirect('/advertiser/campaigns');
        }
    }
    
    public function showCampaign($id) {
        $this->requireAuth('advertiser');
        $user = $this->getCurrentUser();
        
        $stmt = $this->db->prepare("
            SELECT c.*, 
                   (SELECT COUNT(*) FROM ad_views av WHERE av.campaign_id = c.id) as total_views
            FROM campaigns c
            WHERE c.id = ? AND c.advertiser_id = ?
        ");
        $stmt->execute([$id, $user['id']]);
        $campaign = $stmt->fetch();
        
        if (!$campaign) {
            $this->redirect('/advertiser/campaigns');
        }
        
        $this->view('advertiser/show_campaign', ['campaign' => $campaign]);
    }
    
    public function statistics() {
        $this->requireAuth('advertiser');
        $user = $this->getCurrentUser();
        
        $stmt = $this->db->prepare("
            SELECT DATE(av.timestamp) as date, COUNT(*) as views, SUM(av.cost) as revenue
            FROM ad_views av
            JOIN campaigns c ON av.campaign_id = c.id
            WHERE c.advertiser_id = ? AND av.timestamp >= date('now', '-30 days')
            GROUP BY DATE(av.timestamp)
            ORDER BY date DESC
        ");
        $stmt->execute([$user['id']]);
        $dailyStats = $stmt->fetchAll();
        
        $this->view('advertiser/statistics', ['dailyStats' => $dailyStats]);
    }
} 