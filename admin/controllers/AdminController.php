<?php

class AdminController extends Controller {
    private $userController;
    private $campaignController;
    private $statisticController;
    private $statisticModel;
    private $userModel;

    public function __construct() {
        parent::__construct();

        // Inicjalizacja modeli
        require_once 'models/Statistic.php';
        require_once 'models/User.php';
        $this->statisticModel = new Statistic();
        $this->userModel = new User();

        // Inicjalizacja kontrolerów
        require_once 'controllers/AdminUserController.php';
        require_once 'controllers/AdminCampaignController.php';
        require_once 'controllers/AdminStatisticController.php';
        require_once 'controllers/AdminImportController.php';
        $this->userController = new AdminUserController();
        $this->campaignController = new AdminCampaignController();
        $this->statisticController = new AdminStatisticController();
        $this->importController = new AdminImportController();
    }

    public function dashboard() {
        $this->requireAuth('admin');

        // Statystyki ogólne
        $stats = $this->statisticModel->getDashboardStats();

        // Status klientów online
        $clientStatus = $this->userModel->getOnlineStatus();

        // Dodanie danych dla systemu kolejkowego
        require_once 'models/QueueSystem.php';
        $queueSystem = new QueueSystem();

        // Pobierz liczbę gabinetów
        $roomCount = $this->db->query("SELECT COUNT(*) FROM queue_rooms")->fetchColumn();

        // Pobierz liczbę dzisiejszych wizyt
        $todayAppointments = $this->db->query("
            SELECT COUNT(*) FROM queue_appointments
            WHERE date(appointment_date) = date('now')
        ")->fetchColumn();

        // Pobierz liczbę aktywnych wyświetlaczy
        $activeDisplays = $this->db->query("
            SELECT COUNT(*) FROM client_displays
            WHERE last_heartbeat > datetime('now', '-5 minutes')
        ")->fetchColumn();

        // Dodaj informacje o gabinetach i statusie systemu do każdego klienta
        foreach ($clientStatus as &$client) {
            // Liczba gabinetów
            $stmt = $this->db->prepare("
                SELECT COUNT(*) FROM queue_rooms
                WHERE client_id = ?
            ");
            $stmt->execute([$client['id']]);
            $client['rooms_count'] = $stmt->fetchColumn();

            // Status systemu kolejkowego
            $client['queue_enabled'] = $queueSystem->isEnabled($client['id']);

            // Liczba dzisiejszych wizyt
            $stmt = $this->db->prepare("
                SELECT COUNT(*) FROM queue_appointments
                WHERE client_id = ? AND date(appointment_date) = date('now')
            ");
            $stmt->execute([$client['id']]);
            $client['today_appointments'] = $stmt->fetchColumn();
        }

        $this->view('admin/dashboard', [
            'stats' => $stats,
            'clientStatus' => $clientStatus,
            'roomCount' => $roomCount,
            'todayAppointments' => $todayAppointments,
            'activeDisplays' => $activeDisplays
        ]);
    }

    // Przekierowanie do kontrolera użytkowników

    public function users() {
        $this->userController->index();
    }

    public function addUser() {
        $this->userController->add();
    }

    public function editUser($id = null) {
        $this->userController->edit($id);
    }

    public function deleteUser($id = null) {
        $this->userController->delete($id);
    }

    // Przekierowanie do kontrolera importu
    public function import() {
        $this->importController->index();
    }

    public function createImport() {
        $this->importController->create();
    }

    public function editImport($id = null) {
        $this->importController->edit($id);
    }

    public function deleteImport($id = null) {
        $this->importController->delete($id);
    }

    public function doctorMappings($importSettingId = null) {
        $this->importController->doctorMappings($importSettingId);
    }

    public function updateDoctorMapping() {
        $this->importController->updateDoctorMapping();
    }

    public function deleteDoctorMapping() {
        $this->importController->deleteDoctorMapping();
    }

    public function generateSyncCode() {
        $this->importController->generateSyncCode();
    }

    public function testConnection() {
        $this->importController->testConnection();
    }

    // Przekierowanie do kontrolera kampanii

    public function campaigns() {
        $this->campaignController->index();
    }

    public function addCampaignForm() {
        $this->campaignController->add();
    }

    public function addCampaign() {
        $this->campaignController->add();
    }

    public function editCampaignForm($id) {
        $this->campaignController->edit($id);
    }

    public function editCampaign($id) {
        $this->campaignController->edit($id);
    }

    public function deleteCampaign($id = null) {
        $this->campaignController->delete($id);
    }

    public function forceAcceptCampaign($id = null) {
        $this->campaignController->forceAccept($id);
    }

    // Przekierowanie do kontrolera statystyk

    public function statistics() {
        $this->requireAuth('admin');

        // Pobierz statystyki wyświetleń
        $stmt = $this->db->query("
            SELECT DATE(timestamp) as date, COUNT(*) as views, SUM(cost) as revenue
            FROM ad_views
            WHERE timestamp >= date('now', '-30 days')
            GROUP BY DATE(timestamp)
            ORDER BY date DESC
        ");
        $dailyStats = $stmt->fetchAll();

        $this->view('admin/statistics', ['dailyStats' => $dailyStats]);
    }

    // Funkcja pomocnicza - oblicza czas który upłynął
    private function timeElapsed($timestamp) {
        $now = time();
        $diff = $now - $timestamp;

        if ($diff < 60) {
            return 'przed chwilą';
        } else if ($diff < 3600) {
            $minutes = floor($diff / 60);
            return $minutes . ' min. temu';
        } else if ($diff < 86400) {
            $hours = floor($diff / 3600);
            return $hours . ' godz. temu';
        } else {
            $days = floor($diff / 86400);
            return $days . ' dni temu';
        }
    }

    // Zarządzanie systemem kolejkowym dla klientów
    public function queueManagement() {
        $this->requireAuth('admin');

        // Pobierz listę klientów z informacją o liczbie sal i statusie systemu kolejkowego
        $stmt = $this->db->query("
            SELECT u.*,
                   (SELECT COUNT(*) FROM queue_rooms qr WHERE qr.client_id = u.id) as rooms_count,
                   (SELECT is_enabled FROM queue_config qc WHERE qc.client_id = u.id) as queue_enabled
            FROM users u
            WHERE u.role = 'client'
        ");

        $clients = $stmt->fetchAll();

        // Pobierz dodatkowe statystyki dla każdego klienta
        foreach ($clients as &$client) {
            // Określ czy klient jest online
            $client['is_online'] = (isset($client['last_heartbeat']) &&
                strtotime($client['last_heartbeat']) > strtotime('-5 minutes'));

            // Czas od ostatniej aktywności
            if (isset($client['last_heartbeat'])) {
                $client['time_elapsed'] = $this->timeElapsed(strtotime($client['last_heartbeat']));
            }

            // Liczba dzisiejszych wizyt
            $stmt = $this->db->prepare("
                SELECT COUNT(*) FROM queue_appointments
                WHERE client_id = ? AND date(appointment_date) = date('now')
            ");
            $stmt->execute([$client['id']]);
            $client['today_appointments'] = $stmt->fetchColumn();

            // Liczba zakończonych wizyt
            $stmt = $this->db->prepare("
                SELECT COUNT(*) FROM queue_appointments
                WHERE client_id = ? AND date(appointment_date) = date('now') AND status = 'completed'
            ");
            $stmt->execute([$client['id']]);
            $client['completed_appointments'] = $stmt->fetchColumn();

            // Liczba aktywnych wyświetlaczy
            $stmt = $this->db->prepare("
                SELECT COUNT(*) FROM displays
                WHERE client_id = ? AND last_heartbeat > datetime('now', '-5 minutes')
            ");
            $stmt->execute([$client['id']]);
            $client['displays_count'] = $stmt->fetchColumn();
        }

        // Statystyki ogólne
        $stats = [
            'active_clients' => $this->db->query("
                SELECT COUNT(*) FROM queue_config WHERE is_enabled = 1
            ")->fetchColumn(),

            'total_rooms' => $this->db->query("
                SELECT COUNT(*) FROM queue_rooms
            ")->fetchColumn(),

            'appointments_today' => $this->db->query("
                SELECT COUNT(*) FROM queue_appointments
                WHERE date(appointment_date) = date('now')
            ")->fetchColumn(),

            'active_displays' => $this->db->query("
                SELECT COUNT(*) FROM displays
                WHERE last_heartbeat > datetime('now', '-5 minutes')
            ")->fetchColumn()
        ];

        // Dane paginacji
        $pagination = [
            'current_page' => 1,
            'total_pages' => 1,
            'query_string' => ''
        ];

        // Globalne ustawienia systemu kolejkowego
        $settings = [
            'default_queue_mode' => 'mixed',
            'appointment_duration' => 15,
            'display_refresh_rate' => 30,
            'queue_per_page' => 10,
            'enable_queue_notifications' => true,
            'show_estimated_wait_time' => true
        ];

        $this->view('admin/queue_management', [
            'clients' => $clients,
            'stats' => $stats,
            'pagination' => $pagination,
            'settings' => $settings
        ]);
    }

    // Włączanie/wyłączanie systemu kolejkowego dla klienta
    public function toggleQueueSystem() {
        $this->requireAuth('admin');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/admin/queue-management');
            return;
        }

        $clientId = $_POST['client_id'] ?? 0;
        $isEnabled = isset($_POST['is_enabled']) && $_POST['is_enabled'] == 1;

        if (!$clientId) {
            $_SESSION['flash_message'] = 'Nie wybrano klienta.';
            $this->redirect('/admin/queue-management');
            return;
        }

        // Sprawdź czy klient istnieje
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM users WHERE id = ? AND role = 'client'");
        $stmt->execute([$clientId]);

        if ($stmt->fetchColumn() == 0) {
            $_SESSION['flash_message'] = 'Wybrany klient nie istnieje.';
            $this->redirect('/admin/queue-management');
            return;
        }

        // Zaktualizuj lub utwórz konfigurację systemu kolejkowego
        $queueSystem = new QueueSystem();
        if ($queueSystem->toggleSystem($clientId, $isEnabled)) {
            $_SESSION['flash_message'] = 'Ustawienia systemu kolejkowego zostały zaktualizowane.';
        } else {
            $_SESSION['flash_message'] = 'Wystąpił błąd podczas aktualizacji ustawień systemu kolejkowego.';
        }

        $this->redirect('/admin/queue-management');
    }

    // Zarządzanie salami dla danego klienta
    // Zarządzanie salami klienta
    public function clientRooms($clientId) {
        $this->requireAuth('admin');

        // Pobierz dane klienta
        $stmt = $this->db->prepare("SELECT * FROM users WHERE id = ? AND role = 'client'");
        $stmt->execute([$clientId]);
        $client = $stmt->fetch();

        if (!$client) {
            $_SESSION['flash_message'] = 'Wybrany klient nie istnieje.';
            $this->redirect('/admin/queue-management');
            return;
        }

        // Pobierz status systemu kolejkowego
        require_once 'models/QueueSystem.php';
        $queueSystem = new QueueSystem();
        $client['queue_enabled'] = $queueSystem->isEnabled($clientId);

        // Pobierz sale klienta
        $stmt = $this->db->prepare("
            SELECT qr.*,
                   (SELECT COUNT(*) FROM queue_appointments qa
                    WHERE qa.room_id = qr.id AND date(qa.appointment_date) = date('now')) as appointments_count,
                   (SELECT status FROM queue_appointments qa
                    WHERE qa.room_id = qr.id AND date(qa.appointment_date) = date('now')
                    AND qa.status = 'serving' LIMIT 1) as current_queue_status
            FROM queue_rooms qr
            WHERE qr.client_id = ?
            ORDER BY qr.name
        ");
        $stmt->execute([$clientId]);
        $rooms = $stmt->fetchAll();

        // Statystyki
        $db = $this->db;
        $stats = [
            'today_appointments' => (function () use ($clientId, $db) {
                $stmt = $db->prepare("
                    SELECT COUNT(*) FROM queue_appointments
                    WHERE client_id = ? AND date(appointment_date) = date('now')
                ");
                $stmt->execute([$clientId]);
                return $stmt->fetchColumn();
            })(),

            'completed_appointments' => (function () use ($clientId, $db) {
                $stmt = $db->prepare("
                    SELECT COUNT(*) FROM queue_appointments
                    WHERE client_id = ? AND date(appointment_date) = date('now') AND status = 'completed'
                ");
                $stmt->execute([$clientId]);
                return $stmt->fetchColumn();
            })(),

            'waiting_appointments' => (function () use ($clientId, $db) {
                $stmt = $db->prepare("
                    SELECT COUNT(*) FROM queue_appointments
                    WHERE client_id = ? AND date(appointment_date) = date('now') AND status = 'waiting'
                ");
                $stmt->execute([$clientId]);
                return $stmt->fetchColumn();
            })(),

            'active_displays' => (function () use ($clientId, $db) {
                $stmt = $db->prepare("
                    SELECT COUNT(*) FROM displays
                    WHERE client_id = ? AND last_heartbeat > datetime('now', '-5 minutes')
                ");
                $stmt->execute([$clientId]);
                return $stmt->fetchColumn();
            })()
        ];

        $this->view('admin/client_rooms', [
            'client' => $client,
            'rooms' => $rooms,
            'stats' => $stats
        ]);
    }
}
