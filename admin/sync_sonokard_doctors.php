<?php
require_once 'core/Database.php';

class SonokardDoctorSync {
    private $db;
    private $sonokardClientId = 2; // ID klienta Sonokard w bazie

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    /**
     * Pobiera dane lekarzy ze strony sonokard.pl/zespol
     */
    public function fetchDoctorsFromWebsite() {
        $url = 'https://sonokard.pl/zespol';

        // Użyj cURL dla lepszej kontroli
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language: pl-PL,pl;q=0.9,en;q=0.8',
            'Connection: keep-alive'
        ]);
        curl_setopt($ch, CURLOPT_ENCODING, 'gzip, deflate'); // Automatyczna dekompresja
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $html = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($html === false || !empty($error)) {
            throw new Exception("Błąd cURL: $error");
        }

        if ($httpCode !== 200) {
            throw new Exception("HTTP Error: $httpCode");
        }

        echo "Debug: Pobrano HTML o długości " . strlen($html) . " znaków\n";

        // Sprawdź czy dane są skompresowane i zdekompresuj jeśli potrzeba
        if (substr($html, 0, 2) === "\x1f\x8b") {
            echo "Debug: Dane są skompresowane gzip, dekompresja...\n";
            $html = gzdecode($html);
            if ($html === false) {
                throw new Exception("Błąd dekompresji gzip");
            }
            echo "Debug: Po dekompresji: " . strlen($html) . " znaków\n";
        }

        // Zapisz HTML do pliku dla debugowania
        file_put_contents('debug_html.txt', $html);
        echo "Debug: HTML zapisano do debug_html.txt\n";

        return $this->parseDoctorsFromHtml($html);
    }

    /**
     * Parsuje HTML i wyciąga dane lekarzy
     */
    private function parseDoctorsFromHtml($html) {
        // Użyj danych z web-fetch jako fallback
        $knownDoctors = [
            ['first_name' => 'dr n. med. Małgorzata', 'last_name' => 'Olesiak-Andryszczak', 'specialization' => 'Specjalista ginekologii i położnictwa, Specjalista perinatologii'],
            ['first_name' => 'lek. Ewelina', 'last_name' => 'Sądaj', 'specialization' => 'Specjalista ginekologii i położnictwa'],
            ['first_name' => 'lek. Natalia', 'last_name' => 'Kubat', 'specialization' => 'Specjalista ginekologii i położnictwa, Specjalista perinatologii'],
            ['first_name' => 'lek. Oliwia', 'last_name' => 'Kopera', 'specialization' => 'Specjalista ginekologii i położnictwa'],
            ['first_name' => 'dr n. med. Aneta', 'last_name' => 'Walaszek-Gruszka', 'specialization' => 'Specjalista ginekologii i położnictwa, Specjalista perinatologii'],
            ['first_name' => 'lek. Yuliia', 'last_name' => 'Baraniak', 'specialization' => 'Specjalista ginekologii i położnictwa'],
            ['first_name' => 'lek. Beata', 'last_name' => 'Dawiec', 'specialization' => 'Specjalista ginekologii i położnictwa'],
            ['first_name' => 'lek. Agnieszka', 'last_name' => 'Tyszko-Tymińska', 'specialization' => 'Specjalista ginekologii i położnictwa, Ginekolog Dziecięcy'],
            ['first_name' => 'lek. Joanna', 'last_name' => 'Nestorowicz-Czernianin', 'specialization' => 'Specjalista ginekologii i położnictwa'],
            ['first_name' => 'lek. Tomasz', 'last_name' => 'Kościelniak', 'specialization' => 'Specjalista ginekologii i położnictwa'],
            ['first_name' => 'lek. Jakub', 'last_name' => 'Andrzejewski', 'specialization' => 'Specjalista ginekologii i położnictwa'],
            ['first_name' => 'lek. Przemysław', 'last_name' => 'Piec', 'specialization' => 'Specjalista ginekologii i położnictwa'],
            ['first_name' => 'lek. Sylwia', 'last_name' => 'Wnuk', 'specialization' => 'Specjalista radiologii, Specjalista onkologii, Specjalista chorób wewnętrznych'],
            ['first_name' => 'mgr', 'last_name' => 'Aleksandra Żurakowska', 'specialization' => 'Magister fizjoterapii'],
            ['first_name' => 'dr n. med. Piotr', 'last_name' => 'Miśkiewicz', 'specialization' => 'Specjalista chirurgii dziecięcej, Specjalista ortopedii i traumatologii dziecięcej'],
            ['first_name' => 'dr n. med. Grzegorz', 'last_name' => 'Dobaczewski', 'specialization' => 'Specjalista onkologii i hematologii dziecięcej, Specjalista pediatrii'],
            ['first_name' => 'dr n. med. Justyna', 'last_name' => 'Kuliczkowska-Płaksej', 'specialization' => 'Specjalista endokrynologii, Specjalista chorób wewnętrznych'],
            ['first_name' => 'dr n. med. Barbara', 'last_name' => 'Stachowska', 'specialization' => 'Specjalista endokrynologii, Specjalista chorób wewnętrznych'],
            ['first_name' => 'dr n. med. Ewelina', 'last_name' => 'Jasic-Szpak', 'specialization' => 'Specjalista kardiologii'],
            ['first_name' => 'dr n. med. Katarzyna', 'last_name' => 'Kulej-Łyko', 'specialization' => 'Specjalista kardiologii'],
            ['first_name' => 'dr. n. med. Marta', 'last_name' => 'Obremska', 'specialization' => 'Specjalista kardiologii'],
            ['first_name' => 'dr n. med. Amelia', 'last_name' => 'Głowaczewska-Wójcik', 'specialization' => 'Specjalista dermatologii'],
            ['first_name' => 'lek. Marta', 'last_name' => 'Nogaj-Adamowicz', 'specialization' => 'Specjalista dermatologii'],
            ['first_name' => 'dr n. med. Łukasz', 'last_name' => 'Jabłoński', 'specialization' => 'Specjalista chirurgii, Specjalista chirurgii naczyniowej'],
            ['first_name' => 'lek. Jerzy', 'last_name' => 'Płochowski', 'specialization' => 'Specjalista medycyny sportowej, Specjalista ortopedii i traumatologii'],
            ['first_name' => 'dr n. k. f. Malwina', 'last_name' => 'Pawik', 'specialization' => 'Doktor nauk o kulturze fizycznej, Magister fizjoterapii'],
            ['first_name' => 'dr n. med. Bożena', 'last_name' => 'Dołęga-Kozierowska', 'specialization' => 'Specjalista alergologii, Specjalista pediatrii'],
            ['first_name' => 'dr n. med. Ryszard', 'last_name' => 'Ślęzak', 'specialization' => 'Specjalista genetyki klinicznej, Specjalista chorób wewnętrznych'],
            ['first_name' => 'dr n. med. Piotr', 'last_name' => 'Siekanowicz', 'specialization' => 'Specjalista chirurgii dziecięcej, Urolog dziecięcy'],
            ['first_name' => '', 'last_name' => 'Izabela Śliwińska', 'specialization' => 'Magister położnictwa'],
            ['first_name' => '', 'last_name' => 'Magdalena Dudziec', 'specialization' => 'Magister położnictwa'],
            ['first_name' => '', 'last_name' => 'Klaudia Bonar', 'specialization' => 'Położna']
        ];

        echo "Debug: Używam listy " . count($knownDoctors) . " znanych lekarzy\n";

        return $knownDoctors;
    }

    /**
     * Próbuje wyciągnąć specjalizację dla danego lekarza
     */
    private function extractSpecialization($html, $doctorName) {
        // Szukamy specjalizacji w pobliżu nazwy lekarza
        $escapedName = preg_quote($doctorName, '/');
        $pattern = '/' . $escapedName . '.*?Specjalista ([^<]+)/s';

        if (preg_match($pattern, $html, $match)) {
            return trim(strip_tags($match[1]));
        }

        return '';
    }

    /**
     * Usuwa duplikaty z listy lekarzy
     */
    private function removeDuplicates($doctors) {
        $unique = [];
        $seen = [];

        foreach ($doctors as $doctor) {
            $key = strtolower($doctor['first_name'] . ' ' . $doctor['last_name']);
            if (!isset($seen[$key])) {
                $seen[$key] = true;
                $unique[] = $doctor;
            }
        }

        return $unique;
    }

    /**
     * Pobiera istniejących lekarzy z bazy danych dla klienta Sonokard
     */
    public function getExistingDoctors() {
        $stmt = $this->db->prepare("
            SELECT id, first_name, last_name, specialization, photo_url 
            FROM queue_doctors 
            WHERE client_id = ? AND active = 1
        ");
        $stmt->execute([$this->sonokardClientId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Sprawdza czy lekarz już istnieje w bazie
     */
    private function doctorExists($firstName, $lastName, $existingDoctors) {
        foreach ($existingDoctors as $existing) {
            // Porównanie z uwzględnieniem różnych formatów tytułów
            $existingName = strtolower(trim($existing['first_name'] . ' ' . $existing['last_name']));
            $newName = strtolower(trim($firstName . ' ' . $lastName));

            // Usuń różne warianty tytułów dla porównania
            $existingClean = preg_replace('/^(dr n\. med\.|lek\.|mgr|dr n\. k\. f\.|dr\. n\. med\.|dr)\s+/i', '', $existingName);
            $newClean = preg_replace('/^(dr n\. med\.|lek\.|mgr|dr n\. k\. f\.|dr\. n\. med\.|dr)\s+/i', '', $newName);

            if ($existingClean === $newClean) {
                return $existing;
            }
        }
        return false;
    }

    /**
     * Dodaje nowego lekarza do bazy danych
     */
    private function addDoctor($firstName, $lastName, $specialization = '') {
        // Generuj unikalny kod dostępu
        $accessCode = $this->generateAccessCode();

        $stmt = $this->db->prepare("
            INSERT INTO queue_doctors (client_id, first_name, last_name, specialization, access_code, active, created_at)
            VALUES (?, ?, ?, ?, ?, 1, datetime('now'))
        ");

        return $stmt->execute([$this->sonokardClientId, $firstName, $lastName, $specialization, $accessCode]);
    }

    /**
     * Generuje unikalny kod dostępu
     */
    private function generateAccessCode() {
        $characters = 'abcdefghijklmnopqrstuvwxyz0123456789';

        do {
            $code = '';
            for ($i = 0; $i < 12; $i++) {
                $code .= $characters[rand(0, strlen($characters) - 1)];
            }

            // Sprawdź czy kod już istnieje
            $stmt = $this->db->prepare("SELECT id FROM queue_doctors WHERE access_code = ?");
            $stmt->execute([$code]);
        } while ($stmt->fetch());

        return $code;
    }

    /**
     * Główna metoda synchronizacji
     */
    public function syncDoctors() {
        echo "Rozpoczynam synchronizację lekarzy ze strony sonokard.pl...\n";

        try {
            // Pobierz lekarzy ze strony
            $webDoctors = $this->fetchDoctorsFromWebsite();
            echo "Znaleziono " . count($webDoctors) . " lekarzy na stronie\n";

            // Pobierz istniejących lekarzy z bazy
            $existingDoctors = $this->getExistingDoctors();
            echo "W bazie jest już " . count($existingDoctors) . " lekarzy\n";

            $added = 0;
            $skipped = 0;

            foreach ($webDoctors as $doctor) {
                $existing = $this->doctorExists($doctor['first_name'], $doctor['last_name'], $existingDoctors);

                if ($existing) {
                    echo "Pomijam: {$doctor['first_name']} {$doctor['last_name']} (już istnieje)\n";
                    $skipped++;
                } else {
                    if ($this->addDoctor($doctor['first_name'], $doctor['last_name'], $doctor['specialization'])) {
                        echo "Dodano: {$doctor['first_name']} {$doctor['last_name']}\n";
                        $added++;
                    } else {
                        echo "Błąd podczas dodawania: {$doctor['first_name']} {$doctor['last_name']}\n";
                    }
                }
            }

            echo "\nPodsumowanie:\n";
            echo "- Dodano nowych lekarzy: $added\n";
            echo "- Pominięto istniejących: $skipped\n";
            echo "- Łącznie lekarzy na stronie: " . count($webDoctors) . "\n";
        } catch (Exception $e) {
            echo "Błąd: " . $e->getMessage() . "\n";
        }
    }
}

// Uruchom synchronizację jeśli skrypt jest wywołany bezpośrednio
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $sync = new SonokardDoctorSync();
    $sync->syncDoctors();
}
