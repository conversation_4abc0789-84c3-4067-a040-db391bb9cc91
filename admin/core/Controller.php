<?php

class Controller {
    protected $db;

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    protected function view($view, $data = [], $useLayout = true) {
        extract($data);
        $current_page = $view;

        // Je<PERSON>li parametr useLayout jest ustawiony na false, nie używaj szablonu
        if (!$useLayout) {
            if (file_exists("views/$view.php")) {
                include "views/$view.php";
            }
            return;
        }

        // Sprawdź czy to partial view czy pełny widok
        if (strpos($view, 'auth/') === 0 || strpos($view, 'home/') === 0) {
            // Dla stron auth i home używaj prostego widoku
            ob_start();
            if (file_exists("views/$view.php")) {
                include "views/$view.php";
            } else {
                echo "<div class='alert alert-danger'>Widok views/$view.php nie został znaleziony</div>";
            }
            $content = ob_get_clean();
            include "views/layout.php";
        } else {
            // Dla paneli admin/advertiser/client używaj pełnego layoutu
            ob_start();
            if (file_exists("views/$view.php")) {
                include "views/$view.php";
            } else {
                echo "<div class='alert alert-danger'>Widok views/$view.php nie został znaleziony</div>";
            }
            $content = ob_get_clean();
            include "views/layout.php";
        }
    }

    protected function redirect($url) {
        $config = require __DIR__ . '/../config.php';
        $prefix = $config['url_prefix'];

        if (strpos($url, $prefix) === 0) {
            header("Location: $url");
        } elseif (strpos($url, '/') === 0) {
            header("Location: {$prefix}{$url}");
        } else {
            header("Location: {$prefix}/{$url}");
        }
        exit;
    }

    protected function json($data) {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    protected function requireAuth($role = null) {
        if (!isset($_SESSION['user_id'])) {
            $this->redirect('/login');
        }

        if ($role && $_SESSION['user_role'] !== $role) {
            http_response_code(403);
            die('Brak uprawnień');
        }
    }

    protected function getCurrentUser() {
        if (!isset($_SESSION['user_id'])) {
            return null;
        }

        $stmt = $this->db->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch();
    }

    protected function setError($message) {
        $_SESSION['error_message'] = $message;
    }

    protected function setSuccess($message) {
        $_SESSION['success_message'] = $message;
    }

    protected function getError() {
        if (isset($_SESSION['error_message'])) {
            $message = $_SESSION['error_message'];
            unset($_SESSION['error_message']);
            return $message;
        }
        return null;
    }

    protected function getSuccess() {
        if (isset($_SESSION['success_message'])) {
            $message = $_SESSION['success_message'];
            unset($_SESSION['success_message']);
            return $message;
        }
        return null;
    }
}
