<?php

class Router {
    private $routes = [];
    
    public function get($path, $callback) {
        $this->routes['GET'][$path] = $callback;
    }
    
    public function post($path, $callback) {
        $this->routes['POST'][$path] = $callback;
    }
    
    public function run() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $path = rtrim($path, '/') ?: '/';

        error_log("Router::run - Metoda: " . $method . ", Ścieżka: " . $path);
        
        if (isset($this->routes[$method][$path])) {
            $callback = $this->routes[$method][$path];
            if (is_array($callback)) {
                $controller = new $callback[0]();
                $action = $callback[1];
                error_log("Router::run - Znaleziono dokładne dopasowanie: " . $path);
                $controller->$action();
                return;
            }
        }
        
        // Sprawdź parametryczne routes
        foreach ($this->routes[$method] ?? [] as $route => $callback) {
            $pattern = preg_replace('/\{[^}]+\}/', '([^/]+)', $route);
            error_log("Router::run - Sprawdzanie parametrycznej trasy. Wzorzec: " . $pattern . ", Ścieżka: " . $path);
            if (preg_match('#^' . $pattern . '$#', $path, $matches)) {
                array_shift($matches);
                error_log("Router::run - Dopasowano parametryczną trasę: " . $route . ", Parametry: " . implode(', ', $matches));
                if (is_array($callback)) {
                    $controller = new $callback[0]();
                    $action = $callback[1];
                    $controller->$action(...$matches);
                    return;
                }
            }
        }
        
        // 404
        http_response_code(404);
        echo "Strona nie została znaleziona";
        error_log("Router::run - 404: Strona nie została znaleziona dla: " . $path);
    }
} 