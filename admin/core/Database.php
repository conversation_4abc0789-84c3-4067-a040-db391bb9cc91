<?php

class Database {
    private static $instance = null;
    private $pdo;
    
    private function __construct() {
        try {
            // Upewnij się, że katalog database istnieje
            $databasePath = __DIR__ . '/../../database';
            if (!is_dir($databasePath)) {
                mkdir($databasePath, 0777, true);
            }
            
            $this->pdo = new PDO('sqlite:' . $databasePath . '/reklama.db');
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            die('Błąd połączenia z bazą danych: ' . $e->getMessage());
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->pdo;
    }
    
    public static function init() {
        $db = self::getInstance();
        $db->createTables();
    }
    
    private function createTables() {
        // Tabela użytkowników
        $this->pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role TEXT NOT NULL,
            company_name VARCHAR(100),
            balance DECIMAL(10,2) DEFAULT 0,
            rate_per_second DECIMAL(5,4) DEFAULT 0.0001,
            is_active INTEGER DEFAULT 1,
            last_activity DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )");
        
        // Tabela kategorii kampanii
        $this->pdo->exec("
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )");
        
        // Tabela kampanii reklamowych
        $this->pdo->exec("
        CREATE TABLE IF NOT EXISTS campaigns (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            advertiser_id INTEGER NOT NULL,
            category_id INTEGER,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            media_type TEXT NOT NULL,
            media_url VARCHAR(255) NOT NULL,
            youtube_id VARCHAR(20),
            duration INTEGER DEFAULT 30,
            budget DECIMAL(10,2) NOT NULL,
            spent DECIMAL(10,2) DEFAULT 0,
            rate_per_second DECIMAL(5,4) DEFAULT 0.0001,
            max_frequency_per_hour INTEGER DEFAULT 0,
            start_date DATETIME,
            end_date DATETIME,
            is_active INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (advertiser_id) REFERENCES users(id),
            FOREIGN KEY (category_id) REFERENCES categories(id)
        )");
        
        // Tabela wyświetleń reklam
        $this->pdo->exec("
        CREATE TABLE IF NOT EXISTS ad_views (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            campaign_id INTEGER NOT NULL,
            client_id INTEGER NOT NULL,
            duration_seconds INTEGER NOT NULL,
            cost DECIMAL(10,4) NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (campaign_id) REFERENCES campaigns(id),
            FOREIGN KEY (client_id) REFERENCES users(id)
        )");
        
        // Tabela monitorów/TV klientów
        $this->pdo->exec("
        CREATE TABLE IF NOT EXISTS client_displays (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            client_id INTEGER NOT NULL,
            display_name VARCHAR(100) NOT NULL,
            is_online INTEGER DEFAULT 0,
            last_heartbeat DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES users(id)
        )");
        
        // Tabela przypisań kampanii do klientów
        $this->pdo->exec("
        CREATE TABLE IF NOT EXISTS campaign_assignments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            campaign_id INTEGER NOT NULL,
            client_id INTEGER NOT NULL,
            is_accepted INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (campaign_id) REFERENCES campaigns(id),
            FOREIGN KEY (client_id) REFERENCES users(id)
        )");
        
        // Tabela automatycznej akceptacji kategorii przez klientów
        $this->pdo->exec("
        CREATE TABLE IF NOT EXISTS campaign_auto_accept (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            client_id INTEGER NOT NULL,
            category_id INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES users(id),
            FOREIGN KEY (category_id) REFERENCES categories(id),
            UNIQUE(client_id, category_id)
        )");
        
        // Tabela ustawień importu dla różnych systemów
        $this->pdo->exec("
        CREATE TABLE IF NOT EXISTS import_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            client_id INTEGER NOT NULL,
            system_name VARCHAR(100) NOT NULL, -- np. 'igabinet', 'medinet', etc.
            sync_code VARCHAR(16) UNIQUE NOT NULL, -- 16-znakowy kod synchronizacji
            is_active INTEGER DEFAULT 1,
            api_endpoint VARCHAR(255),
            api_credentials TEXT, -- JSON z danymi logowania
            last_sync DATETIME,
            sync_frequency INTEGER DEFAULT 3600, -- w sekundach
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES users(id),
            UNIQUE(client_id, system_name)
        )");

        // Tabela mapowań lekarzy z systemów zewnętrznych
        $this->pdo->exec("
        CREATE TABLE IF NOT EXISTS external_doctor_mappings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            import_setting_id INTEGER NOT NULL,
            external_doctor_id VARCHAR(100) NOT NULL,
            external_doctor_name VARCHAR(200) NOT NULL,
            external_doctor_specialization VARCHAR(200),
            system_doctor_id INTEGER,
            is_mapped INTEGER DEFAULT 0,
            last_seen DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (import_setting_id) REFERENCES import_settings(id),
            FOREIGN KEY (system_doctor_id) REFERENCES queue_doctors(id),
            UNIQUE(import_setting_id, external_doctor_id)
        )");

        // Tabela logów synchronizacji
        $this->pdo->exec("
        CREATE TABLE IF NOT EXISTS sync_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            import_setting_id INTEGER NOT NULL,
            sync_type VARCHAR(50) NOT NULL, -- 'full', 'incremental', 'manual'
            status VARCHAR(20) NOT NULL, -- 'success', 'error', 'partial'
            records_processed INTEGER DEFAULT 0,
            records_updated INTEGER DEFAULT 0,
            records_created INTEGER DEFAULT 0,
            error_message TEXT,
            started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            completed_at DATETIME,
            FOREIGN KEY (import_setting_id) REFERENCES import_settings(id)
        )");
        
        // Dodaj domyślnego administratora
        $this->createDefaultAdmin();
        
        // Dodaj domyślne kategorie
        $this->createDefaultCategories();
        
        // Dodaj domyślne dane importu
        $this->createDefaultImportData();
    }
    
    private function createDefaultAdmin() {
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users WHERE role = 'admin'");
        $stmt->execute();
        
        if ($stmt->fetchColumn() == 0) {
            $stmt = $this->pdo->prepare("
                INSERT INTO users (username, email, password, role, company_name) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                'admin',
                '<EMAIL>',
                password_hash('admin123', PASSWORD_DEFAULT),
                'admin',
                'Panel Administratora'
            ]);
        }
    }
    
    private function createDefaultCategories() {
        // Sprawdź, czy istnieją już jakieś kategorie
        $stmt = $this->pdo->query("SELECT COUNT(*) FROM categories");
        $count = $stmt->fetchColumn();
        
        if ($count > 0) {
            return; // Kategorie już istnieją
        }
        
        // Dodaj domyślne kategorie
        $defaultCategories = [
            ['name' => 'Edukacja', 'description' => 'Kampanie związane z edukacją'],
            ['name' => 'Medycyna', 'description' => 'Kampanie związane z medycyną i zdrowiem'],
            ['name' => 'Firmy lokalne', 'description' => 'Kampanie lokalnych firm i usług'],
            ['name' => 'Finanse', 'description' => 'Kampanie związane z finansami i bankowością'],
            ['name' => 'Rozrywka', 'description' => 'Kampanie związane z rozrywką i wydarzeniami']
        ];
        
        $stmt = $this->pdo->prepare("INSERT INTO categories (name, description) VALUES (?, ?)");
        
        foreach ($defaultCategories as $category) {
            $stmt->execute([$category['name'], $category['description']]);
        }
    }
    
    private function createDefaultImportData() {
        // Sprawdź, czy istnieją już jakieś ustawienia importu
        $stmt = $this->pdo->query("SELECT COUNT(*) FROM import_settings");
        $count = $stmt->fetchColumn();
        
        if ($count > 0) {
            return; // Dane już istnieją
        }
        
        // Sprawdź, czy istnieją klienci
        $stmt = $this->pdo->query("SELECT id FROM users WHERE role = 'client' LIMIT 2");
        $clients = $stmt->fetchAll();
        
        if (empty($clients)) {
            return; // Brak klientów
        }
        
        // Dodaj przykładowe ustawienia importu dla systemu igabinet
        $stmt = $this->pdo->prepare("
            INSERT INTO import_settings (client_id, system_name, sync_code, is_active, api_endpoint, sync_frequency) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        foreach ($clients as $index => $client) {
            $stmt->execute([
                $client['id'],
                'igabinet',
                'igab' . str_pad($index + 1, 12, '0', STR_PAD_LEFT),
                1,
                'https://sonokard.igabinet.pl/admin/request/work_schedule_request.php',
                3600
            ]);
        }
    }
} 