<?php
require_once 'core/Database.php';
require_once 'models/ExternalDoctorMapping.php';

// Test pobierania lekarzy dla klienta Sonokard (ID=2)
$externalDoctorMapping = new ExternalDoctorMapping();
$systemDoctors = $externalDoctorMapping->getAvailableSystemDoctors(2);

echo "=== TEST POBIERANIA LEKARZY DLA MAPOWANIA ===\n\n";
echo "Liczba lekarzy zwróconych przez getAvailableSystemDoctors(2): " . count($systemDoctors) . "\n\n";

echo "Lista lekarzy:\n";
echo str_pad("ID", 4) . str_pad("Imię", 25) . str_pad("Nazwisko", 25) . "Specjalizacja\n";
echo str_repeat("-", 80) . "\n";

foreach ($systemDoctors as $doctor) {
    echo str_pad($doctor['id'], 4) . 
         str_pad($doctor['first_name'], 25) . 
         str_pad($doctor['last_name'], 25) . 
         ($doctor['specialization'] ? substr($doctor['specialization'], 0, 30) : 'brak') . "\n";
}

echo "\n=== SPRAWDZENIE BEZPOŚREDNIO W BAZIE ===\n";

$db = Database::getInstance()->getConnection();
$stmt = $db->prepare("
    SELECT id, first_name, last_name, specialization, active
    FROM queue_doctors
    WHERE client_id = ? AND active = 1
    ORDER BY first_name, last_name
");
$stmt->execute([2]);
$directResults = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "Liczba lekarzy bezpośrednio z bazy: " . count($directResults) . "\n\n";

// Sprawdź czy są różnice
if (count($systemDoctors) !== count($directResults)) {
    echo "⚠️  UWAGA: Różnica w liczbie lekarzy!\n";
    echo "getAvailableSystemDoctors: " . count($systemDoctors) . "\n";
    echo "Bezpośrednie zapytanie: " . count($directResults) . "\n\n";
}

// Sprawdź czy wszyscy lekarze mają wypełnione pola
echo "=== SPRAWDZENIE JAKOŚCI DANYCH ===\n";
$emptyFirstName = 0;
$emptyLastName = 0;

foreach ($directResults as $doctor) {
    if (empty(trim($doctor['first_name']))) {
        $emptyFirstName++;
        echo "⚠️  Pusty first_name dla ID: {$doctor['id']}\n";
    }
    if (empty(trim($doctor['last_name']))) {
        $emptyLastName++;
        echo "⚠️  Pusty last_name dla ID: {$doctor['id']}\n";
    }
}

echo "\nPodsumowanie jakości danych:\n";
echo "- Lekarze z pustym first_name: $emptyFirstName\n";
echo "- Lekarze z pustym last_name: $emptyLastName\n";

if ($emptyFirstName === 0 && $emptyLastName === 0) {
    echo "✅ Wszystkie dane są kompletne\n";
}

echo "\n=== TEST KONKRETNEGO PRZYPADKU ===\n";

// Sprawdź czy konkretni lekarze są na liście
$testDoctors = ['Małgorzata Olesiak-Andryszczak', 'Ewelina Sądaj', 'Izabela Śliwińska'];

foreach ($testDoctors as $testName) {
    $found = false;
    foreach ($systemDoctors as $doctor) {
        $fullName = trim($doctor['first_name'] . ' ' . $doctor['last_name']);
        if (strpos($fullName, $testName) !== false || strpos($testName, $fullName) !== false) {
            echo "✅ Znaleziono: $testName (ID: {$doctor['id']})\n";
            $found = true;
            break;
        }
    }
    if (!$found) {
        echo "❌ Nie znaleziono: $testName\n";
    }
}
?>
