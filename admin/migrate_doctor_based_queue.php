<?php

/**
 * Migra<PERSON>ja systemu kolejkowego na oparty o lekarzy zamiast pokoi
 * 
 * Zmiany:
 * 1. room_id staje się opcjonalne (NULL)
 * 2. doctor_id staje się wymagane (NOT NULL)
 * 3. Aktualizacja istniejących wizyt bez doctor_id
 */

require_once 'core/Database.php';

try {
    $db = Database::getInstance()->getConnection();

    echo "Rozpoczynam migrację systemu kolejkowego na oparty o lekarzy...\n";

    // Rozpocznij transakcję
    $db->beginTransaction();

    // 1. Sprawdź czy są wizyty bez doctor_id i spróbuj je naprawić
    echo "1. Sprawdzam wizyty bez doctor_id...\n";
    $stmt = $db->query("SELECT COUNT(*) as count FROM queue_appointments WHERE doctor_id IS NULL");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $appointmentsWithoutDoctor = $result['count'];

    if ($appointmentsWithoutDoctor > 0) {
        echo "Znaleziono {$appointmentsWithoutDoctor} wizyt bez doctor_id\n";

        // Spróbuj przypisać lekarzy na podstawie pokoi
        echo "Próbuję przypisać lekarzy na podstawie pokoi...\n";
        $db->exec("
            UPDATE queue_appointments 
            SET doctor_id = (
                SELECT doctor_id 
                FROM queue_rooms 
                WHERE queue_rooms.id = queue_appointments.room_id
                AND queue_rooms.doctor_id IS NOT NULL
            )
            WHERE doctor_id IS NULL
            AND room_id IS NOT NULL
        ");

        // Sprawdź ile wizyt nadal nie ma doctor_id
        $stmt = $db->query("SELECT COUNT(*) as count FROM queue_appointments WHERE doctor_id IS NULL");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $stillWithoutDoctor = $result['count'];

        if ($stillWithoutDoctor > 0) {
            echo "Nadal {$stillWithoutDoctor} wizyt bez doctor_id\n";

            // Przypisz do pierwszego dostępnego lekarza dla każdego klienta
            echo "Przypisuję do pierwszego dostępnego lekarza...\n";
            $stmt = $db->query("
                SELECT DISTINCT client_id 
                FROM queue_appointments 
                WHERE doctor_id IS NULL
            ");
            $clientsWithoutDoctor = $stmt->fetchAll(PDO::FETCH_ASSOC);

            foreach ($clientsWithoutDoctor as $client) {
                $clientId = $client['client_id'];

                // Znajdź pierwszego aktywnego lekarza dla tego klienta
                $stmt = $db->prepare("
                    SELECT id FROM queue_doctors 
                    WHERE client_id = ? AND active = 1 
                    ORDER BY id 
                    LIMIT 1
                ");
                $stmt->execute([$clientId]);
                $firstDoctor = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($firstDoctor) {
                    $doctorId = $firstDoctor['id'];
                    echo "Przypisuję wizyty klienta {$clientId} do lekarza {$doctorId}\n";

                    $stmt = $db->prepare("
                        UPDATE queue_appointments 
                        SET doctor_id = ? 
                        WHERE client_id = ? AND doctor_id IS NULL
                    ");
                    $stmt->execute([$doctorId, $clientId]);
                } else {
                    echo "BŁĄD: Brak aktywnych lekarzy dla klienta {$clientId}\n";
                    throw new Exception("Brak aktywnych lekarzy dla klienta {$clientId}");
                }
            }
        }
    }

    // 2. Sprawdź czy wszystkie wizyty mają teraz doctor_id
    echo "2. Sprawdzam czy wszystkie wizyty mają doctor_id...\n";
    $stmt = $db->query("SELECT COUNT(*) as count FROM queue_appointments WHERE doctor_id IS NULL");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($result['count'] > 0) {
        throw new Exception("Nadal są wizyty bez doctor_id: " . $result['count']);
    }

    echo "✓ Wszystkie wizyty mają przypisanych lekarzy\n";

    // 3. Sprawdź strukturę tabeli
    echo "3. Sprawdzam strukturę tabeli...\n";
    echo "✓ Tabela ma już poprawną strukturę (doctor_id istnieje)\n";
    echo "✓ room_id pozostaje opcjonalne\n";
    echo "✓ Wszystkie wizyty mają przypisanych lekarzy\n";

    // 4. Dodaj indeksy dla lepszej wydajności
    echo "4. Dodaję indeksy...\n";
    $db->exec("CREATE INDEX IF NOT EXISTS idx_appointments_doctor_date ON queue_appointments(doctor_id, appointment_date)");
    $db->exec("CREATE INDEX IF NOT EXISTS idx_appointments_client_date ON queue_appointments(client_id, appointment_date)");
    $db->exec("CREATE INDEX IF NOT EXISTS idx_appointments_status ON queue_appointments(status)");
    $db->exec("CREATE INDEX IF NOT EXISTS idx_appointments_external_id ON queue_appointments(external_id)");

    // Zatwierdź transakcję
    $db->commit();

    echo "✓ Migracja zakończona pomyślnie!\n";
    echo "System kolejkowy jest teraz oparty na lekarzach.\n";
    echo "- doctor_id jest teraz wymagane\n";
    echo "- room_id jest opcjonalne\n";
    echo "- Wszystkie istniejące wizyty mają przypisanych lekarzy\n";
} catch (Exception $e) {
    // Cofnij transakcję w przypadku błędu
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    echo "BŁĄD podczas migracji: " . $e->getMessage() . "\n";
    exit(1);
}
