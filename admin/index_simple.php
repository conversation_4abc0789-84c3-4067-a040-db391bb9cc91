<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Aplik<PERSON><PERSON> działa!</h1>";
echo "<p><PERSON><PERSON><PERSON> wid<PERSON>sz tę stronę, oznacza to, że PHP działa poprawnie w katalogu admin.</p>";

echo "<h2>Test bazy danych:</h2>";
try {
    $dbPath = __DIR__ . '/../database/reklama.db';
    if (file_exists($dbPath)) {
        $pdo = new PDO('sqlite:' . $dbPath);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $result = $stmt->fetch();
        echo "Baza danych działa! Liczba użytkowników: " . $result['count'] . "<br>";
    } else {
        echo "Błąd: Plik bazy danych nie istnieje: " . $dbPath . "<br>";
    }
} catch (Exception $e) {
    echo "Błąd bazy danych: " . $e->getMessage() . "<br>";
}

echo "<h2>Test uploads:</h2>";
$uploadsPath = __DIR__ . '/../uploads/';
if (is_writable($uploadsPath)) {
    echo "Katalog uploads jest zapisywalny<br>";
} else {
    echo "Błąd: Katalog uploads nie jest zapisywalny<br>";
}

echo "<h2>Linki testowe:</h2>";
echo "<a href='test.php'>Test PHP</a><br>";
echo "<a href='debug.php'>Debug info</a><br>";
echo "<a href='index.php'>Główna aplikacja</a><br>";
?> 