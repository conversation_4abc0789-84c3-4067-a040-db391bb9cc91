# System Importu - Panel Administratora i Klienta

## Opis

System importu pozwala na synchronizację danych z zewnętrznych systemów medycznych (np. iGabinet.pl) z systemem kolejkowym. **Ustawienia importu są dostępne zarówno z poziomu administratora, jak i z poziomu klienta (przychodni)**.

## Funkcjonalności

### 1. Ustawienia Importu
- **Dodawanie nowych ustawień importu** - konfiguracja dla różnych systemów
- **Edycja istniejących ustawień** - modyfikacja parametrów synchronizacji
- **Generowanie kodów synchronizacji** - unikalne 16-znakowe kody
- **Zarządzanie częstotliwością synchronizacji** - od 15 minut do codziennie
- **Aktywacja/deaktywacja** - włączanie i wyłączanie importu

### 2. Mapowania Lekarzy
- **Automatyczne tworzenie mapowań** - po pierwszej synchronizacji
- **Ręczne przypisywanie** - mapowanie lekarzy zewnętrznych na systemowych
- **Zarządzanie specjalizacjami** - informacje o lekarzach
- **Status mapowań** - śledzenie przypisanych i nieprzypisanych lekarzy

### 3. Obsługiwane Systemy
- **iGabinet.pl** - główny system medyczny
- **Medinet** - system medyczny (przygotowany)
- **Inne systemy** - możliwość dodania nowych

## Dostęp do Systemu

### Panel Administratora
- **URL:** `http://localhost:8080/admin/import`
- **Login:** `admin`
- **Hasło:** `password`
- **Funkcje:** Zarządzanie ustawieniami importu dla wszystkich klientów

### Panel Klienta (Przychodni)
- **URL:** `http://localhost:8080/client/import`
- **Login:** `klient1` (lub inne konto klienta)
- **Hasło:** `password`
- **Funkcje:** Zarządzanie własnymi ustawieniami importu

## Instalacja i Konfiguracja

### 1. Inicjalizacja Bazy Danych
```bash
cd admin
php init_db.php
```

### 2. Konfiguracja Importu (dla klienta)
1. Zaloguj się do panelu klienta
2. Przejdź do **Ustawienia importu** w menu
3. Kliknij **Dodaj nowe ustawienie**
4. Wybierz system (iGabinet.pl)
5. Wygeneruj kod synchronizacji
6. Ustaw częstotliwość synchronizacji
7. Zapisz ustawienie

### 3. Konfiguracja Importu (dla administratora)
1. Zaloguj się do panelu admina
2. Przejdź do **Ustawienia importu** w menu
3. Kliknij **Dodaj nowe ustawienie**
4. Wybierz klienta i system
5. Wygeneruj kod synchronizacji
6. Ustaw częstotliwość synchronizacji
7. Zapisz ustawienie

## Użycie

### Dla Klienta (Przychodni)

#### Dodawanie Ustawienia Importu
1. **Panel główny** → **Ustawienia importu**
2. **Dodaj nowe ustawienie**
3. Wypełnij formularz:
   - System (iGabinet.pl, Medinet, inny)
   - Kod synchronizacji (16 znaków) - generowany automatycznie
   - Częstotliwość synchronizacji
   - Endpoint API (opcjonalnie)
4. **Zapisz ustawienie**

#### Zarządzanie Mapowaniami Lekarzy
1. **Ustawienia importu** → kliknij ikonę **Mapowania lekarzy**
2. Przeglądaj listę lekarzy z systemu zewnętrznego
3. Kliknij **Edytuj** przy każdym lekarzu
4. Wybierz odpowiedniego lekarza z Twojego systemu
5. **Zapisz mapowanie**

### Dla Administratora

#### Zarządzanie Wszystkimi Ustawieniami
1. **Panel główny** → **Ustawienia importu**
2. **Dodaj nowe ustawienie**
3. Wypełnij formularz:
   - Klient (wybierz z listy)
   - System (iGabinet.pl, Medinet, inny)
   - Kod synchronizacji (16 znaków)
   - Częstotliwość synchronizacji
   - Endpoint API (opcjonalnie)
4. **Zapisz ustawienie**

#### Monitorowanie i Wsparcie
- Przeglądanie wszystkich ustawień importu
- Sprawdzanie statusu synchronizacji
- Wsparcie techniczne dla klientów
- Zarządzanie globalnymi ustawieniami

## Struktura Bazy Danych

### Tabela `import_settings`
- `id` - unikalny identyfikator
- `client_id` - ID klienta (przychodni)
- `system_name` - nazwa systemu zewnętrznego
- `sync_code` - 16-znakowy kod synchronizacji
- `is_active` - status aktywności
- `api_endpoint` - endpoint API (opcjonalnie)
- `sync_frequency` - częstotliwość synchronizacji w sekundach
- `last_sync` - data ostatniej synchronizacji
- `created_at`, `updated_at` - daty utworzenia i modyfikacji

### Tabela `external_doctor_mappings`
- `id` - unikalny identyfikator
- `import_setting_id` - ID ustawienia importu
- `external_doctor_id` - ID lekarza w systemie zewnętrznym
- `external_doctor_name` - nazwa lekarza
- `external_doctor_specialization` - specjalizacja
- `system_doctor_id` - ID lekarza w systemie lokalnym
- `is_mapped` - status mapowania
- `last_seen` - ostatnie widzenie w systemie zewnętrznym

### Tabela `sync_logs`
- `id` - unikalny identyfikator
- `import_setting_id` - ID ustawienia importu
- `sync_type` - typ synchronizacji (pełna, przyrostowa, ręczna)
- `status` - status synchronizacji (sukces, błąd, częściowy)
- `records_processed` - liczba przetworzonych rekordów
- `records_updated` - liczba zaktualizowanych rekordów
- `records_created` - liczba utworzonych rekordów
- `error_message` - komunikat błędu (jeśli wystąpił)
- `started_at`, `completed_at` - czasy rozpoczęcia i zakończenia

## Dodatek Chrome

### Lokalizacja
- Katalog: `chrome1/`
- Plik manifest: `manifest.json`
- Skrypt popup: `popup.js`
- Skrypt tła: `background.js`

### Funkcjonalności
- Eksport danych z iGabinet.pl
- Wysyłanie danych do systemu lokalnego
- Obsługa różnych typów zapytań API
- Automatyczne mapowanie lekarzy

### Instalacja
1. Otwórz Chrome
2. Przejdź do `chrome://extensions/`
3. Włącz **Tryb dewelopera**
4. Kliknij **Załaduj rozpakowane**
5. Wybierz katalog `chrome1/`

## Bezpieczeństwo i Uprawnienia

### Dla Klienta
- Dostęp tylko do własnych ustawień importu
- Możliwość edycji i usuwania własnych ustawień
- Brak dostępu do ustawień innych klientów
- Bezpieczne generowanie kodów synchronizacji

### Dla Administratora
- Dostęp do wszystkich ustawień importu
- Możliwość zarządzania ustawieniami dla każdego klienta
- Pełne uprawnienia CRUD
- Monitorowanie całego systemu

## Rozwiązywanie Problemów

### Błąd "Kod synchronizacji już istnieje"
- Wygeneruj nowy kod synchronizacji
- Sprawdź czy nie ma duplikatów w bazie

### Brak mapowań lekarzy
- Upewnij się, że synchronizacja została wykonana
- Sprawdź logi synchronizacji
- Zweryfikuj połączenie z systemem zewnętrznym

### Problem z połączeniem
- Sprawdź endpoint API
- Zweryfikuj dane logowania
- Sprawdź logi błędów

### Problem z uprawnieniami
- Upewnij się, że jesteś zalogowany jako odpowiedni użytkownik
- Sprawdź czy ustawienie należy do Twojego konta
- Skontaktuj się z administratorem w razie problemów

## Rozszerzenia

### Dodawanie Nowych Systemów
1. Dodaj nową opcję w formularzu (`system_name`)
2. Zaktualizuj logikę synchronizacji
3. Dodaj specyficzne mapowania dla systemu
4. Zaktualizuj dokumentację

### API Endpoints

#### Panel Administratora
- `GET /admin/import` - lista wszystkich ustawień importu
- `POST /admin/create-import` - tworzenie ustawienia
- `GET /admin/edit-import/{id}` - edycja ustawienia
- `GET /admin/doctor-mappings/{id}` - mapowania lekarzy
- `POST /admin/update-doctor-mapping` - aktualizacja mapowania
- `GET /admin/generate-sync-code` - generowanie kodu synchronizacji

#### Panel Klienta
- `GET /client/import` - lista własnych ustawień importu
- `POST /client/import/create` - tworzenie własnego ustawienia
- `GET /client/import/edit/{id}` - edycja własnego ustawienia
- `GET /client/import/doctor-mappings/{id}` - mapowania lekarzy
- `POST /client/import/update-doctor-mapping` - aktualizacja mapowania
- `GET /client/import/generate-sync-code` - generowanie kodu synchronizacji

## Wsparcie

W przypadku problemów:
1. Sprawdź logi systemu
2. Zweryfikuj konfigurację bazy danych
3. Sprawdź uprawnienia użytkowników
4. Skontaktuj się z administratorem systemu

## Korzyści z Dostępu Klienta

### Dla Przychodni
- **Niezależność** - możliwość samodzielnej konfiguracji
- **Szybkość** - natychmiastowe dodawanie nowych systemów
- **Kontrola** - pełne zarządzanie własnymi ustawieniami
- **Bezpieczeństwo** - dostęp tylko do własnych danych

### Dla Administratora
- **Monitorowanie** - przegląd wszystkich ustawień
- **Wsparcie** - pomoc techniczna dla klientów
- **Kontrola** - możliwość interwencji w razie problemów
- **Analiza** - statystyki całego systemu
