<?php
require_once 'core/Database.php';

class RealDoctorPhotoDownloader {
    private $db;
    private $sonokardClientId = 2;
    private $uploadDir = '../uploads/doctors/';
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
        
        // Upewnij się, że katalog istnieje
        if (!is_dir($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }
    }
    
    /**
     * Pobiera wszystkich lekarzy
     */
    public function getAllDoctors() {
        $stmt = $this->db->prepare("
            SELECT id, first_name, last_name, photo_url 
            FROM queue_doctors 
            WHERE client_id = ? AND active = 1
            ORDER BY id
        ");
        $stmt->execute([$this->sonokardClientId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Mapowanie lekarzy na prawdopodobne nazwy plików zdjęć
     */
    private function getDoctorPhotoMappings() {
        return [
            // Mapowanie na podstawie analizy strony sonokard.pl
            'Małgorzata Olesiak-Andryszczak' => [
                'malgorzata-olesiak-andryszczak.jpg',
                'olesiak-andryszczak.jpg',
                'malgorzata.jpg',
                'dr-olesiak.jpg'
            ],
            'Ewelina Sądaj' => [
                'ewelina-sadaj.jpg',
                'sadaj.jpg',
                'ewelina.jpg'
            ],
            'Natalia Kubat' => [
                'natalia-kubat.jpg',
                'kubat.jpg',
                'natalia.jpg'
            ],
            'Oliwia Kopera' => [
                'oliwia-kopera.jpg',
                'kopera.jpg',
                'oliwia.jpg'
            ],
            'Aneta Walaszek-Gruszka' => [
                'aneta-walaszek-gruszka.jpg',
                'walaszek-gruszka.jpg',
                'aneta.jpg'
            ],
            'Yuliia Baraniak' => [
                'yuliia-baraniak.jpg',
                'baraniak.jpg',
                'yuliia.jpg'
            ],
            'Beata Dawiec' => [
                'beata-dawiec.jpg',
                'dawiec.jpg',
                'beata.jpg'
            ],
            'Agnieszka Tyszko-Tymińska' => [
                'agnieszka-tyszko-tyminska.jpg',
                'tyszko-tyminska.jpg',
                'agnieszka.jpg'
            ],
            'Joanna Nestorowicz-Czernianin' => [
                'joanna-nestorowicz-czernianin.jpg',
                'nestorowicz-czernianin.jpg',
                'joanna.jpg'
            ],
            'Tomasz Kościelniak' => [
                'tomasz-koscielniak.jpg',
                'koscielniak.jpg',
                'tomasz.jpg'
            ],
            'Jakub Andrzejewski' => [
                'jakub-andrzejewski.jpg',
                'andrzejewski.jpg',
                'jakub.jpg'
            ],
            'Przemysław Piec' => [
                'przemyslaw-piec.jpg',
                'piec.jpg',
                'przemyslaw.jpg'
            ],
            'Sylwia Wnuk' => [
                'sylwia-wnuk.jpg',
                'wnuk.jpg',
                'sylwia.jpg'
            ],
            'Aleksandra Żurakowska' => [
                'aleksandra-zurakowska.jpg',
                'zurakowska.jpg',
                'aleksandra.jpg'
            ],
            'Piotr Miśkiewicz' => [
                'piotr-miskiewicz.jpg',
                'miskiewicz.jpg',
                'piotr.jpg'
            ],
            'Grzegorz Dobaczewski' => [
                'grzegorz-dobaczewski.jpg',
                'dobaczewski.jpg',
                'grzegorz.jpg'
            ],
            'Justyna Kuliczkowska-Płaksej' => [
                'justyna-kuliczkowska-plaksej.jpg',
                'kuliczkowska-plaksej.jpg',
                'justyna.jpg'
            ],
            'Barbara Stachowska' => [
                'barbara-stachowska.jpg',
                'stachowska.jpg',
                'barbara.jpg'
            ],
            'Ewelina Jasic-Szpak' => [
                'ewelina-jasic-szpak.jpg',
                'jasic-szpak.jpg',
                'ewelina-jasic.jpg'
            ],
            'Katarzyna Kulej-Łyko' => [
                'katarzyna-kulej-lyko.jpg',
                'kulej-lyko.jpg',
                'katarzyna.jpg'
            ],
            'Marta Obremska' => [
                'marta-obremska.jpg',
                'obremska.jpg',
                'marta.jpg'
            ],
            'Amelia Głowaczewska-Wójcik' => [
                'amelia-glowaczewska-wojcik.jpg',
                'glowaczewska-wojcik.jpg',
                'amelia.jpg'
            ],
            'Marta Nogaj-Adamowicz' => [
                'marta-nogaj-adamowicz.jpg',
                'nogaj-adamowicz.jpg',
                'marta-nogaj.jpg'
            ],
            'Łukasz Jabłoński' => [
                'lukasz-jablonski.jpg',
                'jablonski.jpg',
                'lukasz.jpg'
            ],
            'Jerzy Płochowski' => [
                'jerzy-plochowski.jpg',
                'plochowski.jpg',
                'jerzy.jpg'
            ],
            'Malwina Pawik' => [
                'malwina-pawik.jpg',
                'pawik.jpg',
                'malwina.jpg'
            ],
            'Bożena Dołęga-Kozierowska' => [
                'bozena-dolega-kozierowska.jpg',
                'dolega-kozierowska.jpg',
                'bozena.jpg'
            ],
            'Ryszard Ślęzak' => [
                'ryszard-slezak.jpg',
                'slezak.jpg',
                'ryszard.jpg'
            ],
            'Piotr Siekanowicz' => [
                'piotr-siekanowicz.jpg',
                'siekanowicz.jpg',
                'piotr-s.jpg'
            ]
        ];
    }
    
    /**
     * Pobiera zdjęcie lekarza
     */
    public function downloadDoctorPhoto($firstName, $lastName) {
        $fullName = trim(str_replace(['dr n. med.', 'lek.', 'mgr', 'dr n. k. f.', 'dr. n. med.', 'dr'], '', $firstName . ' ' . $lastName));
        $fullName = trim($fullName);
        
        echo "Szukam zdjęcia dla: $fullName\n";
        
        $mappings = $this->getDoctorPhotoMappings();
        $possibleNames = $mappings[$fullName] ?? [];
        
        // Jeśli nie ma mapowania, generuj nazwy automatycznie
        if (empty($possibleNames)) {
            $possibleNames = $this->generatePhotoNames($fullName);
        }
        
        // Możliwe lokalizacje zdjęć na serwerze sonokard.pl
        $baseUrls = [
            'https://sonokard.pl/fileadmin/user_upload/zespol/',
            'https://sonokard.pl/fileadmin/user_upload/',
            'https://sonokard.pl/uploads/pics/',
            'https://sonokard.pl/typo3temp/assets/images/',
            'https://sonokard.pl/fileadmin/_processed_/csm_',
            'https://sonokard.pl/fileadmin/user_upload/team/',
            'https://sonokard.pl/fileadmin/user_upload/doctors/'
        ];
        
        $extensions = ['jpg', 'jpeg', 'png', 'webp'];
        
        foreach ($baseUrls as $baseUrl) {
            foreach ($possibleNames as $name) {
                foreach ($extensions as $ext) {
                    $imageUrl = $baseUrl . $name;
                    if (pathinfo($name, PATHINFO_EXTENSION) === '') {
                        $imageUrl .= '.' . $ext;
                    }
                    
                    echo "  Próbuję: $imageUrl\n";
                    
                    $imageData = $this->downloadImage($imageUrl);
                    if ($imageData !== false) {
                        echo "  ✓ Znaleziono zdjęcie!\n";
                        return $this->saveImage($imageData, $name . '.' . $ext);
                    }
                }
            }
        }
        
        echo "  ✗ Nie znaleziono zdjęcia\n";
        return false;
    }
    
    /**
     * Generuje możliwe nazwy plików na podstawie imienia i nazwiska
     */
    private function generatePhotoNames($fullName) {
        $parts = explode(' ', $fullName);
        $firstName = strtolower($parts[0] ?? '');
        $lastName = strtolower($parts[1] ?? '');
        
        // Usuń polskie znaki
        $firstName = $this->removePolishChars($firstName);
        $lastName = $this->removePolishChars($lastName);
        
        return [
            $firstName . '-' . $lastName,
            $lastName . '-' . $firstName,
            $firstName . '_' . $lastName,
            $lastName . '_' . $firstName,
            $firstName . $lastName,
            $lastName . $firstName,
            $firstName,
            $lastName
        ];
    }
    
    /**
     * Usuwa polskie znaki
     */
    private function removePolishChars($text) {
        $polish = ['ą', 'ć', 'ę', 'ł', 'ń', 'ó', 'ś', 'ź', 'ż', 'Ą', 'Ć', 'Ę', 'Ł', 'Ń', 'Ó', 'Ś', 'Ź', 'Ż'];
        $latin = ['a', 'c', 'e', 'l', 'n', 'o', 's', 'z', 'z', 'A', 'C', 'E', 'L', 'N', 'O', 'S', 'Z', 'Z'];
        return str_replace($polish, $latin, $text);
    }
    
    /**
     * Pobiera obraz z URL
     */
    private function downloadImage($url) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_NOBODY, false);
        
        $data = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        curl_close($ch);
        
        // Sprawdź czy to rzeczywiście obraz
        if ($httpCode === 200 && strpos($contentType, 'image/') === 0 && strlen($data) > 1000) {
            return $data;
        }
        
        return false;
    }
    
    /**
     * Zapisuje obraz do katalogu uploads
     */
    private function saveImage($imageData, $originalName) {
        // Generuj unikalną nazwę pliku
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        if (empty($extension)) {
            $extension = 'jpg';
        }
        $filename = 'sonokard_' . uniqid() . '.' . $extension;
        $filepath = $this->uploadDir . $filename;
        
        if (file_put_contents($filepath, $imageData)) {
            return '/uploads/doctors/' . $filename;
        }
        
        return false;
    }
    
    /**
     * Aktualizuje URL zdjęcia w bazie danych
     */
    private function updateDoctorPhoto($doctorId, $photoUrl) {
        $stmt = $this->db->prepare("
            UPDATE queue_doctors 
            SET photo_url = ? 
            WHERE id = ?
        ");
        return $stmt->execute([$photoUrl, $doctorId]);
    }
    
    /**
     * Główna metoda pobierania zdjęć
     */
    public function downloadAllPhotos() {
        echo "Rozpoczynam pobieranie rzeczywistych zdjęć lekarzy...\n";
        
        $doctors = $this->getAllDoctors();
        echo "Znaleziono " . count($doctors) . " lekarzy\n\n";
        
        $downloaded = 0;
        $failed = 0;
        
        foreach ($doctors as $doctor) {
            echo "Pobieranie zdjęcia dla: {$doctor['first_name']} {$doctor['last_name']}\n";
            
            $photoUrl = $this->downloadDoctorPhoto($doctor['first_name'], $doctor['last_name']);
            
            if ($photoUrl) {
                if ($this->updateDoctorPhoto($doctor['id'], $photoUrl)) {
                    echo "✓ Pobrano i zapisano zdjęcie: $photoUrl\n";
                    $downloaded++;
                } else {
                    echo "✗ Błąd podczas aktualizacji bazy danych\n";
                    $failed++;
                }
            } else {
                echo "✗ Nie znaleziono zdjęcia\n";
                $failed++;
            }
            
            echo "\n";
            sleep(1); // Pauza między pobieraniami
        }
        
        echo "Podsumowanie:\n";
        echo "- Pobrano zdjęć: $downloaded\n";
        echo "- Nie znaleziono: $failed\n";
    }
}

// Uruchom pobieranie jeśli skrypt jest wywołany bezpośrednio
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $downloader = new RealDoctorPhotoDownloader();
    $downloader->downloadAllPhotos();
}
?>
