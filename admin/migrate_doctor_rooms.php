<?php
require_once 'core/Database.php';

// Inicjalizacja bazy danych
Database::init();
$db = Database::getInstance();
$pdo = $db->getConnection();

echo "Rozpoczynam migrację bazy danych dla systemu zarządzania gabinetami lekarzy...\n";

try {
    // 1. Dodaj pole default_room_id do tabeli queue_doctors
    echo "1. Do<PERSON><PERSON><PERSON> pole default_room_id do tabeli queue_doctors...\n";
    $pdo->exec("ALTER TABLE queue_doctors ADD COLUMN default_room_id INTEGER");
    echo "✓ Pole default_room_id zostało dodane\n";
    
    // 2. Dodaj pole doctor_id do tabeli queue_appointments
    echo "2. Dodaję pole doctor_id do tabeli queue_appointments...\n";
    $pdo->exec("ALTER TABLE queue_appointments ADD COLUMN doctor_id INTEGER");
    echo "✓ Pole doctor_id zostało dodane\n";
    
    // 3. Dodaj pole appointment_date do tabeli queue_appointments
    echo "3. <PERSON><PERSON><PERSON><PERSON> pole appointment_date do tabeli queue_appointments...\n";
    $pdo->exec("ALTER TABLE queue_appointments ADD COLUMN appointment_date DATE");
    echo "✓ Pole appointment_date zostało dodane\n";
    
    // 4. Dodaj pole started_at do tabeli queue_appointments
    echo "4. Dodaję pole started_at do tabeli queue_appointments...\n";
    $pdo->exec("ALTER TABLE queue_appointments ADD COLUMN started_at DATETIME");
    echo "✓ Pole started_at zostało dodane\n";
    
    // 5. Zaktualizuj istniejące wizyty - przypisz lekarzy na podstawie gabinetów
    echo "5. Aktualizuję istniejące wizyty - przypisuję lekarzy do gabinetów...\n";
    $pdo->exec("
        UPDATE queue_appointments 
        SET doctor_id = (
            SELECT doctor_id 
            FROM queue_rooms 
            WHERE queue_rooms.id = queue_appointments.room_id
        )
        WHERE doctor_id IS NULL
    ");
    echo "✓ Istniejące wizyty zostały zaktualizowane\n";
    
    // 6. Zaktualizuj domyślne gabinety lekarzy na podstawie przypisanych gabinetów
    echo "6. Aktualizuję domyślne gabinety lekarzy...\n";
    $pdo->exec("
        UPDATE queue_doctors 
        SET default_room_id = (
            SELECT id 
            FROM queue_rooms 
            WHERE queue_rooms.doctor_id = queue_doctors.id 
            LIMIT 1
        )
        WHERE default_room_id IS NULL
    ");
    echo "✓ Domyślne gabinety lekarzy zostały zaktualizowane\n";
    
    // 7. Zaktualizuj daty wizyt na dzisiejszą datę
    echo "7. Aktualizuję daty wizyt...\n";
    $pdo->exec("
        UPDATE queue_appointments 
        SET appointment_date = date('now') 
        WHERE appointment_date IS NULL
    ");
    echo "✓ Daty wizyt zostały zaktualizowane\n";
    
    // 8. Zaktualizuj statusy wizyt na nowe formaty
    echo "8. Aktualizuję statusy wizyt...\n";
    $pdo->exec("
        UPDATE queue_appointments 
        SET status = 'in_progress' 
        WHERE status = 'current'
    ");
    echo "✓ Statusy wizyt zostały zaktualizowane\n";
    
    echo "\n🎉 Migracja zakończona pomyślnie!\n";
    echo "Nowe funkcjonalności systemu zarządzania gabinetami lekarzy są gotowe.\n";
    
} catch (Exception $e) {
    echo "\n❌ Błąd podczas migracji: " . $e->getMessage() . "\n";
    echo "Szczegóły błędu: " . $e->getTraceAsString() . "\n";
}

echo "\nMigracja zakończona.\n";
?> 