# Raport Poprawki Przesunięcia Czasu w Podglądzie Mapowania

## Problem
W podglądzie mapowania data eksportu z pola `exportDate` w JSON była wyświetlana w formacie UTC bez konwersji na lokalny czas polski, co powodowało mylące informacje o czasie eksportu danych.

## Analiza Problemu

### Przykład Problemu
- **Data w JSON**: `"2025-08-26T12:35:40.964Z"` (UTC)
- **Wyświetlana data PRZED**: `2025-08-26T12:35:40.964Z` (surowy UTC)
- **Oczekiwana data**: `26.08.2025 14:35:40` (czas lokalny polski)

### Przyczyna
Pole `exportDate` w JSON zawiera datę w formacie ISO 8601 UTC (z sufiksem "Z"), ale w widoku `view_sync_data.php` by<PERSON>o wyświetlane bezpośrednio bez konwersji na lokalną strefę czasową.

## Rozwiązanie

### 1. Poprawka w Widoku
**Plik**: `admin/views/client/import/view_sync_data.php`

**Przed poprawką** (linia 76):
```php
<li><strong>Data eksportu:</strong> <?= htmlspecialchars($jsonData['exportDate'] ?? 'Brak') ?></li>
```

**Po poprawce** (linie 76-92):
```php
<li><strong>Data eksportu:</strong> 
    <?php 
    if (isset($jsonData['exportDate']) && $jsonData['exportDate']) {
        try {
            // Konwertuj UTC na lokalny czas polski
            $utcDate = new DateTime($jsonData['exportDate']);
            $utcDate->setTimezone(new DateTimeZone('Europe/Warsaw'));
            echo htmlspecialchars($utcDate->format('d.m.Y H:i:s'));
            echo ' <small class="text-muted">(czas lokalny)</small>';
        } catch (Exception $e) {
            echo htmlspecialchars($jsonData['exportDate']);
            echo ' <small class="text-muted">(błąd konwersji)</small>';
        }
    } else {
        echo 'Brak';
    }
    ?>
</li>
```

### 2. Naprawka Błędu Składni
Dodatkowo naprawiono błąd składni PHP - brakujący otwierający tag `if` dla sekcji wizyt (linia 149).

## Funkcjonalność

### Konwersja Strefy Czasowej
- **Strefa źródłowa**: UTC (Coordinated Universal Time)
- **Strefa docelowa**: Europe/Warsaw (czas polski)
- **Automatyczne uwzględnienie**: 
  - Czas zimowy (UTC+1)
  - Czas letni (UTC+2)

### Przykłady Konwersji
1. **Lato 2025**: `2025-08-26T12:35:40.964Z` → `26.08.2025 14:35:40` (+2h)
2. **Zima 2025**: `2025-12-15T14:30:00.000Z` → `15.12.2025 15:30:00` (+1h)

### Obsługa Błędów
- Jeśli konwersja się nie powiedzie, wyświetlana jest oryginalna data z oznaczeniem błędu
- Graceful degradation - system nie przestaje działać przy błędach

## Testy Wykonane

### Test 1: Konwersja Podstawowa
```
Input:  "2025-08-26T12:35:40.964Z"
Output: "26.08.2025 14:35:40 (czas lokalny)"
Status: ✅ PASS
```

### Test 2: Różne Pory Roku
```
Lato:   UTC+2 (CEST) ✅
Zima:   UTC+1 (CET)  ✅
Status: ✅ PASS
```

### Test 3: Obsługa Błędów
```
Nieprawidłowy format: Wyświetla oryginalną datę + "(błąd konwersji)"
Brak daty: Wyświetla "Brak"
Status: ✅ PASS
```

### Test 4: Składnia PHP
```
php -l views/client/import/view_sync_data.php
Output: "No syntax errors detected"
Status: ✅ PASS
```

## Wpływ na Użytkowników

### Przed Poprawką
- ❌ Mylące informacje o czasie eksportu
- ❌ Konieczność ręcznego przeliczania UTC na czas lokalny
- ❌ Potencjalne błędy w interpretacji czasu

### Po Poprawce
- ✅ Jasne wyświetlanie czasu lokalnego
- ✅ Automatyczna konwersja stref czasowych
- ✅ Oznaczenie "(czas lokalny)" dla przejrzystości
- ✅ Obsługa błędów konwersji

## Lokalizacja Zmian

### Zmodyfikowane Pliki
1. `admin/views/client/import/view_sync_data.php` - główna poprawka
2. `admin/test_time_conversion.php` - skrypt testowy (nowy)
3. `admin/timezone_fix_report.md` - ten raport (nowy)

### Nie Wymagają Zmian
- Inne widoki używają już lokalnych dat
- API zwraca daty w odpowiednich formatach
- Baza danych przechowuje daty w lokalnym czasie

## Kompatybilność

### Wsteczna Kompatybilność
- ✅ Istniejące dane JSON pozostają niezmienione
- ✅ Stary format dat nadal jest obsługiwany
- ✅ Brak wpływu na inne funkcjonalności

### Przyszłe Dane
- ✅ Nowe eksporty będą poprawnie wyświetlane
- ✅ Automatyczna obsługa zmian czasu (lato/zima)
- ✅ Zgodność z międzynarodowymi standardami

## Podsumowanie

✅ **Problem został całkowicie rozwiązany**

Data eksportu w podglądzie mapowania jest teraz wyświetlana w lokalnym czasie polskim z automatyczną konwersją z UTC. Użytkownicy widzą rzeczywisty czas eksportu danych zgodny z ich strefą czasową.

## Dodatkowe Poprawki

### 3. Poprawka Wyświetlania Daty "Ostatnio widziany"
**Problem**: Data `last_seen` w tabeli mapowań była wyświetlana bez uwzględnienia strefy czasowej.

**Pliki poprawione**:
- `admin/views/client/import/doctor_mappings.php` (linie 146-167)
- `admin/views/admin/import/doctor_mappings.php` (linie 143-165)

**Logika konwersji**:
```php
// Sprawdź czy data zawiera informację o strefie czasowej
if (strpos($mapping['last_seen'], 'T') !== false && strpos($mapping['last_seen'], 'Z') !== false) {
    // Data w formacie ISO 8601 UTC - konwertuj na lokalny czas
    $utcDate = new DateTime($mapping['last_seen']);
    $utcDate->setTimezone(new DateTimeZone('Europe/Warsaw'));
    echo $utcDate->format('d.m.Y H:i');
} else {
    // Data prawdopodobnie już w lokalnym czasie
    echo date('d.m.Y H:i', strtotime($mapping['last_seen']));
}
```

### 4. Poprawka Zapisywania Daty "Ostatnio widziany"
**Problem**: Pole `last_seen` było zapisywane w lokalnym czasie zamiast UTC.

**Plik**: `admin/models/ExternalDoctorMapping.php`

**Przed poprawką**:
```php
SET last_seen = CURRENT_TIMESTAMP
```

**Po poprawce**:
```php
$utcTime = gmdate('Y-m-d\TH:i:s\Z');
SET last_seen = ?
```

**Korzyści**:
- ✅ Spójność z formatem `exportDate` z JSON
- ✅ Jednoznaczna strefa czasowa (UTC)
- ✅ Automatyczna konwersja w widoku
- ✅ Zgodność z międzynarodowymi standardami

## Przykład Działania

### Przed Wszystkimi Poprawkami
- **Data eksportu**: `2025-08-26T14:50:31.168Z` (surowy UTC)
- **Ostatnio widziany**: `26.08.2025 14:50` (mylący lokalny czas)

### Po Wszystkich Poprawkach
- **Data eksportu**: `26.08.2025 16:50:31 (czas lokalny)` (poprawny lokalny czas)
- **Ostatnio widziany**: `26.08.2025 16:50` (poprawny lokalny czas)

**Data poprawki**: 26 sierpnia 2025
**Status**: ✅ CAŁKOWICIE ROZWIĄZANE
