<?php
echo "=== TEST KONWERSJI CZASU Z UTC NA LOKALNY ===\n\n";

// Przykładowe daty z JSON
$testDates = [
    "2025-08-26T12:35:40.964Z",
    "2025-08-26T10:00:16.938Z",
    "2025-12-15T14:30:00.000Z", // Zima (UTC+1)
    "2025-06-15T14:30:00.000Z"  // Lato (UTC+2)
];

foreach ($testDates as $utcDateString) {
    echo "Data UTC: $utcDateString\n";
    
    try {
        // Konwertuj UTC na lokalny czas polski
        $utcDate = new DateTime($utcDateString);
        echo "Parsed UTC: " . $utcDate->format('Y-m-d H:i:s T') . "\n";
        
        $utcDate->setTimezone(new DateTimeZone('Europe/Warsaw'));
        $localTime = $utcDate->format('d.m.Y H:i:s');
        $timezone = $utcDate->format('T');
        
        echo "Czas lokalny: $localTime ($timezone)\n";
        echo "Różnica: " . $utcDate->format('P') . "\n";
        
    } catch (Exception $e) {
        echo "Błąd: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

echo "=== TEST SYMULACJI WIDOKU ===\n\n";

// Symuluj to co dzieje się w widoku
$jsonData = [
    'exportDate' => '2025-08-26T12:35:40.964Z',
    'syncCode' => 'igab000000000001'
];

echo "Symulacja kodu z widoku:\n";
echo "Oryginalna data: " . $jsonData['exportDate'] . "\n";

if (isset($jsonData['exportDate']) && $jsonData['exportDate']) {
    try {
        // Konwertuj UTC na lokalny czas polski
        $utcDate = new DateTime($jsonData['exportDate']);
        $utcDate->setTimezone(new DateTimeZone('Europe/Warsaw'));
        $displayDate = $utcDate->format('d.m.Y H:i:s');
        echo "Wyświetlana data: $displayDate (czas lokalny)\n";
    } catch (Exception $e) {
        echo "Błąd konwersji: " . $e->getMessage() . "\n";
    }
} else {
    echo "Brak daty\n";
}

echo "\n=== PORÓWNANIE PRZED I PO ZMIANIE ===\n";
echo "PRZED: 2025-08-26T12:35:40.964Z (surowy UTC)\n";
echo "PO:    26.08.2025 14:35:40 (czas lokalny)\n";
echo "Różnica: +2 godziny (czas letni w Polsce)\n";
?>
