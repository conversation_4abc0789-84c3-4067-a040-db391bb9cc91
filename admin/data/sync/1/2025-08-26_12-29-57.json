{"exportDate": "2025-08-26T12:29:57.679Z", "syncCode": "igab000000000001", "syncData": {"days": [{"date": "2025-08-26", "doctors": [{"doctorId": "43", "doctorName": "Lekarz ID 43", "appointments": [{"appointmentId": 106689, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "09:00", "appointmentEnd": "09:20", "appointmentDuration": 20}, {"appointmentId": 108195, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "09:00", "appointmentEnd": "09:20", "appointmentDuration": 20}, {"appointmentId": 103403, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "09:20", "appointmentEnd": "09:40", "appointmentDuration": 20}, {"appointmentId": 107064, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "09:20", "appointmentEnd": "09:40", "appointmentDuration": 20}, {"appointmentId": 103920, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "09:40", "appointmentEnd": "10:00", "appointmentDuration": 20}, {"appointmentId": 103066, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "10:00", "appointmentEnd": "10:20", "appointmentDuration": 20}, {"appointmentId": 104947, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "10:20", "appointmentEnd": "10:40", "appointmentDuration": 20}, {"appointmentId": 104555, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "10:40", "appointmentEnd": "11:10", "appointmentDuration": 30}, {"appointmentId": 104880, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "11:10", "appointmentEnd": "11:30", "appointmentDuration": 20}, {"appointmentId": 108314, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "11:30", "appointmentEnd": "11:50", "appointmentDuration": 20}, {"appointmentId": 105187, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "12:10", "appointmentEnd": "12:30", "appointmentDuration": 20}, {"appointmentId": 108422, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "12:35", "appointmentEnd": "12:55", "appointmentDuration": 20}, {"appointmentId": 103689, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "12:40", "appointmentEnd": "13:00", "appointmentDuration": 20}, {"appointmentId": 107649, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "13:00", "appointmentEnd": "13:20", "appointmentDuration": 20}, {"appointmentId": 107931, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "13:20", "appointmentEnd": "13:40", "appointmentDuration": 20}, {"appointmentId": 106686, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "13:55", "appointmentEnd": "14:15", "appointmentDuration": 20}, {"appointmentId": 108417, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "14:15", "appointmentEnd": "14:35", "appointmentDuration": 20}, {"appointmentId": 108029, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "08:00", "appointmentEnd": "08:15", "appointmentDuration": 15}, {"appointmentId": 102981, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "09:00", "appointmentEnd": "14:00", "appointmentDuration": 300}, {"appointmentId": 108315, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "11:50", "appointmentEnd": "12:10", "appointmentDuration": 20}, {"appointmentId": 108295, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "13:20", "appointmentEnd": "13:50", "appointmentDuration": 30}, {"appointmentId": 105327, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "13:50", "appointmentEnd": "14:20", "appointmentDuration": 30}, {"appointmentId": 108253, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "14:20", "appointmentEnd": "14:40", "appointmentDuration": 20}, {"appointmentId": 108319, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "14:40", "appointmentEnd": "15:00", "appointmentDuration": 20}, {"appointmentId": 101876, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "15:00", "appointmentEnd": "15:20", "appointmentDuration": 20}, {"appointmentId": 104706, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "15:20", "appointmentEnd": "15:40", "appointmentDuration": 20}, {"appointmentId": 108390, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "15:20", "appointmentEnd": "15:40", "appointmentDuration": 20}, {"appointmentId": 104515, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "15:40", "appointmentEnd": "16:00", "appointmentDuration": 20}, {"appointmentId": 108328, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "15:40", "appointmentEnd": "16:00", "appointmentDuration": 20}, {"appointmentId": 104852, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "16:00", "appointmentEnd": "19:00", "appointmentDuration": 180}, {"appointmentId": 105536, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "16:00", "appointmentEnd": "16:20", "appointmentDuration": 20}, {"appointmentId": 106332, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "16:00", "appointmentEnd": "16:20", "appointmentDuration": 20}, {"appointmentId": 108293, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "16:00", "appointmentEnd": "16:20", "appointmentDuration": 20}, {"appointmentId": 105577, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "16:20", "appointmentEnd": "16:40", "appointmentDuration": 20}, {"appointmentId": 108326, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "16:20", "appointmentEnd": "16:40", "appointmentDuration": 20}, {"appointmentId": 105681, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "16:40", "appointmentEnd": "17:00", "appointmentDuration": 20}, {"appointmentId": 108320, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "16:40", "appointmentEnd": "17:00", "appointmentDuration": 20}, {"appointmentId": 105508, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "17:00", "appointmentEnd": "17:20", "appointmentDuration": 20}, {"appointmentId": 106478, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "17:00", "appointmentEnd": "17:20", "appointmentDuration": 20}, {"appointmentId": 108318, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "17:00", "appointmentEnd": "17:20", "appointmentDuration": 20}, {"appointmentId": 105995, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "17:20", "appointmentEnd": "17:40", "appointmentDuration": 20}, {"appointmentId": 106697, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "17:20", "appointmentEnd": "17:40", "appointmentDuration": 20}, {"appointmentId": 108345, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "17:20", "appointmentEnd": "17:40", "appointmentDuration": 20}, {"appointmentId": 106111, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "17:40", "appointmentEnd": "18:00", "appointmentDuration": 20}, {"appointmentId": 106624, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "17:40", "appointmentEnd": "18:00", "appointmentDuration": 20}, {"appointmentId": 108021, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "18:00", "appointmentEnd": "18:20", "appointmentDuration": 20}, {"appointmentId": 108288, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "18:00", "appointmentEnd": "18:20", "appointmentDuration": 20}, {"appointmentId": 106605, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "18:20", "appointmentEnd": "18:40", "appointmentDuration": 20}, {"appointmentId": 108190, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "18:20", "appointmentEnd": "18:40", "appointmentDuration": 20}, {"appointmentId": 108329, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "18:40", "appointmentEnd": "19:00", "appointmentDuration": 20}, {"appointmentId": 108286, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "18:45", "appointmentEnd": "18:50", "appointmentDuration": 5}, {"appointmentId": 108369, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "18:50", "appointmentEnd": "18:55", "appointmentDuration": 5}, {"appointmentId": 108384, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "18:55", "appointmentEnd": "19:00", "appointmentDuration": 5}, {"appointmentId": 107466, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "19:00", "appointmentEnd": "19:20", "appointmentDuration": 20}, {"appointmentId": 108419, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "19:00", "appointmentEnd": "19:10", "appointmentDuration": 10}, {"appointmentId": 108333, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "19:20", "appointmentEnd": "19:40", "appointmentDuration": 20}, {"appointmentId": 108334, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "19:40", "appointmentEnd": "20:00", "appointmentDuration": 20}, {"appointmentId": 106582, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "20:00", "appointmentEnd": "20:05", "appointmentDuration": 5}, {"appointmentId": 108403, "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "", "appointmentStart": "20:25", "appointmentEnd": "20:30", "appointmentDuration": 5}]}, {"doctorId": "10", "doctorName": "ginekolog dr n.med. Małgorzata Ole<PERSON>k-And<PERSON>sz<PERSON>ak", "appointments": []}, {"doctorId": "20", "doctorName": "ginekolog lek. <PERSON>", "appointments": []}, {"doctorId": "83", "doctorName": "Lekarz ID 83", "appointments": []}, {"doctorId": "**********", "doctorName": "Pielęgniarka / Położna", "appointments": []}]}]}}