<?php
require_once 'core/Database.php';

class SonokardSyncSummary {
    private $db;
    private $sonokardClientId = 2;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    /**
     * Wyświetla podsumowanie synchronizacji
     */
    public function displaySummary() {
        echo "=== PODSUMOWANIE SYNCHRONIZACJI LEKARZY SONOKARD ===\n\n";
        
        // Statystyki ogólne
        $this->displayGeneralStats();
        
        echo "\n";
        
        // Lista wszystkich lekarzy
        $this->displayAllDoctors();
        
        echo "\n";
        
        // Nowo dodani lekarze
        $this->displayNewDoctors();
    }
    
    /**
     * Wyświetla statystyki ogólne
     */
    private function displayGeneralStats() {
        // Łączna liczba lekarzy
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as total 
            FROM queue_doctors 
            WHERE client_id = ? AND active = 1
        ");
        $stmt->execute([$this->sonokardClientId]);
        $total = $stmt->fetchColumn();
        
        // Lekarze ze zdjęciami
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as with_photos 
            FROM queue_doctors 
            WHERE client_id = ? AND active = 1 AND photo_url IS NOT NULL AND photo_url != ''
        ");
        $stmt->execute([$this->sonokardClientId]);
        $withPhotos = $stmt->fetchColumn();
        
        // Lekarze bez zdjęć
        $withoutPhotos = $total - $withPhotos;
        
        echo "STATYSTYKI OGÓLNE:\n";
        echo "- Łączna liczba aktywnych lekarzy: $total\n";
        echo "- Lekarze ze zdjęciami: $withPhotos\n";
        echo "- Lekarze bez zdjęć: $withoutPhotos\n";
        echo "- Pokrycie zdjęciami: " . round(($withPhotos / $total) * 100, 1) . "%\n";
    }
    
    /**
     * Wyświetla listę wszystkich lekarzy
     */
    private function displayAllDoctors() {
        $stmt = $this->db->prepare("
            SELECT id, first_name, last_name, specialization, photo_url, access_code
            FROM queue_doctors 
            WHERE client_id = ? AND active = 1 
            ORDER BY last_name, first_name
        ");
        $stmt->execute([$this->sonokardClientId]);
        $doctors = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "LISTA WSZYSTKICH LEKARZY:\n";
        echo str_pad("ID", 4) . str_pad("Imię i nazwisko", 40) . str_pad("Specjalizacja", 30) . "Zdjęcie\n";
        echo str_repeat("-", 100) . "\n";
        
        foreach ($doctors as $doctor) {
            $fullName = trim($doctor['first_name'] . ' ' . $doctor['last_name']);
            $specialization = substr($doctor['specialization'], 0, 28);
            $hasPhoto = !empty($doctor['photo_url']) ? "✓" : "✗";
            
            echo str_pad($doctor['id'], 4) . 
                 str_pad($fullName, 40) . 
                 str_pad($specialization, 30) . 
                 $hasPhoto . "\n";
        }
        
        echo "\nŁącznie: " . count($doctors) . " lekarzy\n";
    }
    
    /**
     * Wyświetla nowo dodanych lekarzy (ID > 16)
     */
    private function displayNewDoctors() {
        $stmt = $this->db->prepare("
            SELECT id, first_name, last_name, specialization, photo_url, access_code, created_at
            FROM queue_doctors 
            WHERE client_id = ? AND active = 1 AND id > 16
            ORDER BY id
        ");
        $stmt->execute([$this->sonokardClientId]);
        $newDoctors = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "NOWO DODANI LEKARZE:\n";
        echo str_pad("ID", 4) . str_pad("Imię i nazwisko", 40) . str_pad("Kod dostępu", 15) . "Data dodania\n";
        echo str_repeat("-", 80) . "\n";
        
        foreach ($newDoctors as $doctor) {
            $fullName = trim($doctor['first_name'] . ' ' . $doctor['last_name']);
            $accessCode = $doctor['access_code'] ?? 'brak';
            $createdAt = $doctor['created_at'] ?? 'nieznana';
            
            echo str_pad($doctor['id'], 4) . 
                 str_pad($fullName, 40) . 
                 str_pad($accessCode, 15) . 
                 $createdAt . "\n";
        }
        
        echo "\nNowo dodanych: " . count($newDoctors) . " lekarzy\n";
    }
    
    /**
     * Eksportuje dane do CSV
     */
    public function exportToCSV($filename = 'sonokard_doctors.csv') {
        $stmt = $this->db->prepare("
            SELECT id, first_name, last_name, specialization, photo_url, access_code, created_at
            FROM queue_doctors 
            WHERE client_id = ? AND active = 1 
            ORDER BY id
        ");
        $stmt->execute([$this->sonokardClientId]);
        $doctors = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $fp = fopen($filename, 'w');
        
        // Nagłówki CSV
        fputcsv($fp, ['ID', 'Imię', 'Nazwisko', 'Specjalizacja', 'URL zdjęcia', 'Kod dostępu', 'Data utworzenia']);
        
        // Dane lekarzy
        foreach ($doctors as $doctor) {
            fputcsv($fp, [
                $doctor['id'],
                $doctor['first_name'],
                $doctor['last_name'],
                $doctor['specialization'],
                $doctor['photo_url'],
                $doctor['access_code'],
                $doctor['created_at']
            ]);
        }
        
        fclose($fp);
        
        echo "Eksport do CSV zakończony: $filename\n";
        echo "Wyeksportowano " . count($doctors) . " lekarzy\n";
    }
}

// Uruchom podsumowanie jeśli skrypt jest wywołany bezpośrednio
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $summary = new SonokardSyncSummary();
    
    // Sprawdź argumenty wiersza poleceń
    if (isset($argv[1]) && $argv[1] === 'csv') {
        $filename = $argv[2] ?? 'sonokard_doctors.csv';
        $summary->exportToCSV($filename);
    } else {
        $summary->displaySummary();
    }
}
?>
