<?php

class Campaign {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    public function getAll() {
        $stmt = $this->db->query("
            SELECT c.*, u.username, u.company_name, 
                   (SELECT COUNT(*) FROM ad_views av WHERE av.campaign_id = c.id) as views,
                   cat.name as category_name
            FROM campaigns c
            JOIN users u ON c.advertiser_id = u.id
            LEFT JOIN categories cat ON c.category_id = cat.id
            ORDER BY c.created_at DESC
        ");
        return $stmt->fetchAll();
    }
    
    public function getById($id) {
        $stmt = $this->db->prepare("
            SELECT c.*, u.username, u.company_name
            FROM campaigns c
            JOIN users u ON c.advertiser_id = u.id
            WHERE c.id = ?
        ");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }
    
    public function add($campaignData, $mediaInfo) {
        $stmt = $this->db->prepare("
            INSERT INTO campaigns (
                advertiser_id, name, description, media_type, media_url, youtube_id,
                duration, budget, max_frequency_per_hour, start_date, end_date, is_active, category_id
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        return $stmt->execute([
            $campaignData['advertiser_id'],
            $campaignData['name'],
            $campaignData['description'],
            $campaignData['media_type'],
            $mediaInfo['media_url'],
            $mediaInfo['youtube_id'],
            intval($campaignData['duration'] ?? 30),
            floatval($campaignData['budget'] ?? 0),
            intval($campaignData['max_frequency_per_hour'] ?? 0),
            $campaignData['start_date'],
            $campaignData['end_date'],
            isset($campaignData['is_active']) ? 1 : 0,
            $campaignData['category_id']
        ]);
    }
    
    public function update($id, $campaignData, $mediaInfo = null) {
        if ($mediaInfo) {
            $stmt = $this->db->prepare("
                UPDATE campaigns 
                SET advertiser_id = ?, name = ?, description = ?, 
                    media_type = ?, media_url = ?, youtube_id = ?,
                    duration = ?, budget = ?, max_frequency_per_hour = ?, start_date = ?, 
                    end_date = ?, is_active = ?, category_id = ?
                WHERE id = ?
            ");
            
            return $stmt->execute([
                $campaignData['advertiser_id'],
                $campaignData['name'],
                $campaignData['description'],
                $campaignData['media_type'],
                $mediaInfo['media_url'],
                $mediaInfo['youtube_id'],
                intval($campaignData['duration'] ?? 30),
                floatval($campaignData['budget'] ?? 0),
                intval($campaignData['max_frequency_per_hour'] ?? 0),
                $campaignData['start_date'],
                $campaignData['end_date'],
                isset($campaignData['is_active']) ? 1 : 0,
                $campaignData['category_id'],
                $id
            ]);
        } else {
            $stmt = $this->db->prepare("
                UPDATE campaigns 
                SET advertiser_id = ?, name = ?, description = ?, 
                    duration = ?, budget = ?, max_frequency_per_hour = ?, start_date = ?, 
                    end_date = ?, is_active = ?, category_id = ?
                WHERE id = ?
            ");
            
            return $stmt->execute([
                $campaignData['advertiser_id'],
                $campaignData['name'],
                $campaignData['description'],
                intval($campaignData['duration'] ?? 30),
                floatval($campaignData['budget'] ?? 0),
                intval($campaignData['max_frequency_per_hour'] ?? 0),
                $campaignData['start_date'],
                $campaignData['end_date'],
                isset($campaignData['is_active']) ? 1 : 0,
                $campaignData['category_id'],
                $id
            ]);
        }
    }
    
    public function delete($id) {
        $stmt = $this->db->prepare("DELETE FROM campaigns WHERE id = ?");
        return $stmt->execute([$id]);
    }
    
    public function hasViews($id) {
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM ad_views WHERE campaign_id = ?");
        $stmt->execute([$id]);
        return $stmt->fetchColumn() > 0;
    }
    
    public function getTopCampaigns($limit = 10) {
        $stmt = $this->db->query("
            SELECT c.name, c.media_type, COUNT(av.id) as views, SUM(av.cost) as revenue,
                   u.company_name as advertiser
            FROM campaigns c
            LEFT JOIN ad_views av ON c.id = av.campaign_id
            JOIN users u ON c.advertiser_id = u.id
            GROUP BY c.id
            ORDER BY views DESC
            LIMIT " . intval($limit)
        );
        return $stmt->fetchAll();
    }
    
    public function getCampaignAssignments($campaignId) {
        $stmt = $this->db->prepare("
            SELECT ca.*, u.username, u.company_name
            FROM campaign_assignments ca
            JOIN users u ON ca.client_id = u.id
            WHERE ca.campaign_id = ?
        ");
        $stmt->execute([$campaignId]);
        $assignments = $stmt->fetchAll();
        
        // Przekształć na mapę dla łatwiejszego dostępu
        $assignmentMap = [];
        foreach ($assignments as $assignment) {
            $assignmentMap[$assignment['client_id']] = $assignment;
        }
        
        return $assignmentMap;
    }
    
    public function assignCampaignToClient($campaignId, $clientId, $isAccepted = 1) {
        // Sprawdź czy przypisanie już istnieje
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM campaign_assignments 
            WHERE campaign_id = ? AND client_id = ?
        ");
        $stmt->execute([$campaignId, $clientId]);
        $exists = $stmt->fetchColumn() > 0;
        
        if ($exists) {
            // Aktualizuj istniejące przypisanie
            $stmt = $this->db->prepare("
                UPDATE campaign_assignments 
                SET is_accepted = ?
                WHERE campaign_id = ? AND client_id = ?
            ");
            return $stmt->execute([$isAccepted, $campaignId, $clientId]);
        } else {
            // Dodaj nowe przypisanie
            $stmt = $this->db->prepare("
                INSERT INTO campaign_assignments (campaign_id, client_id, is_accepted)
                VALUES (?, ?, ?)
            ");
            return $stmt->execute([$campaignId, $clientId, $isAccepted]);
        }
    }
    
    public function forceAcceptCategory($categoryId, $clientIds = [], $forceAll = false) {
        if ($forceAll) {
            // Wymuś akceptację kategorii dla wszystkich klientów
            // Usuń istniejące automatyczne akceptacje dla tej kategorii
            $stmt = $this->db->prepare("
                DELETE FROM campaign_auto_accept WHERE category_id = ?
            ");
            $stmt->execute([$categoryId]);
            
            // Dodaj nowe automatyczne akceptacje
            $stmt = $this->db->prepare("
                INSERT INTO campaign_auto_accept (client_id, category_id)
                SELECT id, ? FROM users WHERE role = 'client' AND is_active = 1
            ");
            $stmt->execute([$categoryId]);
            
            // Zaktualizuj istniejące przypisania
            $stmt = $this->db->prepare("
                UPDATE campaign_assignments
                SET is_accepted = 1
                WHERE campaign_id IN (
                    SELECT id FROM campaigns WHERE category_id = ?
                )
            ");
            return $stmt->execute([$categoryId]);
        } elseif (!empty($clientIds)) {
            $success = true;
            
            foreach ($clientIds as $clientId) {
                // Sprawdź czy automatyczna akceptacja już istnieje
                $stmt = $this->db->prepare("
                    SELECT COUNT(*) FROM campaign_auto_accept 
                    WHERE client_id = ? AND category_id = ?
                ");
                $stmt->execute([$clientId, $categoryId]);
                $exists = $stmt->fetchColumn() > 0;
                
                if (!$exists) {
                    // Dodaj nową automatyczną akceptację
                    $stmt = $this->db->prepare("
                        INSERT INTO campaign_auto_accept (client_id, category_id)
                        VALUES (?, ?)
                    ");
                    $result = $stmt->execute([$clientId, $categoryId]);
                    if (!$result) $success = false;
                }
                
                // Zaktualizuj istniejące przypisania dla tego klienta
                $stmt = $this->db->prepare("
                    UPDATE campaign_assignments
                    SET is_accepted = 1
                    WHERE campaign_id IN (
                        SELECT id FROM campaigns WHERE category_id = ?
                    )
                    AND client_id = ?
                ");
                $result = $stmt->execute([$categoryId, $clientId]);
                if (!$result) $success = false;
            }
            
            return $success;
        }
        
        return false;
    }
    
    public function deleteAssignments($campaignId) {
        $stmt = $this->db->prepare("DELETE FROM campaign_assignments WHERE campaign_id = ?");
        return $stmt->execute([$campaignId]);
    }
    
    public function processMediaUpload($mediaType, $file = null, $youtubeUrl = null) {
        $mediaInfo = [
            'media_url' => '',
            'youtube_id' => null
        ];
        
        if ($mediaType === 'youtube' && !empty($youtubeUrl)) {
            // Wyodrębnij ID filmu z URL
            preg_match('/(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/', $youtubeUrl, $matches);
            if (isset($matches[1])) {
                $mediaInfo['youtube_id'] = $matches[1];
                $mediaInfo['media_url'] = 'https://www.youtube.com/embed/' . $matches[1];
                return $mediaInfo;
            }
            return false;
        } elseif ($file && $file['error'] === UPLOAD_ERR_OK) {
            $allowed_types = [
                'image' => ['image/jpeg', 'image/png', 'image/gif'],
                'video' => ['video/mp4', 'video/webm', 'video/ogg']
            ];
            
            if (!in_array($file['type'], $allowed_types[$mediaType] ?? [])) {
                return false;
            }
            
            $upload_dir = __DIR__ . '/../../uploads/campaigns/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $filename = uniqid() . '_' . basename($file['name']);
            $upload_path = $upload_dir . $filename;
            
            if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                $mediaInfo['media_url'] = '/' . $upload_path;
                return $mediaInfo;
            }
        }
        
        return false;
    }
} 