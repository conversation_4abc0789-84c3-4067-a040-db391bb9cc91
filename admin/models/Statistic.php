<?php

class Statistic {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    public function getDashboardStats() {
        $stats = [];
        
        // Liczba użytkowników
        $stmt = $this->db->query("SELECT role, COUNT(*) as count FROM users WHERE role != 'admin' GROUP BY role");
        $userStats = $stmt->fetchAll();
        foreach ($userStats as $stat) {
            $stats['users'][$stat['role']] = $stat['count'];
        }
        
        // Liczba kampanii
        $stmt = $this->db->query("SELECT COUNT(*) as count FROM campaigns");
        $stats['campaigns'] = $stmt->fetchColumn();
        
        // Łączne wyświetlenia
        $stmt = $this->db->query("SELECT COUNT(*) as views, SUM(cost) as revenue FROM ad_views");
        $result = $stmt->fetch();
        $stats['views'] = $result['views'];
        $stats['revenue'] = $result['revenue'] ?? 0;
        
        return $stats;
    }
    
    public function getDailyStats($days = 30) {
        $stmt = $this->db->query("
            SELECT DATE(timestamp) as date, COUNT(*) as views, SUM(cost) as revenue
            FROM ad_views 
            WHERE timestamp >= date('now', '-" . intval($days) . " days')
            GROUP BY DATE(timestamp)
            ORDER BY date DESC
        ");
        return $stmt->fetchAll();
    }
    
    public function getTopCampaigns($limit = 10) {
        $stmt = $this->db->query("
            SELECT c.name, c.media_type, COUNT(av.id) as views, SUM(av.cost) as revenue,
                   u.company_name as advertiser
            FROM campaigns c
            LEFT JOIN ad_views av ON c.id = av.campaign_id
            JOIN users u ON c.advertiser_id = u.id
            GROUP BY c.id
            ORDER BY views DESC
            LIMIT " . intval($limit)
        );
        return $stmt->fetchAll();
    }
    
    public function getTopClients($limit = 10) {
        $stmt = $this->db->query("
            SELECT u.company_name, COUNT(av.id) as views, SUM(av.cost) as revenue
            FROM users u
            LEFT JOIN ad_views av ON u.id = av.client_id
            WHERE u.role = 'client'
            GROUP BY u.id
            ORDER BY views DESC
            LIMIT " . intval($limit)
        );
        return $stmt->fetchAll();
    }
} 