<?php

class User {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    public function getAll($excludeAdmin = true) {
        $query = "SELECT id, username, email, role, company_name, balance, is_active, 
                   last_activity, created_at 
                FROM users";
        
        if ($excludeAdmin) {
            $query .= " WHERE role != 'admin'";
        }
        
        $query .= " ORDER BY created_at DESC";
        
        $stmt = $this->db->query($query);
        return $stmt->fetchAll();
    }
    
    public function getById($id) {
        $stmt = $this->db->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }
    
    public function add($userData) {
        $stmt = $this->db->prepare("
            INSERT INTO users (username, email, password, role, company_name, balance, is_active)
            VALUES (?, ?, ?, ?, ?, ?, 1)
        ");
        
        return $stmt->execute([
            $userData['username'],
            $userData['email'],
            password_hash($userData['password'], PASSWORD_DEFAULT),
            $userData['role'],
            $userData['company_name'],
            floatval($userData['balance'] ?? 0)
        ]);
    }
    
    public function update($id, $userData) {
        if (!empty($userData['password'])) {
            $stmt = $this->db->prepare("
                UPDATE users 
                SET email = ?, role = ?, company_name = ?, balance = ?, 
                    is_active = ?, password = ?
                WHERE id = ?
            ");
            
            return $stmt->execute([
                $userData['email'],
                $userData['role'],
                $userData['company_name'],
                floatval($userData['balance'] ?? 0),
                isset($userData['is_active']) ? 1 : 0,
                password_hash($userData['password'], PASSWORD_DEFAULT),
                $id
            ]);
        } else {
            $stmt = $this->db->prepare("
                UPDATE users 
                SET email = ?, role = ?, company_name = ?, balance = ?, 
                    is_active = ?
                WHERE id = ?
            ");
            
            return $stmt->execute([
                $userData['email'],
                $userData['role'],
                $userData['company_name'],
                floatval($userData['balance'] ?? 0),
                isset($userData['is_active']) ? 1 : 0,
                $id
            ]);
        }
    }
    
    public function delete($id) {
        $stmt = $this->db->prepare("DELETE FROM users WHERE id = ?");
        return $stmt->execute([$id]);
    }
    
    public function isUsernameUnique($username, $excludeId = null) {
        if ($excludeId) {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM users WHERE username = ? AND id != ?");
            $stmt->execute([$username, $excludeId]);
        } else {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
            $stmt->execute([$username]);
        }
        return $stmt->fetchColumn() == 0;
    }
    
    public function isEmailUnique($email, $excludeId = null) {
        if ($excludeId) {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM users WHERE email = ? AND id != ?");
            $stmt->execute([$email, $excludeId]);
        } else {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
            $stmt->execute([$email]);
        }
        return $stmt->fetchColumn() == 0;
    }
    
    public function hasCampaigns($id) {
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM campaigns WHERE advertiser_id = ?");
        $stmt->execute([$id]);
        return $stmt->fetchColumn() > 0;
    }
    
    public function hasViews($id) {
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM ad_views WHERE client_id = ?");
        $stmt->execute([$id]);
        return $stmt->fetchColumn() > 0;
    }
    
    public function hasDisplays($id) {
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM client_displays WHERE client_id = ?");
        $stmt->execute([$id]);
        return $stmt->fetchColumn() > 0;
    }
    
    public function getActiveClients() {
        $stmt = $this->db->query("SELECT id, username, company_name FROM users WHERE role = 'client' AND is_active = 1");
        return $stmt->fetchAll();
    }
    
    public function getOnlineStatus() {
        $stmt = $this->db->query("
            SELECT u.username, u.company_name, cd.display_name, cd.is_online, cd.last_heartbeat
            FROM users u 
            LEFT JOIN client_displays cd ON u.id = cd.client_id 
            WHERE u.role = 'client'
        ");
        return $stmt->fetchAll();
    }

    /**
     * Pobierz wszystkich klientów (użytkowników z rolą 'client')
     */
    public function getClients() {
        $stmt = $this->db->query("
            SELECT id, username, company_name, is_active 
            FROM users 
            WHERE role = 'client' 
            ORDER BY company_name, username
        ");
        return $stmt->fetchAll();
    }
} 