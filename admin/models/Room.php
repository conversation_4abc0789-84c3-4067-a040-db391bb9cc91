<?php

class Room {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    // Pobiera wszystkie sale dla danego klienta (z informacjami o lekarzach)
    public function getByClientId($clientId) {
        $stmt = $this->db->prepare("
            SELECT r.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name, 
                   d.photo_url as doctor_photo, d.specialization as doctor_specialization
            FROM queue_rooms r
            LEFT JOIN queue_doctors d ON r.doctor_id = d.id
            WHERE r.client_id = ? AND r.active = 1
            ORDER BY r.name
        ");
        $stmt->execute([$clientId]);
        return $stmt->fetchAll();
    }
    
    // Pobiera salę po ID i client_id
    public function getByIdAndClientId($roomId, $clientId) {
        $stmt = $this->db->prepare("
            SELECT r.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name, 
                   d.photo_url as doctor_photo, d.specialization as doctor_specialization
            FROM queue_rooms r
            LEFT JOIN queue_doctors d ON r.doctor_id = d.id
            WHERE r.id = ? AND r.client_id = ?
        ");
        $stmt->execute([$roomId, $clientId]);
        return $stmt->fetch();
    }
    
    // Dodaje nową salę
    public function create($clientId, $name, $description = '', $doctorId = null, $roomNumber = '') {
        $stmt = $this->db->prepare("
            INSERT INTO queue_rooms (client_id, name, description, doctor_id, room_number)
            VALUES (?, ?, ?, ?, ?)
        ");
        return $stmt->execute([$clientId, $name, $description, $doctorId, $roomNumber]);
    }
    
    // Aktualizuje salę
    public function update($roomId, $name, $description, $active, $doctorId = null, $roomNumber = '') {
        $stmt = $this->db->prepare("
            UPDATE queue_rooms
            SET name = ?, description = ?, active = ?, doctor_id = ?, room_number = ?
            WHERE id = ?
        ");
        return $stmt->execute([$name, $description, $active ? 1 : 0, $doctorId, $roomNumber, $roomId]);
    }
    
    // Usuwa salę
    public function delete($roomId) {
        $stmt = $this->db->prepare("DELETE FROM queue_rooms WHERE id = ?");
        return $stmt->execute([$roomId]);
    }
} 