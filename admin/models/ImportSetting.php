<?php

class ImportSetting {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    /**
     * Pobierz wszystkie ustawienia importu dla klienta
     */
    public function getByClientId($clientId) {
        $stmt = $this->db->prepare("
            SELECT * FROM import_settings 
            WHERE client_id = ? 
            ORDER BY system_name, created_at DESC
        ");
        $stmt->execute([$clientId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Pobierz ustawienie importu po ID
     */
    public function getById($id) {
        $stmt = $this->db->prepare("
            SELECT
                i.*,
                u.company_name as client_name
            FROM import_settings i
            LEFT JOIN users u ON i.client_id = u.id
            WHERE i.id = ?
        ");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Pobierz ustawienie importu po kodzie synchronizacji
     */
    public function getBySyncCode($syncCode) {
        $stmt = $this->db->prepare("
            SELECT * FROM import_settings 
            WHERE sync_code = ? AND is_active = 1
        ");
        $stmt->execute([$syncCode]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Utwórz nowe ustawienie importu
     */
    public function create($data) {
        $stmt = $this->db->prepare("
            INSERT INTO import_settings (
                client_id, system_name, sync_code, is_active, 
                api_endpoint, api_credentials, sync_frequency
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        return $stmt->execute([
            $data['client_id'],
            $data['system_name'],
            $data['sync_code'],
            $data['is_active'] ?? 1,
            $data['api_endpoint'] ?? null,
            $data['api_credentials'] ?? null,
            $data['sync_frequency'] ?? 3600
        ]);
    }

    /**
     * Zaktualizuj ustawienie importu
     */
    public function update($id, $data) {
        $stmt = $this->db->prepare("
            UPDATE import_settings SET
                system_name = ?,
                sync_code = ?,
                is_active = ?,
                api_endpoint = ?,
                api_credentials = ?,
                sync_frequency = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");

        return $stmt->execute([
            $data['system_name'],
            $data['sync_code'],
            $data['is_active'] ?? 1,
            $data['api_endpoint'] ?? null,
            $data['api_credentials'] ?? null,
            $data['sync_frequency'] ?? 3600,
            $id
        ]);
    }

    /**
     * Usuń ustawienie importu
     */
    public function delete($id) {
        $stmt = $this->db->prepare("DELETE FROM import_settings WHERE id = ?");
        return $stmt->execute([$id]);
    }

    /**
     * Generuj unikalny kod synchronizacji
     */
    public function generateSyncCode() {
        do {
            $code = 'igab' . strtoupper(substr(md5(uniqid()), 0, 12));
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM import_settings WHERE sync_code = ?");
            $stmt->execute([$code]);
        } while ($stmt->fetchColumn() > 0);

        return $code;
    }

    /**
     * Sprawdź czy kod synchronizacji jest unikalny
     */
    public function isSyncCodeUnique($syncCode, $excludeId = null) {
        $sql = "SELECT COUNT(*) FROM import_settings WHERE sync_code = ?";
        $params = [$syncCode];

        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchColumn() == 0;
    }

    /**
     * Pobierz wszystkie aktywne ustawienia importu
     */
    public function getActiveSettings() {
        $stmt = $this->db->prepare("
            SELECT * FROM import_settings 
            WHERE is_active = 1 
            ORDER BY client_id, system_name
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Pobierz ustawienia importu z informacjami o kliencie
     */
    public function getWithClientInfo() {
        $stmt = $this->db->prepare("
            SELECT
                i.*,
                u.company_name as client_name,
                u.username as client_username
            FROM import_settings i
            JOIN users u ON i.client_id = u.id
            ORDER BY u.company_name, i.system_name
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Pobierz ustawienie importu z informacjami o kliencie
     */
    public function getByIdWithClient($id) {
        $stmt = $this->db->prepare("
            SELECT
                i.*,
                u.company_name as client_name,
                u.username as client_username
            FROM import_settings i
            JOIN users u ON i.client_id = u.id
            WHERE i.id = ?
        ");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
