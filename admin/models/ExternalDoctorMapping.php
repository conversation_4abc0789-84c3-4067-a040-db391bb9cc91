<?php

class ExternalDoctorMapping {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    /**
     * Pobierz wszystkie mapowania dla ustawienia importu
     */
    public function getByImportSettingId($importSettingId) {
        $stmt = $this->db->prepare("
            SELECT 
                edm.*,
                qd.first_name,
                qd.last_name,
                qd.specialization as system_specialization,
                qd.photo_url
            FROM external_doctor_mappings edm
            LEFT JOIN queue_doctors qd ON edm.system_doctor_id = qd.id
            WHERE edm.import_setting_id = ?
            ORDER BY edm.external_doctor_name
        ");
        $stmt->execute([$importSettingId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Pobierz mapowanie po ID
     */
    public function getById($id) {
        $stmt = $this->db->prepare("
            SELECT * FROM external_doctor_mappings 
            WHERE id = ?
        ");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Utwórz nowe mapowanie
     */
    public function create($data) {
        $stmt = $this->db->prepare("
            INSERT INTO external_doctor_mappings (
                import_setting_id, external_doctor_id, external_doctor_name,
                external_doctor_specialization, system_doctor_id, is_mapped
            ) VALUES (?, ?, ?, ?, ?, ?)
        ");

        return $stmt->execute([
            $data['import_setting_id'],
            $data['external_doctor_id'],
            $data['external_doctor_name'],
            $data['external_doctor_specialization'] ?? null,
            $data['system_doctor_id'] ?? null,
            $data['is_mapped'] ?? 0
        ]);
    }

    /**
     * Zaktualizuj mapowanie
     */
    public function update($id, $data) {
        // Pobierz istniejące dane
        $existing = $this->getById($id);
        if (!$existing) {
            return false;
        }

        // Przygotuj dane do aktualizacji - zachowaj istniejące mapowanie jeśli nie podano nowego
        $updateData = [
            'external_doctor_name' => $data['external_doctor_name'] ?? $existing['external_doctor_name'],
            'external_doctor_specialization' => $data['external_doctor_specialization'] ?? $existing['external_doctor_specialization'],
            'system_doctor_id' => isset($data['system_doctor_id']) ? $data['system_doctor_id'] : $existing['system_doctor_id'],
            'is_mapped' => isset($data['is_mapped']) ? $data['is_mapped'] : $existing['is_mapped']
        ];

        // Zapisz czas w formacie UTC ISO 8601 dla spójności z exportDate
        $utcTime = gmdate('Y-m-d\TH:i:s\Z');

        $stmt = $this->db->prepare("
            UPDATE external_doctor_mappings SET
                external_doctor_name = ?,
                external_doctor_specialization = ?,
                system_doctor_id = ?,
                is_mapped = ?,
                last_seen = ?
            WHERE id = ?
        ");

        return $stmt->execute([
            $updateData['external_doctor_name'],
            $updateData['external_doctor_specialization'],
            $updateData['system_doctor_id'],
            $updateData['is_mapped'],
            $utcTime,
            $id
        ]);
    }

    /**
     * Usuń mapowanie
     */
    public function delete($id) {
        $stmt = $this->db->prepare("DELETE FROM external_doctor_mappings WHERE id = ?");
        return $stmt->execute([$id]);
    }

    /**
     * Znajdź mapowanie po ID lekarza zewnętrznego
     */
    public function findByExternalId($importSettingId, $externalDoctorId) {
        $stmt = $this->db->prepare("
            SELECT * FROM external_doctor_mappings 
            WHERE import_setting_id = ? AND external_doctor_id = ?
        ");
        $stmt->execute([$importSettingId, $externalDoctorId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Zaktualizuj lub utwórz mapowanie (upsert)
     */
    public function upsert($data) {
        $existing = $this->findByExternalId($data['import_setting_id'], $data['external_doctor_id']);

        if ($existing) {
            return $this->update($existing['id'], $data);
        } else {
            return $this->create($data);
        }
    }

    /**
     * Pobierz nieprzypisanych lekarzy zewnętrznych
     */
    public function getUnmapped($importSettingId) {
        $stmt = $this->db->prepare("
            SELECT * FROM external_doctor_mappings 
            WHERE import_setting_id = ? AND is_mapped = 0
            ORDER BY external_doctor_name
        ");
        $stmt->execute([$importSettingId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Pobierz przypisanych lekarzy zewnętrznych
     */
    public function getMapped($importSettingId) {
        $stmt = $this->db->prepare("
            SELECT 
                edm.*,
                qd.first_name,
                qd.last_name,
                qd.specialization as system_specialization
            FROM external_doctor_mappings edm
            JOIN queue_doctors qd ON edm.system_doctor_id = qd.id
            WHERE edm.import_setting_id = ? AND edm.is_mapped = 1
            ORDER BY edm.external_doctor_name
        ");
        $stmt->execute([$importSettingId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Pobierz lekarzy systemowych dostępnych do mapowania
     */
    public function getAvailableSystemDoctors($clientId) {
        $stmt = $this->db->prepare("
            SELECT id, first_name, last_name, specialization
            FROM queue_doctors
            WHERE client_id = ? AND active = 1
            ORDER BY first_name, last_name
        ");
        $stmt->execute([$clientId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Zaktualizuj czas ostatniego widzenia
     */
    public function updateLastSeen($id) {
        // Zapisz czas w formacie UTC ISO 8601 dla spójności z exportDate
        $utcTime = gmdate('Y-m-d\TH:i:s\Z');

        $stmt = $this->db->prepare("
            UPDATE external_doctor_mappings
            SET last_seen = ?
            WHERE id = ?
        ");
        return $stmt->execute([$utcTime, $id]);
    }
}
