<?php
echo "=== TEST KONWERSJI DATY LAST_SEEN ===\n\n";

// Symuluj różne formaty dat last_seen
$testDates = [
    '2025-08-26 14:50:31',           // Format lokalny (z bazy)
    '2025-08-26T14:50:31.168Z',     // Format UTC ISO 8601
    '2025-08-26T12:50:31Z',         // Format UTC bez milisekund
    '2025-08-26 12:50:31',          // Format lokalny (UTC w bazie)
];

foreach ($testDates as $lastSeen) {
    echo "Data last_seen: '$lastSeen'\n";
    
    // Symuluj logikę z widoku
    try {
        // Sprawdź czy data zawiera informację o strefie czasowej
        if (strpos($lastSeen, 'T') !== false && strpos($lastSeen, 'Z') !== false) {
            // Data w formacie ISO 8601 UTC - konwertuj na lokalny czas
            $utcDate = new DateTime($lastSeen);
            $utcDate->setTimezone(new DateTimeZone('Europe/Warsaw'));
            $result = $utcDate->format('d.m.Y H:i');
            echo "  -> Konwersja UTC: $result\n";
        } else {
            // Data prawdopodobnie już w lokalnym czasie
            $result = date('d.m.Y H:i', strtotime($lastSeen));
            echo "  -> Lokalny czas: $result\n";
        }
    } catch (Exception $e) {
        $result = date('d.m.Y H:i', strtotime($lastSeen));
        echo "  -> Błąd, fallback: $result\n";
    }
    
    echo "\n";
}

echo "=== TEST RZECZYWISTYCH DANYCH Z BAZY ===\n\n";

// Sprawdź rzeczywiste dane z bazy
try {
    $pdo = new PDO('sqlite:database/reklama.db');
    $stmt = $pdo->query("SELECT id, external_doctor_name, last_seen FROM external_doctor_mappings WHERE last_seen IS NOT NULL LIMIT 3");
    $mappings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($mappings as $mapping) {
        echo "Lekarz: {$mapping['external_doctor_name']}\n";
        echo "Last seen (raw): '{$mapping['last_seen']}'\n";
        
        // Symuluj logikę z widoku
        if ($mapping['last_seen']) {
            try {
                // Sprawdź czy data zawiera informację o strefie czasowej
                if (strpos($mapping['last_seen'], 'T') !== false && strpos($mapping['last_seen'], 'Z') !== false) {
                    // Data w formacie ISO 8601 UTC - konwertuj na lokalny czas
                    $utcDate = new DateTime($mapping['last_seen']);
                    $utcDate->setTimezone(new DateTimeZone('Europe/Warsaw'));
                    $result = $utcDate->format('d.m.Y H:i');
                    echo "Wyświetlana data (UTC->lokalny): $result\n";
                } else {
                    // Data prawdopodobnie już w lokalnym czasie
                    $result = date('d.m.Y H:i', strtotime($mapping['last_seen']));
                    echo "Wyświetlana data (lokalny): $result\n";
                }
            } catch (Exception $e) {
                $result = date('d.m.Y H:i', strtotime($mapping['last_seen']));
                echo "Wyświetlana data (fallback): $result\n";
            }
        } else {
            echo "Wyświetlana data: -\n";
        }
        
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "Błąd połączenia z bazą: " . $e->getMessage() . "\n";
}

echo "=== PODSUMOWANIE ===\n";
echo "Logika konwersji:\n";
echo "1. Jeśli data zawiera 'T' i 'Z' -> konwertuj z UTC na lokalny czas\n";
echo "2. W przeciwnym razie -> traktuj jako lokalny czas\n";
echo "3. W przypadku błędu -> fallback do standardowego formatowania\n";
?>
