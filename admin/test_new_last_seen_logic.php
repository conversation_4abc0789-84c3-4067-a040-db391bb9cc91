<?php
echo "=== TEST NOWEJ LOGIKI ZAPISYWANIA LAST_SEEN ===\n\n";

// Test funkcji gmdate
$utcTime = gmdate('Y-m-d\TH:i:s\Z');
echo "Nowy format UTC: $utcTime\n";

// Test konwersji tego formatu
try {
    $utcDate = new DateTime($utcTime);
    $utcDate->setTimezone(new DateTimeZone('Europe/Warsaw'));
    $localTime = $utcDate->format('d.m.Y H:i:s');
    echo "Konwersja na lokalny: $localTime\n";
    echo "Różnica: " . $utcDate->format('P') . "\n";
} catch (Exception $e) {
    echo "Błąd konwersji: " . $e->getMessage() . "\n";
}

echo "\n=== SYMULACJA LOGIKI Z WIDOKU ===\n\n";

// Symuluj nowy format z bazy
$newLastSeen = $utcTime;
echo "Data z bazy (nowy format): '$newLastSeen'\n";

// Sprawdź czy data zawiera informację o strefie czasowej
if (strpos($newLastSeen, 'T') !== false && strpos($newLastSeen, 'Z') !== false) {
    echo "Wykryto format UTC ISO 8601\n";
    // Data w formacie ISO 8601 UTC - konwertuj na lokalny czas
    $utcDate = new DateTime($newLastSeen);
    $utcDate->setTimezone(new DateTimeZone('Europe/Warsaw'));
    $result = $utcDate->format('d.m.Y H:i');
    echo "Wyświetlana data: $result\n";
} else {
    echo "Format lokalny\n";
    // Data prawdopodobnie już w lokalnym czasie
    $result = date('d.m.Y H:i', strtotime($newLastSeen));
    echo "Wyświetlana data: $result\n";
}

echo "\n=== PORÓWNANIE FORMATÓW ===\n";
echo "Stary format (CURRENT_TIMESTAMP): 2025-08-26 14:50:31\n";
echo "Nowy format (UTC ISO 8601):       $utcTime\n";
echo "\nKorzyści nowego formatu:\n";
echo "1. Spójność z exportDate z JSON\n";
echo "2. Jednoznaczna strefa czasowa (UTC)\n";
echo "3. Automatyczna konwersja w widoku\n";
echo "4. Zgodność z międzynarodowymi standardami\n";

echo "\n=== TEST AKTUALIZACJI BAZY ===\n";

// Symuluj aktualizację jednego rekordu
try {
    $pdo = new PDO('sqlite:database/reklama.db');
    
    // Zaktualizuj jeden rekord dla testu
    $stmt = $pdo->prepare("UPDATE external_doctor_mappings SET last_seen = ? WHERE id = 1");
    $stmt->execute([$utcTime]);
    
    echo "Zaktualizowano rekord ID=1 z nowym formatem UTC\n";
    
    // Sprawdź wynik
    $stmt = $pdo->prepare("SELECT id, external_doctor_name, last_seen FROM external_doctor_mappings WHERE id = 1");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "Lekarz: {$result['external_doctor_name']}\n";
        echo "Last seen: {$result['last_seen']}\n";
        
        // Test konwersji
        if (strpos($result['last_seen'], 'T') !== false && strpos($result['last_seen'], 'Z') !== false) {
            $utcDate = new DateTime($result['last_seen']);
            $utcDate->setTimezone(new DateTimeZone('Europe/Warsaw'));
            $displayTime = $utcDate->format('d.m.Y H:i');
            echo "Wyświetlana data: $displayTime\n";
        }
    }
    
} catch (Exception $e) {
    echo "Błąd bazy danych: " . $e->getMessage() . "\n";
}
?>
