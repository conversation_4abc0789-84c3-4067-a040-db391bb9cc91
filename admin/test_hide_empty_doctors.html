<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Ukrywanie Le<PERSON>zy Bez Wizyt</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .doctor-card {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        .doctor-card[style*="display: none"] {
            opacity: 0;
            transform: scale(0.95);
        }

        .form-check-input:checked {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }

        .form-check-label {
            font-weight: 500;
            color: #495057;
        }

        #emptyDoctorsMessage {
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>Test - Ukrywanie Lekarzy Bez Wizyt</h2>
        
        <!-- Przycisk ukrywania lekarzy bez wizyt -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">Lekarze na dzień 26.08.2025</h5>
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="hideEmptyDoctors" onchange="toggleEmptyDoctors()">
                <label class="form-check-label" for="hideEmptyDoctors">
                    Ukryj lekarzy bez wizyt
                </label>
            </div>
        </div>
        
        <div class="row" id="doctorsContainer">
            <!-- Lekarz z wizytami -->
            <div class="col-md-6 col-lg-4 mb-4 doctor-card" data-doctor-id="1" data-has-appointments="true">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">Dr Jan Kowalski</h6>
                        <small>Kardiolog</small>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info py-2 mb-3">
                            <div class="fw-bold">14:30</div>
                            <small>Anna Nowak</small>
                        </div>
                        <h6 class="text-muted mb-2">Oczekujące (3)</h6>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between py-1 border-bottom">
                                <span class="fw-bold">15:00</span>
                                <span class="text-muted">Piotr Wiśniewski</span>
                            </div>
                            <div class="d-flex justify-content-between py-1 border-bottom">
                                <span class="fw-bold">15:30</span>
                                <span class="text-muted">Maria Kowalczyk</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Lekarz bez wizyt -->
            <div class="col-md-6 col-lg-4 mb-4 doctor-card doctor-empty" data-doctor-id="2" data-has-appointments="false">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">Dr Anna Nowak</h6>
                        <small>Dermatolog</small>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-light py-2 mb-3 text-center text-muted">
                            <i class="fas fa-clock"></i> Brak aktualnej wizyty
                        </div>
                        <h6 class="text-muted mb-2">Oczekujące (0)</h6>
                        <div class="text-muted text-center py-2 mb-3">
                            <small>Brak oczekujących wizyt</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Kolejny lekarz z wizytami -->
            <div class="col-md-6 col-lg-4 mb-4 doctor-card" data-doctor-id="3" data-has-appointments="true">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">Dr Piotr Zieliński</h6>
                        <small>Neurolog</small>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-light py-2 mb-3 text-center text-muted">
                            <i class="fas fa-clock"></i> Brak aktualnej wizyty
                        </div>
                        <h6 class="text-muted mb-2">Oczekujące (1)</h6>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between py-1 border-bottom">
                                <span class="fw-bold">16:00</span>
                                <span class="text-muted">Katarzyna Lewandowska</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Kolejny lekarz bez wizyt -->
            <div class="col-md-6 col-lg-4 mb-4 doctor-card doctor-empty" data-doctor-id="4" data-has-appointments="false">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">Dr Michał Wójcik</h6>
                        <small>Ortopeda</small>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-light py-2 mb-3 text-center text-muted">
                            <i class="fas fa-clock"></i> Brak aktualnej wizyty
                        </div>
                        <h6 class="text-muted mb-2">Oczekujące (0)</h6>
                        <div class="text-muted text-center py-2 mb-3">
                            <small>Brak oczekujących wizyt</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Funkcja ukrywania/pokazywania lekarzy bez wizyt
        function toggleEmptyDoctors() {
            const checkbox = document.getElementById('hideEmptyDoctors');
            const doctorCards = document.querySelectorAll('.doctor-card');
            
            doctorCards.forEach(card => {
                const hasAppointments = card.getAttribute('data-has-appointments') === 'true';
                
                if (checkbox.checked && !hasAppointments) {
                    // Ukryj lekarzy bez wizyt
                    card.style.display = 'none';
                } else {
                    // Pokaż wszystkich lekarzy
                    card.style.display = 'block';
                }
            });
            
            // Zapisz preferencję w localStorage
            localStorage.setItem('hideEmptyDoctors', checkbox.checked);
            
            // Pokaż komunikat o liczbie ukrytych lekarzy
            updateEmptyDoctorsMessage();
        }

        // Funkcja aktualizująca komunikat o ukrytych lekarzach
        function updateEmptyDoctorsMessage() {
            const checkbox = document.getElementById('hideEmptyDoctors');
            const doctorCards = document.querySelectorAll('.doctor-card');
            const emptyDoctors = document.querySelectorAll('.doctor-card[data-has-appointments="false"]');
            
            // Usuń poprzedni komunikat
            const existingMessage = document.getElementById('emptyDoctorsMessage');
            if (existingMessage) {
                existingMessage.remove();
            }
            
            if (checkbox.checked && emptyDoctors.length > 0) {
                const message = document.createElement('div');
                message.id = 'emptyDoctorsMessage';
                message.className = 'alert alert-info mt-2 mb-3';
                message.innerHTML = `
                    <i class="fas fa-info-circle"></i> 
                    Ukryto ${emptyDoctors.length} lekarzy bez wizyt na dzień 26.08.2025
                `;
                
                const doctorsContainer = document.getElementById('doctorsContainer');
                doctorsContainer.parentNode.insertBefore(message, doctorsContainer);
            }
        }

        // Przywróć preferencję z localStorage przy ładowaniu strony
        document.addEventListener('DOMContentLoaded', function() {
            const hideEmptyDoctors = localStorage.getItem('hideEmptyDoctors') === 'true';
            const checkbox = document.getElementById('hideEmptyDoctors');
            
            if (checkbox && hideEmptyDoctors) {
                checkbox.checked = true;
                toggleEmptyDoctors();
            }
        });
    </script>
</body>
</html>
