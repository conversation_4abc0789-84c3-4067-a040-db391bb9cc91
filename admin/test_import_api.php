<?php
// Test skrypt dla API importu
require_once 'core/Database.php';
require_once 'models/ImportSetting.php';
require_once 'models/ExternalDoctorMapping.php';

// Inicjalizacja bazy danych
Database::init();

echo "<h1>Test API Importu</h1>";

// Test 1: Sprawdź czy ustawienia importu istnieją
echo "<h2>Test 1: Ustawienia importu</h2>";
$importModel = new ImportSetting();
$settings = $importModel->getActiveSettings();

if (empty($settings)) {
    echo "<p style='color: red;'>❌ Brak aktywnych ustawień importu!</p>";
    echo "<p>Utwórz ustawienie importu w panelu administratora lub klienta.</p>";
} else {
    echo "<p style='color: green;'>✅ Znaleziono " . count($settings) . " aktywnych ustawień importu:</p>";
    foreach ($settings as $setting) {
        echo "<ul>";
        echo "<li>ID: {$setting['id']}</li>";
        echo "<li>System: {$setting['system_name']}</li>";
        echo "<li>Kod: {$setting['sync_code']}</li>";
        echo "<li>Klient ID: {$setting['client_id']}</li>";
        echo "</ul><hr>";
    }
}

// Test 2: Sprawdź strukturę tabeli queue_appointments
echo "<h2>Test 2: Struktura tabeli queue_appointments</h2>";
try {
    $db = Database::getInstance()->getConnection();
    $stmt = $db->prepare("PRAGMA table_info(queue_appointments)");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $requiredColumns = ['external_id', 'service_name', 'end_time', 'office', 'type'];
    $existingColumns = array_column($columns, 'name');
    
    echo "<p>Istniejące kolumny:</p><ul>";
    foreach ($columns as $column) {
        echo "<li>{$column['name']} ({$column['type']})</li>";
    }
    echo "</ul>";
    
    $missingColumns = array_diff($requiredColumns, $existingColumns);
    if (empty($missingColumns)) {
        echo "<p style='color: green;'>✅ Wszystkie wymagane kolumny istnieją</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Brakujące kolumny (zostaną dodane automatycznie): " . implode(', ', $missingColumns) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Błąd sprawdzania struktury tabeli: " . $e->getMessage() . "</p>";
}

// Test 3: Test przykładowych danych JSON
echo "<h2>Test 3: Przykładowe dane JSON</h2>";
$testData = [
    'sync_code' => $settings[0]['sync_code'] ?? 'test_code',
    'data' => [
        'date' => 'Środa, 06 sierpnia',
        'doctors' => [
            [
                'id' => 'test_doctor_1',
                'name' => 'dr Test Testowy',
                'appointments' => [
                    [
                        'id' => 'test_appointment_1',
                        'patient' => 'Jan Kowalski',
                        'service' => 'Konsultacja',
                        'time' => '09:00 - 09:30',
                        'office' => 'Gabinet 1',
                        'type' => 'wizyta'
                    ]
                ]
            ]
        ]
    ]
];

echo "<p>Przykładowe dane JSON:</p>";
echo "<pre>" . json_encode($testData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

// Test 4: Sprawdź endpoint API
echo "<h2>Test 4: Endpoint API</h2>";
$apiUrl = 'http://localhost:8080/admin/api/import';
echo "<p>URL endpointu: <code>$apiUrl</code></p>";

// Test 5: Instrukcje testowania
echo "<h2>Test 5: Instrukcje testowania</h2>";
echo "<ol>";
echo "<li>Upewnij się, że masz aktywne ustawienie importu (powyżej)</li>";
echo "<li>Zainstaluj rozszerzenie Chrome z katalogu <code>chrome1/</code></li>";
echo "<li>Przejdź na stronę harmonogramu w iGabinet.pl</li>";
echo "<li>Otwórz rozszerzenie Chrome</li>";
echo "<li>Wprowadź kod synchronizacji: <strong>" . ($settings[0]['sync_code'] ?? 'brak_kodu') . "</strong></li>";
echo "<li>Kliknij 'Importuj do systemu'</li>";
echo "</ol>";

// Test 6: Sprawdź mapowania lekarzy
if (!empty($settings)) {
    echo "<h2>Test 6: Mapowania lekarzy</h2>";
    $mappingModel = new ExternalDoctorMapping();
    
    foreach ($settings as $setting) {
        echo "<h3>Ustawienie: {$setting['system_name']} (ID: {$setting['id']})</h3>";
        
        $mappings = $mappingModel->getByImportSettingId($setting['id']);
        if (empty($mappings)) {
            echo "<p style='color: orange;'>⚠️ Brak mapowań lekarzy dla tego ustawienia</p>";
        } else {
            echo "<p style='color: green;'>✅ Znaleziono " . count($mappings) . " mapowań:</p>";
            echo "<ul>";
            foreach ($mappings as $mapping) {
                $status = $mapping['is_mapped'] ? '✅ Zmapowany' : '❌ Nie zmapowany';
                echo "<li>{$mapping['external_doctor_name']} → {$mapping['first_name']} {$mapping['last_name']} ($status)</li>";
            }
            echo "</ul>";
        }
    }
}

echo "<h2>Podsumowanie</h2>";
echo "<p>Jeśli wszystkie testy przeszły pomyślnie, system importu jest gotowy do użycia!</p>";
echo "<p><strong>Następne kroki:</strong></p>";
echo "<ol>";
echo "<li>Przetestuj import z rozszerzenia Chrome</li>";
echo "<li>Sprawdź czy dane zostały zaimportowane do tabeli queue_appointments</li>";
echo "<li>Skonfiguruj mapowania lekarzy w panelu administracyjnym</li>";
echo "</ol>";
?>
