<?php
require_once 'core/Database.php';

class DoctorPhotoDownloader {
    private $db;
    private $sonokardClientId = 2;
    private $uploadDir = '../uploads/doctors/';
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
        
        // Upewnij się, że katalog istnieje
        if (!is_dir($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }
    }
    
    /**
     * Pobiera listę lekarzy bez zdjęć
     */
    public function getDoctorsWithoutPhotos() {
        $stmt = $this->db->prepare("
            SELECT id, first_name, last_name, photo_url 
            FROM queue_doctors 
            WHERE client_id = ? AND active = 1 AND (photo_url IS NULL OR photo_url = '')
        ");
        $stmt->execute([$this->sonokardClientId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Pobiera zdjęcie lekarza ze strony sonokard.pl
     */
    public function downloadDoctorPhoto($firstName, $lastName) {
        // Usuń tytuły z imienia dla wyszukiwania
        $cleanFirstName = preg_replace('/^(dr n\. med\.|lek\.|mgr|dr n\. k\. f\.|dr\. n\. med\.|dr)\s+/i', '', $firstName);
        
        // Spróbuj różnych kombinacji nazw plików
        $possibleNames = [
            strtolower($cleanFirstName . '-' . $lastName),
            strtolower($cleanFirstName . '_' . $lastName),
            strtolower($cleanFirstName . $lastName),
            strtolower($lastName),
            strtolower($cleanFirstName)
        ];
        
        // Usuń polskie znaki
        $possibleNames = array_map(function($name) {
            $name = str_replace(['ą', 'ć', 'ę', 'ł', 'ń', 'ó', 'ś', 'ź', 'ż'], 
                              ['a', 'c', 'e', 'l', 'n', 'o', 's', 'z', 'z'], $name);
            $name = str_replace([' ', '.', '-'], ['', '', ''], $name);
            return $name;
        }, $possibleNames);
        
        // Spróbuj pobrać zdjęcie z różnych możliwych URL
        $baseUrls = [
            'https://sonokard.pl/uploads/pics/',
            'https://sonokard.pl/fileadmin/user_upload/',
            'https://sonokard.pl/fileadmin/user_upload/zespol/',
            'https://sonokard.pl/uploads/doctors/',
            'https://sonokard.pl/images/doctors/',
            'https://sonokard.pl/assets/images/doctors/'
        ];
        
        $extensions = ['jpg', 'jpeg', 'png', 'webp'];
        
        foreach ($baseUrls as $baseUrl) {
            foreach ($possibleNames as $name) {
                foreach ($extensions as $ext) {
                    $imageUrl = $baseUrl . $name . '.' . $ext;
                    
                    echo "Próbuję: $imageUrl\n";
                    
                    $imageData = $this->downloadImage($imageUrl);
                    if ($imageData !== false) {
                        echo "Znaleziono zdjęcie: $imageUrl\n";
                        return $this->saveImage($imageData, $name . '.' . $ext);
                    }
                }
            }
        }
        
        return false;
    }
    
    /**
     * Pobiera obraz z URL
     */
    private function downloadImage($url) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HEADER, false);
        
        $data = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        curl_close($ch);
        
        // Sprawdź czy to rzeczywiście obraz
        if ($httpCode === 200 && strpos($contentType, 'image/') === 0 && strlen($data) > 1000) {
            return $data;
        }
        
        return false;
    }
    
    /**
     * Zapisuje obraz do katalogu uploads
     */
    private function saveImage($imageData, $originalName) {
        // Generuj unikalną nazwę pliku
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $filename = uniqid() . '.' . $extension;
        $filepath = $this->uploadDir . $filename;
        
        if (file_put_contents($filepath, $imageData)) {
            return '/uploads/doctors/' . $filename;
        }
        
        return false;
    }
    
    /**
     * Aktualizuje URL zdjęcia w bazie danych
     */
    private function updateDoctorPhoto($doctorId, $photoUrl) {
        $stmt = $this->db->prepare("
            UPDATE queue_doctors 
            SET photo_url = ? 
            WHERE id = ?
        ");
        return $stmt->execute([$photoUrl, $doctorId]);
    }
    
    /**
     * Główna metoda pobierania zdjęć
     */
    public function downloadMissingPhotos() {
        echo "Rozpoczynam pobieranie brakujących zdjęć lekarzy...\n";
        
        $doctors = $this->getDoctorsWithoutPhotos();
        echo "Znaleziono " . count($doctors) . " lekarzy bez zdjęć\n";
        
        $downloaded = 0;
        $failed = 0;
        
        foreach ($doctors as $doctor) {
            echo "\nPobieranie zdjęcia dla: {$doctor['first_name']} {$doctor['last_name']}\n";
            
            $photoUrl = $this->downloadDoctorPhoto($doctor['first_name'], $doctor['last_name']);
            
            if ($photoUrl) {
                if ($this->updateDoctorPhoto($doctor['id'], $photoUrl)) {
                    echo "✓ Pobrano i zapisano zdjęcie: $photoUrl\n";
                    $downloaded++;
                } else {
                    echo "✗ Błąd podczas aktualizacji bazy danych\n";
                    $failed++;
                }
            } else {
                echo "✗ Nie znaleziono zdjęcia\n";
                $failed++;
            }
            
            // Krótka pauza między pobieraniami
            sleep(1);
        }
        
        echo "\nPodsumowanie:\n";
        echo "- Pobrano zdjęć: $downloaded\n";
        echo "- Nie znaleziono: $failed\n";
    }
    
    /**
     * Pobiera zdjęcie z konkretnego URL (dla testów)
     */
    public function downloadSpecificPhoto($url, $doctorId) {
        echo "Pobieranie zdjęcia z: $url\n";
        
        $imageData = $this->downloadImage($url);
        if ($imageData !== false) {
            $photoUrl = $this->saveImage($imageData, basename($url));
            if ($photoUrl && $this->updateDoctorPhoto($doctorId, $photoUrl)) {
                echo "✓ Pobrano i zapisano zdjęcie: $photoUrl\n";
                return true;
            }
        }
        
        echo "✗ Nie udało się pobrać zdjęcia\n";
        return false;
    }
}

// Uruchom pobieranie jeśli skrypt jest wywołany bezpośrednio
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $downloader = new DoctorPhotoDownloader();
    $downloader->downloadMissingPhotos();
}
?>
