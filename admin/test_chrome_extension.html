<!DOCTYPE html>
<html lang="pl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Rozszerzenia Chrome</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }

        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .success {
            background: #d4edda;
            border-color: #c3e6cb;
        }

        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
        }

        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
        }

        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #0056b3;
        }

        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 10px 0;
        }

        #result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🧪 Test Rozszerzenia Chrome - Import API</h1>

        <div class="test-section info">
            <h3>📋 Instrukcje testowania</h3>
            <ol>
                <li>Zainstaluj rozszerzenie Chrome z katalogu <code>chrome1/</code></li>
                <li>Wprowadź kod synchronizacji: <strong>igab000000000001</strong></li>
                <li>Kliknij przycisk poniżej, aby przetestować API bezpośrednio z przeglądarki</li>
                <li>Sprawdź czy dane zostały zaimportowane</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🚀 Test API Importu</h3>
            <p>Ten test symuluje wysłanie danych z rozszerzenia Chrome do API importu.</p>

            <div class="code">
                <strong>Endpoint:</strong> POST /admin/api/import<br>
                <strong>Kod synchronizacji:</strong> igab000000000001<br>
                <strong>Dane testowe:</strong> 2 lekarzy, 3 wizyty z danymi pacjentek (PESEL, telefon, email)
            </div>

            <button onclick="testImportAPI()">🧪 Przetestuj Import API</button>
            <button onclick="testStatusAPI()">📊 Sprawdź Status</button>
            <button onclick="testAutoMapping()">🤖 Test Auto-mapowania</button>
        </div>

        <div id="result"></div>

        <div class="test-section">
            <h3>📊 Sprawdzenie wyników</h3>
            <p>Po udanym imporcie sprawdź:</p>
            <ul>
                <li><a href="/admin/client/import" target="_blank">Panel ustawień importu</a></li>
                <li><a href="/admin/test_import_api.php" target="_blank">Test konfiguracji systemu</a></li>
                <li><a href="/admin/client/queue" target="_blank">System kolejkowy (wizyty)</a></li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔧 Rozwiązywanie problemów</h3>
            <details>
                <summary>Kliknij, aby zobaczyć typowe problemy</summary>
                <ul>
                    <li><strong>Timeout:</strong> Zwiększ timeout w rozszerzeniu Chrome</li>
                    <li><strong>CORS:</strong> Sprawdź czy serwer akceptuje żądania z rozszerzenia</li>
                    <li><strong>Brak mapowań:</strong> Zmapuj lekarzy w panelu administracyjnym</li>
                    <li><strong>Błąd 401:</strong> Sprawdź kod synchronizacji</li>
                </ul>
            </details>
        </div>
    </div>

    <script>
        function showResult(message, type = 'info') {
            const result = document.getElementById('result');
            result.className = `test-section ${type}`;
            result.innerHTML = message;
            result.style.display = 'block';
        }

        async function testImportAPI() {
            showResult('🔄 Wysyłanie danych do API...', 'info');

            const testData = {
                exportDate: new Date().toISOString(),
                syncCode: 'igab000000000001',
                syncData: {
                    days: [
                        {
                            date: new Date().toISOString().split('T')[0], // Format YYYY-MM-DD
                            doctors: [
                                {
                                    doctorId: 'test_doctor_chrome_' + Date.now(),
                                    doctorName: 'dr n. med. Małgorzata Olesiak-Andryszczak',
                                    appointments: [
                                        {
                                            appointmentId: 'test_appointment_chrome_' + Date.now() + '_1',
                                            patientFirstName: 'Anna',
                                            patientLastName: 'Testowa',
                                            appointmentStart: '14:00',
                                            appointmentEnd: '14:30',
                                            appointmentDuration: 30
                                        }
                                    ]
                                },
                                {
                                    doctorId: 'test_doctor_chrome2_' + Date.now(),
                                    doctorName: 'lek. Beata Dawiec',
                                    appointments: [
                                        {
                                            appointmentId: 'test_appointment_chrome_' + Date.now() + '_2',
                                            patientFirstName: 'Maria',
                                            patientLastName: 'Testowa',
                                            appointmentStart: '14:30',
                                            appointmentEnd: '15:00',
                                            appointmentDuration: 30
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            };

            try {
                const response = await fetch('/admin/api/import', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                const result = await response.json();

                if (result.success) {
                    const stats = result.stats;
                    showResult(`
                        <h4>✅ Import zakończony pomyślnie!</h4>
                        <ul>
                            <li>Przetworzonych lekarzy: ${stats.doctors_processed}</li>
                            <li>Zmapowanych lekarzy: ${stats.doctors_mapped}</li>
                            <li>Przetworzonych wizyt: ${stats.appointments_processed}</li>
                            <li>Utworzonych wizyt: ${stats.appointments_created}</li>
                            <li>Zaktualizowanych wizyt: ${stats.appointments_updated}</li>
                            ${stats.errors.length > 0 ? '<li>Błędy: ' + stats.errors.join(', ') + '</li>' : ''}
                        </ul>
                        <p><strong>Następny krok:</strong> Sprawdź <a href="/admin/client/import/doctor-mappings/1" target="_blank">mapowania lekarzy</a></p>
                    `, 'success');
                } else {
                    showResult(`<h4>❌ Import nie powiódł się</h4><p>Błąd: ${result.error}</p>`, 'error');
                }
            } catch (error) {
                showResult(`<h4>❌ Błąd połączenia</h4><p>${error.message}</p>`, 'error');
            }
        }

        async function testStatusAPI() {
            showResult('🔄 Sprawdzanie statusu...', 'info');

            try {
                const response = await fetch('/admin/api/import/status/igab000000000001');
                const result = await response.json();

                if (result.success) {
                    showResult(`
                        <h4>📊 Status importu</h4>
                        <ul>
                            <li>System: ${result.import_setting.system_name}</li>
                            <li>Aktywny: ${result.import_setting.is_active ? 'Tak' : 'Nie'}</li>
                            <li>Ostatnia synchronizacja: ${result.import_setting.last_sync || 'Brak'}</li>
                            <li>Zmapowanych lekarzy: ${result.doctor_mappings.mapped}</li>
                            <li>Niezmapowanych lekarzy: ${result.doctor_mappings.unmapped}</li>
                            <li>Ostatnie synchronizacje: ${result.recent_syncs.length}</li>
                        </ul>
                    `, 'success');
                } else {
                    showResult(`<h4>❌ Błąd statusu</h4><p>${result.error}</p>`, 'error');
                }
            } catch (error) {
                showResult(`<h4>❌ Błąd połączenia</h4><p>${error.message}</p>`, 'error');
            }
        }

        async function testAutoMapping() {
            showResult('🔄 Testowanie automatycznego mapowania...', 'info');

            try {
                const response = await fetch('/admin/api/import/auto-map/igab000000000001', {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    showResult(`
                        <h4>🤖 Automatyczne mapowanie zakończone</h4>
                        <ul>
                            <li>Automatycznie zmapowanych: ${result.mapped_count}</li>
                            <li>Sugestii do ręcznego mapowania: ${result.suggestions.length}</li>
                        </ul>
                        ${result.suggestions.length > 0 ?
                            '<p><strong>Sugestie:</strong></p><ul>' +
                            result.suggestions.map(s =>
                                `<li>${s.external_doctor.external_doctor_name} → ${s.suggested_doctor.first_name} ${s.suggested_doctor.last_name} (${Math.round(s.confidence * 100)}%)</li>`
                            ).join('') +
                            '</ul>' : ''
                        }
                    `, 'success');
                } else {
                    showResult(`<h4>❌ Błąd auto-mapowania</h4><p>${result.error}</p>`, 'error');
                }
            } catch (error) {
                showResult(`<h4>❌ Błąd połączenia</h4><p>${error.message}</p>`, 'error');
            }
        }
    </script>
</body>

</html>