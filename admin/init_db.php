<?php

// Definicja schematu bazy danych
$database_schema = <<<SQL
-- <PERSON><PERSON><PERSON>ik<PERSON>
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role TEXT NOT NULL,
    company_name VARCHAR(100),
    balance DECIMAL(10,2) DEFAULT 0,
    rate_per_second DECIMAL(5,4) DEFAULT 0.0001,
    is_active INTEGER DEFAULT 1,
    last_activity DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON> kate<PERSON><PERSON> kampanii
CREATE TABLE IF NOT EXISTS categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON>bela kampanii reklamowych
CREATE TABLE IF NOT EXISTS campaigns (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    advertiser_id INTEGER NOT NULL,
    category_id INTEGER,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    media_type TEXT NOT NULL,
    media_url VARCHAR(255) NOT NULL,
    youtube_id VARCHAR(20),
    duration INTEGER DEFAULT 30,
    budget DECIMAL(10,2) NOT NULL,
    spent DECIMAL(10,2) DEFAULT 0,
    rate_per_second DECIMAL(5,4) DEFAULT 0.0001,
    max_frequency_per_hour INTEGER DEFAULT 0,
    start_date DATETIME,
    end_date DATETIME,
    is_active INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (advertiser_id) REFERENCES users(id),
    FOREIGN KEY (category_id) REFERENCES categories(id)
);

-- Tabela wyświetleń reklam
CREATE TABLE IF NOT EXISTS ad_views (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    campaign_id INTEGER NOT NULL,
    client_id INTEGER NOT NULL,
    duration_seconds INTEGER NOT NULL,
    cost DECIMAL(10,4) NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (campaign_id) REFERENCES campaigns(id),
    FOREIGN KEY (client_id) REFERENCES users(id)
);

-- Tabela monitorów/TV klientów
CREATE TABLE IF NOT EXISTS client_displays (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    display_code VARCHAR(6) UNIQUE,
    is_online INTEGER DEFAULT 0,
    last_heartbeat DATETIME,
    queue_system_enabled INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES users(id)
);

-- Tabela przypisań kampanii do klientów
CREATE TABLE IF NOT EXISTS campaign_assignments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    campaign_id INTEGER NOT NULL,
    client_id INTEGER NOT NULL,
    is_accepted INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (campaign_id) REFERENCES campaigns(id),
    FOREIGN KEY (client_id) REFERENCES users(id)
);

-- Tabela automatycznej akceptacji kategorii przez klientów
CREATE TABLE IF NOT EXISTS campaign_auto_accept (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    category_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES users(id),
    FOREIGN KEY (category_id) REFERENCES categories(id),
    UNIQUE(client_id, category_id)
);

-- Tabela konfiguracji systemu kolejkowego dla klientów
CREATE TABLE IF NOT EXISTS queue_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    is_enabled INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES users(id)
);

-- Tabela lekarzy w systemie kolejkowym
CREATE TABLE IF NOT EXISTS queue_doctors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    photo_url VARCHAR(255),
    specialization VARCHAR(200),
    access_code VARCHAR(12) UNIQUE,
    active INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    default_room_id INTEGER,
    FOREIGN KEY (client_id) REFERENCES users(id)
);

-- Tabela sal w systemie kolejkowym (rozszerzona o lekarza)
CREATE TABLE IF NOT EXISTS queue_rooms (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    doctor_id INTEGER,
    room_number VARCHAR(20),
    active INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES users(id),
    FOREIGN KEY (doctor_id) REFERENCES queue_doctors(id)
);

-- Tabela wizyt w systemie kolejkowym (zamiast numerów)
CREATE TABLE IF NOT EXISTS queue_appointments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    room_id INTEGER NOT NULL,
    doctor_id INTEGER,
    appointment_time TIME NOT NULL,
    appointment_date DATE,
    patient_name VARCHAR(200),
    status TEXT DEFAULT 'waiting', -- 'waiting', 'current', 'completed', 'cancelled'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,
    called_at DATETIME,
    completed_at DATETIME,
    FOREIGN KEY (client_id) REFERENCES users(id),
    FOREIGN KEY (room_id) REFERENCES queue_rooms(id),
    FOREIGN KEY (doctor_id) REFERENCES queue_doctors(id)
);

-- Tabela numerów kolejkowych (zachowana dla kompatybilności wstecznej)
CREATE TABLE IF NOT EXISTS queue_numbers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    room_id INTEGER NOT NULL,
    number INTEGER NOT NULL,
    status TEXT DEFAULT 'waiting', -- 'waiting', 'current', 'completed', 'cancelled'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    called_at DATETIME,
    completed_at DATETIME,
    FOREIGN KEY (client_id) REFERENCES users(id),
    FOREIGN KEY (room_id) REFERENCES queue_rooms(id)
);

-- Tabela mapowań lekarzy z plików CSV
CREATE TABLE IF NOT EXISTS csv_doctor_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    csv_doctor_name VARCHAR(200) NOT NULL,
    system_doctor_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES users(id),
    FOREIGN KEY (system_doctor_id) REFERENCES queue_doctors(id)
);

-- Tabela ustawień importu dla różnych systemów
CREATE TABLE IF NOT EXISTS import_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER NOT NULL,
    system_name VARCHAR(100) NOT NULL, -- np. 'igabinet', 'medinet', etc.
    sync_code VARCHAR(16) UNIQUE NOT NULL, -- 16-znakowy kod synchronizacji
    is_active INTEGER DEFAULT 1,
    api_endpoint VARCHAR(255),
    api_credentials TEXT, -- JSON z danymi logowania
    last_sync DATETIME,
    sync_frequency INTEGER DEFAULT 3600, -- w sekundach
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES users(id),
    UNIQUE(client_id, system_name)
);

-- Tabela mapowań lekarzy z systemów zewnętrznych
CREATE TABLE IF NOT EXISTS external_doctor_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    import_setting_id INTEGER NOT NULL,
    external_doctor_id VARCHAR(100) NOT NULL,
    external_doctor_name VARCHAR(200) NOT NULL,
    external_doctor_specialization VARCHAR(200),
    system_doctor_id INTEGER,
    is_mapped INTEGER DEFAULT 0,
    last_seen DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (import_setting_id) REFERENCES import_settings(id),
    FOREIGN KEY (system_doctor_id) REFERENCES queue_doctors(id),
    UNIQUE(import_setting_id, external_doctor_id)
);

-- Tabela logów synchronizacji
CREATE TABLE IF NOT EXISTS sync_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    import_setting_id INTEGER NOT NULL,
    sync_type VARCHAR(50) NOT NULL, -- 'full', 'incremental', 'manual'
    status VARCHAR(20) NOT NULL, -- 'success', 'error', 'partial'
    records_processed INTEGER DEFAULT 0,
    records_updated INTEGER DEFAULT 0,
    records_created INTEGER DEFAULT 0,
    error_message TEXT,
    started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    FOREIGN KEY (import_setting_id) REFERENCES import_settings(id)
);
SQL;

// Hash hasła "password"
$password_hash = password_hash('password', PASSWORD_DEFAULT);

// Definicja przykładowych danych
$sample_data = <<<SQL
-- Domyślny administrator
INSERT INTO users (username, email, password, role, company_name) 
VALUES ('admin', '<EMAIL>', '$password_hash', 'admin', 'Panel Administratora');

-- Przykładowe kategorie
INSERT INTO categories (name, description) VALUES 
('Edukacja', 'Kampanie związane z edukacją'),
('Medycyna', 'Kampanie związane z medycyną i zdrowiem'),
('Firmy lokalne', 'Kampanie związane z lokalnymi firmami i usługami'),
('Finanse', 'Kampanie związane z finansami i bankowością'),
('Rozrywka', 'Kampanie związane z rozrywką i wydarzeniami'),
('Sport', 'Kampanie związane ze sportem i aktywnością fizyczną'),
('Żywność', 'Kampanie związane z żywnością i gastronomią'),
('Technologia', 'Kampanie związane z technologią i IT'),
('Moda', 'Kampanie związane z modą i ubraniami'),
('Motoryzacja', 'Kampanie związane z samochodami i pojazdami');

-- Przykładowi klienci
INSERT INTO users (username, email, password, role, company_name, balance, is_active) VALUES
('Sonokard', '<EMAIL>', '$password_hash', 'client', 'Sonokard', 1000.00, 1),
('Polański', '<EMAIL>', '$password_hash', 'client', 'Piotr Polański', 1500.00, 1),
('klient3', '<EMAIL>', '$password_hash', 'client', 'Trzecia Firma', 2000.00, 1);

-- Przykładowe wyświetlacze klientów
INSERT INTO client_displays (client_id, display_name, display_code, is_online, last_heartbeat) VALUES
(2, 'Recepcja', 'abcdef', 0, NULL),
(2, 'Poczekalnia', 'ghijkl', 0, NULL),
(3, 'Sala główna', 'mnopqr', 0, NULL),
(4, 'Korytarz', 'stuvwx', 0, NULL);

-- Przykładowe kampanie
INSERT INTO campaigns (advertiser_id, name, description, media_type, media_url, youtube_id, duration, budget, max_frequency_per_hour, start_date, end_date, is_active, category_id) VALUES
(2, 'Wideo lokalne #1', 'Lokalne wideo reklamowe #1', 'video', '/uploads/campaigns/video-01.mp4', '', 30, 500, 10, date('now'), date('now', '+30 days'), 1, 5),
(3, 'Wideo lokalne #2', 'Lokalne wideo reklamowe #2', 'video', '/uploads/campaigns/video-02.mp4', '', 30, 300, 5, date('now'), date('now', '+30 days'), 1, 5),
(2, 'Wideo lokalne #3', 'Lokalne wideo reklamowe #3', 'video', '/uploads/campaigns/video-03.mp4', '', 30, 400, 8, date('now'), date('now', '+30 days'), 1, 5),
(3, 'Wideo lokalne #4', 'Lokalne wideo reklamowe #4', 'video', '/uploads/campaigns/video-04.mp4', '', 30, 350, 7, date('now'), date('now', '+30 days'), 1, 5),
(2, 'Wideo lokalne #5', 'Lokalne wideo reklamowe #5', 'video', '/uploads/campaigns/video-05.mp4', '', 30, 450, 9, date('now'), date('now', '+30 days'), 1, 5),
(3, 'Wideo lokalne #6', 'Lokalne wideo reklamowe #6', 'video', '/uploads/campaigns/video-06.mp4', '', 30, 380, 6, date('now'), date('now', '+30 days'), 1, 5),
(2, 'Wideo lokalne #7', 'Lokalne wideo reklamowe #7', 'video', '/uploads/campaigns/video-07.mp4', '', 30, 420, 8, date('now'), date('now', '+30 days'), 1, 5),
(3, 'Wideo lokalne #8', 'Lokalne wideo reklamowe #8', 'video', '/uploads/campaigns/video-08.mp4', '', 30, 360, 7, date('now'), date('now', '+30 days'), 1, 5),
(2, 'Wideo lokalne #9', 'Lokalne wideo reklamowe #9', 'video', '/uploads/campaigns/video-09.mp4', '', 30, 390, 8, date('now'), date('now', '+30 days'), 1, 5),
(3, 'Wideo lokalne #10', 'Lokalne wideo reklamowe #10', 'video', '/uploads/campaigns/video-10.mp4', '', 30, 410, 9, date('now'), date('now', '+30 days'), 1, 5),
(2, 'Wideo lokalne #11', 'Lokalne wideo reklamowe #11', 'video', '/uploads/campaigns/video-11.mp4', '', 30, 480, 10, date('now'), date('now', '+30 days'), 1, 5),
(3, 'Wideo lokalne #12', 'Lokalne wideo reklamowe #12', 'video', '/uploads/campaigns/video-12.mp4', '', 30, 440, 8, date('now'), date('now', '+30 days'), 1, 5),
(2, 'Wideo lokalne #13', 'Lokalne wideo reklamowe #13', 'video', '/uploads/campaigns/video-13.mp4', '', 30, 460, 9, date('now'), date('now', '+30 days'), 1, 5);

-- Przykładowe przypisania kampanii
INSERT INTO campaign_assignments (campaign_id, client_id, is_accepted) VALUES
(1, 3, 1),
(1, 4, 0),
(2, 2, 1),
(2, 4, 1),
(3, 3, 1),
(3, 4, 1),
(4, 2, 1),
(4, 3, 1),
(5, 2, 1),
(5, 4, 1),
(6, 2, 1),
(6, 3, 1),
(7, 3, 1),
(7, 4, 1),
(8, 2, 1),
(8, 3, 1),
(9, 2, 1),
(9, 4, 1),
(10, 3, 1),
(10, 4, 1),
(11, 2, 1),
(11, 3, 1),
(12, 2, 1),
(12, 4, 1),
(13, 3, 1),
(13, 4, 1);

-- Przykładowe wyświetlenia reklam (ostatnie 30 dni)
INSERT INTO ad_views (campaign_id, client_id, duration_seconds, cost, timestamp)
SELECT 
    CASE WHEN random() % 13 = 0 THEN 1 
         WHEN random() % 12 = 0 THEN 2 
         WHEN random() % 11 = 0 THEN 3 
         WHEN random() % 10 = 0 THEN 4 
         WHEN random() % 9 = 0 THEN 5 
         WHEN random() % 8 = 0 THEN 6 
         WHEN random() % 7 = 0 THEN 7 
         WHEN random() % 6 = 0 THEN 8 
         WHEN random() % 5 = 0 THEN 9 
         WHEN random() % 4 = 0 THEN 10 
         WHEN random() % 3 = 0 THEN 11 
         WHEN random() % 2 = 0 THEN 12 
         ELSE 13 END as campaign_id,
    CASE WHEN random() % 3 = 0 THEN 2 ELSE (CASE WHEN random() % 2 = 0 THEN 3 ELSE 4 END) END as client_id,
    (random() % 26) + 5 as duration_seconds,
    ((random() % 26) + 5) * 0.01 as cost,
    datetime('now', '-' || (random() % 30) || ' days', '-' || (random() % 24) || ' hours', '-' || (random() % 60) || ' minutes')
FROM (SELECT 1 FROM (WITH RECURSIVE cnt(x) AS (SELECT 1 UNION ALL SELECT x+1 FROM cnt LIMIT 50) SELECT x FROM cnt));

-- Przykładowe dane dla systemu kolejkowego
-- Włączenie systemu kolejkowego dla klientów
INSERT INTO queue_config (client_id, is_enabled) VALUES
(2, 1),
(3, 1),
(4, 1);

-- Rzeczywiści lekarze z bazy reklama-newdata.db
INSERT INTO queue_doctors (client_id, first_name, last_name, photo_url, specialization, access_code, active) VALUES
(2, 'dr n. med. Małgorzata', 'Olesiak-Andryszczak', '/uploads/doctors/doctor1.webp', '', 'amf8brwig2nw', 1),
(2, 'lek. Beata', 'Dawiec', '/uploads/doctors/doctor2.webp', '', 'eagxc96nonfp', 1),
(2, 'lek. Natalia', 'Kubat', '/uploads/doctors/doctor3.webp', '', 'dayj40aswk6o', 1),
(3, 'lek. Piotr', 'Polański', '/uploads/doctors/piotr.webp', '', 'nhpsed4qn2nx', 1),
(3, 'lek. Lucyna', 'Polańska', '/uploads/doctors/lucyna.webp', '', 'f1ahqmz4smu9', 1),
(4, 'Magdalena', 'Kamińska', '/uploads/doctors/doctor6.webp', 'Pediatra', 'yw3nnpo3rqs4', 1),
(4, 'Ewa', 'Lewandowska', '/uploads/doctors/doctor7.webp', 'Chirurg', 'xk3ylyanhjwp', 1),
(2, 'dr n. med. Ewelina', 'Jasic-Szpak', '/uploads/doctors/684db9f624654.webp', '', 'yhnrp3ejse59', 1),
(2, 'dr n. med. Bożena', 'Dołęga-Kozierowska', '/uploads/doctors/684edce2dcd27.webp', '', 'x2volazwqitt', 1),
(2, 'dr n. k. f. Malwina', 'Pawik', '/uploads/doctors/684edd4e96b59.webp', '', 'gyl9rzm2a7h4', 1),
(2, 'lek. Przemysław', 'Piec', '/uploads/doctors/684edd96d5765.webp', '', '93s7rcxqkmiw', 1),
(2, 'dr n. med. Piotr', 'Siekanowicz', '/uploads/doctors/684eddc760b78.webp', '', 'bob19i0simr1', 1),
(2, 'lek. Yuliia', 'Baraniak', '/uploads/doctors/684ede1c85843.webp', '', 'k9m2n4p8q6r3', 1),
(2, 'lek. Julia', 'Nestorowicz-Czernianin', '/uploads/doctors/684ede6070b1c.webp', '', 'w6q63ujhu6xt', 1),
(2, 'lek. Jakub', 'Andrzejewski', '/uploads/doctors/684ede86cf3a3.webp', '', 't7v8b9c1d2e4f', 1),
(2, 'Pielęgniarka /', 'Położna', '', '', '4sn4c5kqthfn', 1);

-- Rzeczywiste sale z bazy reklama-newdata.db
INSERT INTO queue_rooms (client_id, name, description, doctor_id, room_number, active) VALUES
(2, 'Gabinet 1', 'Gabinet kardiologiczny', 1, '1', 1),
(2, 'Gabinet 2', 'Gabinet neurologiczny', 2, '2', 1),
(2, 'Gabinet 3', 'Gabinet ortopedyczny', 3, '3', 1),
(3, 'Gabinet 4', 'Gabinet dermatologiczny', 4, '4', 1),
(3, 'Gabinet 5', 'Gabinet okulistyczny', 5, '5', 1),
(4, 'Gabinet 6', 'Gabinet pediatryczny', 6, '6', 1),
(4, 'Gabinet 7', 'Gabinet chirurgiczny', 7, '7', 1),
(2, 'Gabinet 5', '', 3, '4', 1),
(2, 'Gabinet 6', '', NULL, '6', 1),
(2, 'Gabinet 8', '', NULL, '8', 1),
(2, 'Gabinet 9', '', NULL, '9', 1),
(2, 'Gabinet 10', '', NULL, '10', 1);

-- Przykładowe wizyty (godziny) - zaktualizowane dla wszystkich gabinetów
INSERT INTO queue_appointments (client_id, room_id, doctor_id, appointment_time, appointment_date, patient_name, status, created_at) VALUES
-- Gabinet 1 (dr n. med. Małgorzata Olesiak-Andryszczak)
(2, 1, 1, '08:00', date('now'), 'Jan Kowalski', 'completed', datetime('now', '-3 hours')),
(2, 1, 1, '08:30', date('now'), 'Anna Nowak', 'completed', datetime('now', '-2.5 hours')),
(2, 1, 1, '09:00', date('now'), 'Piotr Wiśniewski', 'completed', datetime('now', '-2 hours')),
(2, 1, 1, '09:30', date('now'), 'Maria Kowalczyk', 'current', datetime('now', '-1.5 hours')),
(2, 1, 1, '10:00', date('now'), 'Tomasz Kamiński', 'waiting', datetime('now', '-1 hour')),
(2, 1, 1, '10:30', date('now'), 'Katarzyna Lewandowska', 'waiting', datetime('now', '-30 minutes')),

-- Gabinet 2 (lek. Beata Dawiec)
(2, 2, 2, '08:15', date('now'), 'Marek Zieliński', 'completed', datetime('now', '-2.8 hours')),
(2, 2, 2, '08:45', date('now'), 'Agnieszka Dąbrowska', 'completed', datetime('now', '-2.3 hours')),
(2, 2, 2, '09:15', date('now'), 'Robert Kozłowski', 'current', datetime('now', '-1.8 hours')),
(2, 2, 2, '09:45', date('now'), 'Ewa Jankowska', 'waiting', datetime('now', '-1.2 hours')),
(2, 2, 2, '10:15', date('now'), 'Michał Mazur', 'waiting', datetime('now', '-45 minutes')),

-- Gabinet 3 (lek. Natalia Kubat)
(2, 3, 3, '08:30', date('now'), 'Joanna Król', 'completed', datetime('now', '-2.6 hours')),
(2, 3, 3, '09:00', date('now'), 'Andrzej Nowicki', 'completed', datetime('now', '-2.1 hours')),
(2, 3, 3, '09:30', date('now'), 'Barbara Kowalczyk', 'current', datetime('now', '-1.6 hours')),
(2, 3, 3, '10:00', date('now'), 'Grzegorz Wiśniewski', 'waiting', datetime('now', '-1 hour')),
(2, 3, 3, '10:30', date('now'), 'Dorota Wójcik', 'waiting', datetime('now', '-30 minutes')),

-- Gabinet 4 (Joanna Wójcik - Dermatolog)
(3, 4, 4, '08:00', date('now'), 'Adam Kamiński', 'completed', datetime('now', '-3.2 hours')),
(3, 4, 4, '08:30', date('now'), 'Magdalena Lewandowska', 'completed', datetime('now', '-2.7 hours')),
(3, 4, 4, '09:00', date('now'), 'Krzysztof Zieliński', 'current', datetime('now', '-2.2 hours')),
(3, 4, 4, '09:30', date('now'), 'Monika Dąbrowska', 'waiting', datetime('now', '-1.5 hours')),
(3, 4, 4, '10:00', date('now'), 'Łukasz Kozłowski', 'waiting', datetime('now', '-1 hour')),

-- Gabinet 5 (Agnieszka Kowalczyk - Okulista)
(3, 5, 5, '08:15', date('now'), 'Natalia Jankowska', 'completed', datetime('now', '-3 hours')),
(3, 5, 5, '08:45', date('now'), 'Paweł Mazur', 'completed', datetime('now', '-2.5 hours')),
(3, 5, 5, '09:15', date('now'), 'Karolina Król', 'current', datetime('now', '-2 hours')),
(3, 5, 5, '09:45', date('now'), 'Marek Nowicki', 'waiting', datetime('now', '-1.3 hours')),
(3, 5, 5, '10:15', date('now'), 'Anna Wiśniewska', 'waiting', datetime('now', '-45 minutes')),

-- Gabinet 6 (Magdalena Kamińska - Pediatra)
(4, 6, 6, '08:00', date('now'), 'Tomasz Kowalczyk', 'completed', datetime('now', '-3.1 hours')),
(4, 6, 6, '08:30', date('now'), 'Ewa Lewandowska', 'completed', datetime('now', '-2.6 hours')),
(4, 6, 6, '09:00', date('now'), 'Michał Zieliński', 'current', datetime('now', '-2.1 hours')),
(4, 6, 6, '09:30', date('now'), 'Joanna Dąbrowska', 'waiting', datetime('now', '-1.4 hours')),
(4, 6, 6, '10:00', date('now'), 'Piotr Kozłowski', 'waiting', datetime('now', '-1 hour')),

-- Gabinet 7 (Ewa Lewandowska - Chirurg)
(4, 7, 7, '08:15', date('now'), 'Katarzyna Jankowska', 'completed', datetime('now', '-3.3 hours')),
(4, 7, 7, '08:45', date('now'), 'Marek Mazur', 'completed', datetime('now', '-2.8 hours')),
(4, 7, 7, '09:15', date('now'), 'Agnieszka Król', 'current', datetime('now', '-2.3 hours')),
(4, 7, 7, '09:45', date('now'), 'Robert Nowicki', 'waiting', datetime('now', '-1.6 hours')),
(4, 7, 7, '10:15', date('now'), 'Barbara Wiśniewska', 'waiting', datetime('now', '-50 minutes')),

-- Gabinet 5 (dodatkowy - przypisany do lekarza 3)
(2, 8, 3, '08:30', date('now'), 'Jan Kowalczyk', 'completed', datetime('now', '-2.9 hours')),
(2, 8, 3, '09:00', date('now'), 'Anna Lewandowska', 'current', datetime('now', '-2.4 hours')),
(2, 8, 3, '09:30', date('now'), 'Piotr Zieliński', 'waiting', datetime('now', '-1.7 hours')),
(2, 8, 3, '10:00', date('now'), 'Maria Dąbrowska', 'waiting', datetime('now', '-1.2 hours')),

-- Gabinet 6 (bez przypisanego lekarza)
(2, 9, NULL, '08:00', date('now'), 'Tomasz Jankowska', 'waiting', datetime('now', '-3.5 hours')),
(2, 9, NULL, '08:30', date('now'), 'Katarzyna Mazur', 'waiting', datetime('now', '-3 hours')),
(2, 9, NULL, '09:00', date('now'), 'Marek Król', 'waiting', datetime('now', '-2.5 hours')),

-- Gabinet 8 (bez przypisanego lekarza)
(2, 10, NULL, '08:15', date('now'), 'Agnieszka Nowicki', 'waiting', datetime('now', '-3.4 hours')),
(2, 10, NULL, '08:45', date('now'), 'Robert Wiśniewska', 'waiting', datetime('now', '-2.9 hours')),
(2, 10, NULL, '09:15', date('now'), 'Barbara Kowalczyk', 'waiting', datetime('now', '-2.4 hours')),

-- Gabinet 9 (bez przypisanego lekarza)
(2, 11, NULL, '08:30', date('now'), 'Ewa Lewandowska', 'waiting', datetime('now', '-3.3 hours')),
(2, 11, NULL, '09:00', date('now'), 'Michał Zieliński', 'waiting', datetime('now', '-2.8 hours')),
(2, 11, NULL, '09:30', date('now'), 'Joanna Dąbrowska', 'waiting', datetime('now', '-2.3 hours')),

-- Gabinet 10 (bez przypisanego lekarza)
(2, 12, NULL, '08:00', date('now'), 'Piotr Kozłowski', 'waiting', datetime('now', '-3.6 hours')),
(2, 12, NULL, '08:30', date('now'), 'Maria Jankowska', 'waiting', datetime('now', '-3.1 hours')),
(2, 12, NULL, '09:00', date('now'), 'Tomasz Mazur', 'waiting', datetime('now', '-2.6 hours'));

-- Ustawienie domyślnych gabinetów dla wybranych lekarzy
UPDATE queue_doctors SET default_room_id = 1 WHERE id IN (1, 11, 13);
UPDATE queue_doctors SET default_room_id = 2 WHERE id = 2;
UPDATE queue_doctors SET default_room_id = 5 WHERE id IN (3, 14);
UPDATE queue_doctors SET default_room_id = 6 WHERE id = 12;
UPDATE queue_doctors SET default_room_id = 8 WHERE id = 10;
UPDATE queue_doctors SET default_room_id = 9 WHERE id = 9;

-- Przykładowe ustawienia importu dla systemu igabinet
INSERT INTO import_settings (client_id, system_name, sync_code, is_active, api_endpoint, sync_frequency) VALUES
(2, 'igabinet', 'igab1234567890123', 1, 'https://sonokard.igabinet.pl/admin/request/work_schedule_request.php', 3600),
(3, 'igabinet', 'igab9876543210987', 1, 'https://sonokard.igabinet.pl/admin/request/work_schedule_request.php', 7200);

-- Przykładowe mapowania lekarzy z systemu zewnętrznego
INSERT INTO external_doctor_mappings (import_setting_id, external_doctor_id, external_doctor_name, external_doctor_specialization, system_doctor_id, is_mapped) VALUES
(1, '114', 'dr n. med. Małgorzata Olesiak-Andryszczak', 'Kardiolog', 1, 1),
(1, '129', 'lek. Beata Dawiec', 'Kardiolog', 2, 1),
(1, '72', 'lek. Natalia Kubat', 'Kardiolog', 3, 1),
(1, '20', 'dr n. med. Ewelina Jasic-Szpak', 'Kardiolog', 8, 1),
(1, '1748423446', 'dr n. med. Bożena Dołęga-Kozierowska', 'Kardiolog', 9, 1);
SQL;

// Inicjalizacja bazy danych
echo "Inicjalizacja bazy danych...\n";

// Upewnij się, że katalog database istnieje
if (!is_dir('database')) {
    mkdir('database', 0777, true);
    echo "Utworzono katalog database\n";
}

$db_file = 'database/reklama.db';

// Usuń istniejącą bazę danych
if (file_exists($db_file)) {
    unlink($db_file);
    echo "Usunięto istniejącą bazę danych\n";
}

try {
    // Utwórz nową bazę danych i połącz się z nią
    $db = new PDO('sqlite:' . $db_file);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "Utworzono nową bazę danych\n";

    // Utwórz schemat bazy danych
    $db->exec($database_schema);
    echo "Utworzono schemat bazy danych\n";

    // Wstaw przykładowe dane
    $db->exec($sample_data);
    echo "Dodano przykładowe dane\n";

    echo "\nInicjalizacja bazy danych zakończona pomyślnie!\n";

    echo "\nDane logowania:\n";
    echo "Administrator: admin / password\n";
    echo "Klient 1: klient1 / password\n";
    echo "Klient 2: klient2 / password\n";
    echo "Klient 3: klient3 / password\n";

} catch (PDOException $e) {
    die("Błąd podczas inicjalizacji bazy danych: " . $e->getMessage() . "\n");
}
?> 