# Funkcjonalność: Ukrywanie Lekarzy Bez Wizyt

## Opis
Dodano funkcjonalność ukrywania lekarzy, którzy nie mają żadnych wizyt w wybranym dniu na stronie głównej kolejki (`/admin/client/queue`).

## Lokalizacja
**Plik**: `admin/views/client/queue/index.php`

## Funkcjonalność

### 1. **Przełącznik Ukrywania**
- **Lokalizacja**: Nagłówek sekcji lekarzy
- **Typ**: Bootstrap switch (form-check-switch)
- **Ikona**: `fas fa-eye-slash`
- **Etykieta**: "Ukryj lekarzy bez wizyt"

### 2. **Logika Wykrywania Lekarzy Bez Wizyt**
Lekarz jest uznawany za "bez wizyt" gdy:
- Brak aktualnej wizyty (`$currentAppointment` jest puste)
- Brak oczekujących wizyt (`$waitingAppointments` jest puste)
- Liczba oczekujących wizyt = 0 (`$doctorStats['waiting_count'] == 0`)
- Liczba zakończonych wizyt = 0 (`$doctorStats['completed_count'] == 0`)

```php
$hasAppointments = $currentAppointment || !empty($waitingAppointments) || 
                   ($doctorStats['waiting_count'] > 0) || 
                   ($doctorStats['completed_count'] > 0);
```

### 3. **Atrybuty HTML**
Każda karta lekarza otrzymuje:
- **Klasa CSS**: `doctor-card` (wszystkie) + `doctor-empty` (bez wizyt)
- **Atrybut**: `data-has-appointments="true/false"`
- **Atrybut**: `data-doctor-id="[ID]"`

### 4. **Funkcje JavaScript**

#### `toggleEmptyDoctors()`
- Ukrywa/pokazuje lekarzy na podstawie stanu przełącznika
- Aktualizuje licznik widocznych lekarzy
- Zapisuje preferencję w `localStorage`
- Wywołuje `updateEmptyDoctorsMessage()`

#### `updateEmptyDoctorsMessage()`
- Wyświetla komunikat informacyjny o liczbie ukrytych lekarzy
- Komunikat pojawia się z animacją `slideDown`
- Automatycznie usuwa poprzedni komunikat

### 5. **Zapamiętywanie Preferencji**
- Preferencja zapisywana w `localStorage` jako `hideEmptyDoctors`
- Automatyczne przywracanie przy odświeżeniu strony
- Preferencja zachowana między sesjami

## Interfejs Użytkownika

### Nagłówek Sekcji
```
Lekarze na dzień 26.08.2025 [4]    [🔘] Ukryj lekarzy bez wizyt
```

### Komunikat Informacyjny (gdy aktywny)
```
ℹ️ Ukryto 2 lekarzy bez wizyt na dzień 26.08.2025
```

### Licznik Lekarzy
- **Wszystkich**: Pokazuje całkowitą liczbę lekarzy
- **Filtrowanych**: Pokazuje liczbę widocznych lekarzy

## Animacje i Style

### CSS Transitions
```css
.doctor-card {
    transition: opacity 0.3s ease, transform 0.3s ease;
}
```

### Animacja Komunikatu
```css
@keyframes slideDown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}
```

## Przykład Użycia

### Scenariusz 1: Dzień z Mieszanymi Wizytami
- **Lekarze z wizytami**: Dr Jan Kowalski (3 wizyty), Dr Anna Nowak (1 wizyta)
- **Lekarze bez wizyt**: Dr Piotr Zieliński, Dr Maria Kowalczyk
- **Po włączeniu filtra**: Ukryto 2 lekarzy, widocznych 2/4

### Scenariusz 2: Dzień Bez Wizyt
- **Wszyscy lekarze**: Bez wizyt
- **Po włączeniu filtra**: Ukryto 4 lekarzy, widocznych 0/4
- **Komunikat**: "Ukryto 4 lekarzy bez wizyt na dzień 26.08.2025"

## Kompatybilność

### Przeglądarki
- ✅ Chrome/Edge (localStorage, CSS animations)
- ✅ Firefox (localStorage, CSS animations)
- ✅ Safari (localStorage, CSS animations)

### Responsywność
- ✅ Desktop: Pełna funkcjonalność
- ✅ Tablet: Przełącznik dostosowany do ekranu
- ✅ Mobile: Kompaktowy widok przełącznika

## Integracja z Istniejącymi Funkcjami

### Auto-refresh (30s)
- Preferencja zachowana po odświeżeniu
- Automatyczne przywracanie stanu filtra

### Nawigacja Dat
- Filtr działa niezależnie od wybranej daty
- Preferencja zachowana przy zmianie daty

### Funkcje Kolejki
- Ukryte karty nie wpływają na funkcje wywołania wizyt
- Wszystkie przyciski działają normalnie

## Korzyści

### Dla Użytkowników
- ✅ **Przejrzystość**: Skupienie na aktywnych lekarzach
- ✅ **Efektywność**: Szybsze znajdowanie lekarzy z wizytami
- ✅ **Personalizacja**: Zapamiętywanie preferencji

### Dla Systemu
- ✅ **Wydajność**: Tylko ukrywanie CSS, brak ponownych zapytań
- ✅ **Kompatybilność**: Nie wpływa na istniejące funkcje
- ✅ **Łatwość użycia**: Intuicyjny przełącznik

## Status
✅ **Zaimplementowane i przetestowane**

**Data implementacji**: 26 sierpnia 2025  
**Wersja**: 1.0  
**Status**: Gotowe do produkcji
