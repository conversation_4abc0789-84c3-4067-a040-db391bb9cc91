<?php
// Skrypt do dodania kolumn potrzebnych do importu
require_once 'core/Database.php';

Database::init();
$db = Database::getInstance()->getConnection();

echo "<h1>Dodawanie kolumn do tabeli queue_appointments</h1>";

try {
    // Sprawdź obecną strukturę
    $stmt = $db->prepare("PRAGMA table_info(queue_appointments)");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Obecne kolumny:</h2><ul>";
    $existingColumns = [];
    foreach ($columns as $column) {
        echo "<li>{$column['name']} ({$column['type']})</li>";
        $existingColumns[] = $column['name'];
    }
    echo "</ul>";
    
    // Kolumny do dodania
    $columnsToAdd = [
        'external_id' => 'VARCHAR(100)',
        'service_name' => 'VARCHAR(200)',
        'end_time' => 'TIME',
        'office' => 'VARCHAR(200)',
        'type' => 'VARCHAR(50) DEFAULT "wizyta"'
    ];
    
    echo "<h2>Dodawanie brakujących kolumn:</h2>";
    
    foreach ($columnsToAdd as $columnName => $columnType) {
        if (!in_array($columnName, $existingColumns)) {
            echo "<p>Dodaję kolumnę: <strong>$columnName</strong> ($columnType)...</p>";
            
            $sql = "ALTER TABLE queue_appointments ADD COLUMN $columnName $columnType";
            $db->exec($sql);
            
            echo "<p style='color: green;'>✅ Kolumna $columnName dodana pomyślnie!</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ Kolumna $columnName już istnieje</p>";
        }
    }
    
    // Sprawdź nową strukturę
    echo "<h2>Nowa struktura tabeli:</h2>";
    $stmt = $db->prepare("PRAGMA table_info(queue_appointments)");
    $stmt->execute();
    $newColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<ul>";
    foreach ($newColumns as $column) {
        $isNew = !in_array($column['name'], $existingColumns);
        $style = $isNew ? 'color: green; font-weight: bold;' : '';
        echo "<li style='$style'>{$column['name']} ({$column['type']})" . ($isNew ? ' [NOWA]' : '') . "</li>";
    }
    echo "</ul>";
    
    echo "<h2>✅ Wszystkie kolumny zostały dodane pomyślnie!</h2>";
    echo "<p>Teraz możesz przetestować import ponownie.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Błąd: " . $e->getMessage() . "</p>";
}
?>
