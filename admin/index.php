<?php
session_start();

// Router dla aplikacji admin
$request_uri = $_SERVER['REQUEST_URI'];

// Debug - wyświetl informacje o routingu (tymczasowo)
if (isset($_GET['debug'])) {
    echo "Original URI: " . $_SERVER['REQUEST_URI'] . "<br>";
    echo "Script Name: " . $_SERVER['SCRIPT_NAME'] . "<br>";
    echo "Current Dir: " . __DIR__ . "<br>";
    echo "Request Method: " . $_SERVER['REQUEST_METHOD'] . "<br>";
    exit;
}

// Sprawdź czy to żądanie do aplikacji admin
if (strpos($request_uri, '/admin/') === 0) {
    // Usuń /admin/ z początku URI
    $request_uri = substr($request_uri, 7);
} elseif ($request_uri === '/admin') {
    // Przekieruj /admin na /admin/
    $request_uri = '/';
} elseif (strpos($request_uri, '/wyswietlacz/') === 0) {
    // Zachowaj ścieżkę wyświetlacza bez zmian
    // $request_uri pozostaje bez zmian
} else {
    // To nie jest żądanie do aplikacji admin, przekieruj na stronę główną admin
    $request_uri = '/';
}

// Routing dla statycznych plików PWA
if (strpos($request_uri, '/pwa/') === 0) {
    $file_path = __DIR__ . '/../' . $request_uri;

    // Sprawdź czy plik istnieje
    if (file_exists($file_path) && is_file($file_path)) {
        $extension = pathinfo($file_path, PATHINFO_EXTENSION);

        // Ustaw odpowiednie nagłówki MIME
        $mime_types = [
            'html' => 'text/html',
            'css' => 'text/css',
            'js' => 'application/javascript',
            'json' => 'application/json',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon',
            'webp' => 'image/webp'
        ];

        if (isset($mime_types[$extension])) {
            header('Content-Type: ' . $mime_types[$extension]);
        }

        // Dodaj nagłówki cache dla PWA
        header('Cache-Control: public, max-age=3600');
        header('Expires: ' . gmdate('D, d M Y H:i:s \G\M\T', time() + 3600));

        readfile($file_path);
        exit;
    }
}

// Routing dla plików uploads
if (strpos($request_uri, '/uploads/') === 0) {
    $file_path = __DIR__ . '/../' . $request_uri;
    if (file_exists($file_path) && is_file($file_path)) {
        $extension = pathinfo($file_path, PATHINFO_EXTENSION);

        // Ustaw odpowiednie nagłówki MIME
        $mime_types = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'mp4' => 'video/mp4',
            'webm' => 'video/webm',
            'ogv' => 'video/ogg',
            'pdf' => 'application/pdf',
            'csv' => 'text/csv'
        ];

        if (isset($mime_types[$extension])) {
            header('Content-Type: ' . $mime_types[$extension]);
        }

        readfile($file_path);
        exit;
    }
}

// Routing dla plików assets (tylko jeśli to żądanie do admin)
if (strpos($request_uri, '/assets/') === 0) {
    $file_path = __DIR__ . $request_uri;
    if (file_exists($file_path) && is_file($file_path)) {
        $extension = pathinfo($file_path, PATHINFO_EXTENSION);

        // Ustaw odpowiednie nagłówki MIME
        $mime_types = [
            'css' => 'text/css',
            'js' => 'application/javascript',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon',
            'webp' => 'image/webp'
        ];

        if (isset($mime_types[$extension])) {
            header('Content-Type: ' . $mime_types[$extension]);
        }

        readfile($file_path);
        exit;
    }
}

// Debug - wyświetl informacje o routingu (tymczasowo)
if (isset($_GET['debug'])) {
    echo "Original URI: " . $_SERVER['REQUEST_URI'] . "<br>";
    echo "Modified URI: " . $request_uri . "<br>";
    echo "Script Name: " . $_SERVER['SCRIPT_NAME'] . "<br>";
    echo "Current Dir: " . __DIR__ . "<br>";
    echo "Request Method: " . $_SERVER['REQUEST_METHOD'] . "<br>";
    echo "Router will receive: " . $request_uri . "<br>";
    exit;
}

// Upewnij się, że $request_uri zawsze zaczyna się od ukośnika
if ($request_uri !== '' && $request_uri[0] !== '/') {
    $request_uri = '/' . $request_uri;
}

// Aktualizuj REQUEST_URI dla routera
$_SERVER['REQUEST_URI'] = $request_uri;

// Autoloader dla klas
spl_autoload_register(function ($className) {
    $paths = [
        __DIR__ . '/controllers/' . $className . '.php',
        __DIR__ . '/models/' . $className . '.php',
        __DIR__ . '/core/' . $className . '.php',
        __DIR__ . '/helpers/' . $className . '.php'
    ];

    foreach ($paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            return;
        }
    }
});

// Inicjalizacja bazy danych
Database::init();

// Definicja tras
$router = new Router();

// Trasy publiczne (bez prefiksu admin, bo już jesteśmy w aplikacji admin)
$router->get('/', [HomeController::class, 'index']);
$router->get('/login', [AuthController::class, 'loginForm']);
$router->post('/login', [AuthController::class, 'login']);
$router->get('/register', [AuthController::class, 'registerForm']);
$router->post('/register', [AuthController::class, 'register']);
$router->get('/logout', [AuthController::class, 'logout']);

// Profil i ustawienia (dla zalogowanych)
$router->get('/profile', [ProfileController::class, 'profile']);
$router->post('/profile', [ProfileController::class, 'profile']);
$router->get('/settings', [ProfileController::class, 'settings']);
$router->post('/settings', [ProfileController::class, 'settings']);

// Panel administratora (bez prefiksu admin)
$router->get('/admin', [AdminController::class, 'dashboard']);
$router->get('/admin/users', [AdminController::class, 'users']);
$router->get('/admin/add-user', [AdminController::class, 'addUser']);
$router->post('/admin/add-user', [AdminController::class, 'addUser']);
$router->get('/admin/edit-user/{id}', [AdminController::class, 'editUser']);
$router->post('/admin/edit-user/{id}', [AdminController::class, 'editUser']);
$router->get('/admin/delete-user/{id}', [AdminController::class, 'deleteUser']);
$router->post('/admin/delete-user/{id}', [AdminController::class, 'deleteUser']);
$router->get('/admin/campaigns', [AdminController::class, 'campaigns']);
$router->get('/admin/add-campaign', [AdminController::class, 'addCampaign']);
$router->post('/admin/add-campaign', [AdminController::class, 'addCampaign']);
$router->get('/admin/edit-campaign/{id}', [AdminController::class, 'editCampaignForm']);
$router->post('/admin/edit-campaign/{id}', [AdminController::class, 'editCampaign']);
$router->get('/admin/delete-campaign/{id}', [AdminController::class, 'deleteCampaignForm']);
$router->post('/admin/delete-campaign/{id}', [AdminController::class, 'deleteCampaign']);
$router->get('/admin/force-accept-campaign/{id}', [AdminController::class, 'forceAcceptCampaign']);
$router->post('/admin/force-accept-campaign/{id}', [AdminController::class, 'forceAcceptCampaign']);
$router->get('/admin/statistics', [AdminController::class, 'statistics']);
$router->get('/admin/settings', [ProfileController::class, 'settings']);

// Ustawienia importu - panel administratora
$router->get('/admin/import', [AdminController::class, 'import']);
$router->get('/admin/create-import', [AdminController::class, 'createImport']);
$router->post('/admin/create-import', [AdminController::class, 'createImport']);
$router->get('/admin/edit-import/{id}', [AdminController::class, 'editImport']);
$router->post('/admin/edit-import/{id}', [AdminController::class, 'editImport']);
$router->get('/admin/delete-import/{id}', [AdminController::class, 'deleteImport']);
$router->get('/admin/doctor-mappings/{id}', [AdminController::class, 'doctorMappings']);
$router->post('/admin/update-doctor-mapping', [AdminController::class, 'updateDoctorMapping']);
$router->post('/admin/delete-doctor-mapping', [AdminController::class, 'deleteDoctorMapping']);
$router->get('/admin/generate-sync-code', [AdminController::class, 'generateSyncCode']);
$router->post('/admin/test-connection', [AdminController::class, 'testConnection']);

// System kolejkowy - panel administratora
$router->get('/admin/queue-management', [AdminController::class, 'queueManagement']);
$router->post('/admin/toggle-queue-system', [AdminController::class, 'toggleQueueSystem']);
$router->get('/admin/client-rooms/{id}', [AdminController::class, 'clientRooms']);

// Kategorie
$router->get('/admin/categories', [CategoriesController::class, 'index']);
$router->get('/admin/categories/create', [CategoriesController::class, 'create']);
$router->post('/admin/categories/store', [CategoriesController::class, 'store']);
$router->get('/admin/categories/edit/{id}', [CategoriesController::class, 'edit']);
$router->post('/admin/categories/update/{id}', [CategoriesController::class, 'update']);
$router->get('/admin/categories/delete/{id}', [CategoriesController::class, 'delete']);
$router->post('/admin/categories/delete/{id}', [CategoriesController::class, 'delete']);

// Panel klienta (łączy funkcje reklamodawcy i reklamobiorcy)
$router->get('/client', [ClientController::class, 'dashboard']);
$router->get('/client/', [ClientController::class, 'dashboard']);

// Funkcje związane z reklamami (jako reklamodawca)
$router->get('/client/campaigns', [ClientController::class, 'campaigns']);
$router->get('/client/campaigns/create', [ClientController::class, 'createCampaign']);
$router->post('/client/store-campaign', [ClientController::class, 'storeCampaign']);
$router->get('/client/campaigns/{id}', [ClientController::class, 'showCampaign']);

// Funkcje związane z wyświetlaniem reklam (jako reklamobiorca)
$router->get('/client/display', [ClientController::class, 'display']);
$router->get('/client/displays', [ClientController::class, 'manageDisplays']);
$router->get('/client/displays/status', [ClientController::class, 'getDisplaysStatus']);
$router->post('/client/displays/add', [ClientController::class, 'addDisplay']);
$router->get('/client/displays/delete/{id}', [ClientController::class, 'deleteDisplay']);
$router->post('/client/heartbeat', [ClientController::class, 'heartbeat']);
$router->get('/client/available-campaigns', [ClientController::class, 'availableCampaigns']);
$router->post('/client/join-campaign', [ClientController::class, 'joinCampaign']);
$router->post('/client/update-auto-accept', [ClientController::class, 'updateAutoAccept']);

// System kolejkowy - główny kontroler
$router->get('/client/queue', [QueueController::class, 'index']);
$router->post('/client/queue/toggle', [QueueController::class, 'toggleSystem']);

// Ustawienia importu - panel klienta
$router->get('/client/import', [ClientImportController::class, 'index']);
$router->get('/client/import/create', [ClientImportController::class, 'create']);
$router->post('/client/import/create', [ClientImportController::class, 'create']);
$router->get('/client/import/edit/{id}', [ClientImportController::class, 'edit']);
$router->post('/client/import/edit/{id}', [ClientImportController::class, 'edit']);
$router->get('/client/import/delete/{id}', [ClientImportController::class, 'delete']);
$router->get('/client/import/doctor-mappings/{id}', [ClientImportController::class, 'doctorMappings']);
$router->post('/client/import/update-doctor-mapping', [ClientImportController::class, 'updateDoctorMapping']);
$router->post('/client/import/delete-doctor-mapping', [ClientImportController::class, 'deleteDoctorMapping']);
$router->get('/client/import/generate-sync-code', [ClientImportController::class, 'generateSyncCode']);
$router->post('/client/import/test-connection', [ClientImportController::class, 'testConnection']);

// Import CSV - system kolejkowy (przed innymi trasami z parametrami)
$router->get('/client/queue/import-csv', [QueueController::class, 'importCsv']);
$router->post('/client/queue/process-csv', [QueueController::class, 'processCsvFile']);
$router->post('/client/queue/execute-import', [QueueController::class, 'executeImport']);

// Zarządzanie salami - nowy kontroler
$router->get('/client/queue/rooms', [RoomController::class, 'index']);
$router->get('/client/queue/rooms/create', [RoomController::class, 'create']);
$router->post('/client/queue/rooms/store', [RoomController::class, 'store']);
$router->get('/client/queue/rooms/edit/{id}', [RoomController::class, 'edit']);
$router->post('/client/queue/rooms/update/{id}', [RoomController::class, 'update']);
$router->get('/client/queue/rooms/delete/{id}', [RoomController::class, 'delete']);

// Zarządzanie lekarzami - nowy kontroler
$router->get('/client/queue/doctors', [DoctorController::class, 'index']);
$router->get('/client/queue/doctors/create', [DoctorController::class, 'create']);
$router->post('/client/queue/doctors/store', [DoctorController::class, 'store']);
$router->get('/client/queue/doctors/edit/{id}', [DoctorController::class, 'edit']);
$router->post('/client/queue/doctors/update/{id}', [DoctorController::class, 'update']);
$router->get('/client/queue/doctors/delete/{id}', [DoctorController::class, 'delete']);
$router->get('/client/queue/doctors/generate-code/{id}', [DoctorController::class, 'generateAccessCode']);

// Zarządzanie wizytami - nowy kontroler
$router->get('/client/queue/appointments/{roomId}', [AppointmentController::class, 'index']);
$router->get('/client/queue/appointments/{roomId}/create', [AppointmentController::class, 'create']);
$router->post('/client/queue/appointments/{roomId}/store', [AppointmentController::class, 'store']);
$router->get('/client/queue/appointments/edit/{id}', [AppointmentController::class, 'edit']);
$router->post('/client/queue/appointments/update/{id}', [AppointmentController::class, 'update']);
$router->get('/client/queue/appointments/delete/{id}', [AppointmentController::class, 'delete']);
$router->post('/client/queue/appointments/{roomId}/call-next', [AppointmentController::class, 'callNext']);
$router->post('/client/queue/appointments/{roomId}/skip-current', [AppointmentController::class, 'skipCurrent']);

// Metody dla lekarzy (zachowane dla kompatybilności)
$router->post('/client/queue/callNextAppointmentForDoctor/{id}', [QueueController::class, 'callNextAppointmentForDoctor']);
$router->post('/client/queue/skipCurrentAppointmentForDoctor/{id}', [QueueController::class, 'skipCurrentAppointmentForDoctor']);
$router->post('/client/queue/addAppointmentForDoctor', [QueueController::class, 'addAppointmentForDoctor']);
$router->get('/client/queue/doctor-queue/{id}', [QueueController::class, 'doctorQueue']);

// Publiczny dostęp do wyświetlaczy
$router->get('/wyswietlacz/{kod}', [ClientController::class, 'displayPublic']);

// API dla systemu kolejkowego
$router->get('/api/queue/{clientId}', [QueueController::class, 'getQueueStatus']);

// API dla lekarzy (PWA)
$router->post('/api/doctor/login', [DoctorApiController::class, 'login']);
$router->get('/api/doctor/appointments/{roomId}', [DoctorApiController::class, 'appointments']);
$router->post('/api/doctor/call-next/{roomId}', [DoctorApiController::class, 'callNext']);
$router->post('/api/doctor/previous/{roomId}', [DoctorApiController::class, 'previous']);
$router->post('/api/doctor/skip-current/{roomId}', [DoctorApiController::class, 'skipCurrent']);
$router->get('/api/doctor/stats/{roomId}', [DoctorApiController::class, 'stats']);
$router->post('/api/doctor/check-room-availability', [DoctorApiController::class, 'checkRoomAvailability']);

// Wspólne funkcje
$router->get('/client/statistics', [ClientController::class, 'statistics']);

// API dla wyświetlania reklam
$router->get('/api/ads/{clientId}', [ApiController::class, 'getAds']);
$router->post('/api/ads/view', [ApiController::class, 'recordView']);

// API dla importu danych z Chrome
$router->post('/api/import', [ImportApiController::class, 'import']);
$router->get('/api/import/status/{syncCode}', [ImportApiController::class, 'status']);
$router->post('/api/import/auto-map/{syncCode}', [ImportApiController::class, 'autoMap']);

$router->run();
