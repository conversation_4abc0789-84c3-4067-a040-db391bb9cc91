<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($_SESSION["user_role"]) &&
    $_SESSION["user_role"] === "client"
        ? "KtoOstatni - System Kolejkowy"
        : (isset($title)
            ? $title
            : "KtoOstatni - System Reklamowy") ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="<?= UrlHelper::asset('css/style.css') ?>" rel="stylesheet">
    <link href="<?= UrlHelper::asset('css/custom.css') ?>" rel="stylesheet">
    <link rel="icon" href="<?= isset($_SESSION["user_role"]) &&
    $_SESSION["user_role"] === "client"
        ? UrlHelper::asset('img/queue-icon.png')
        : UrlHelper::url('favicon.ico') ?>" type="image/x-icon">
    <?php if (
        isset($_SESSION["user_role"]) &&
        $_SESSION["user_role"] === "client"
    ): ?>
    <style>
    /* Dodatkowe style dla przychodni - wyróżnienie systemu kolejkowego */
    .main-nav .nav-menu li:first-child a {
        font-weight: bold;
    }
    </style>
    <?php endif; ?>
</head>
<body class="<?= isset($_SESSION["user_role"])
    ? $_SESSION["user_role"] . "-mode"
    : "" ?>">
    <?php if (isset($_SESSION["user_id"])): ?>
    <!-- Header z logo i menu -->
    <header class="header">
        <div class="container-fluid">
            <div class="header-content">
                <div class="logo">
                    <a href="<?= UrlHelper::url() ?>">
                        <?php if (
                            isset($_SESSION["user_role"]) &&
                            $_SESSION["user_role"] === "client"
                        ): ?>
                            <i class="fas fa-user-clock me-2"></i>
                            <span>KtoOstatni - System Kolejkowy</span>
                        <?php else: ?>
                            <i class="fas fa-tv"></i>
                            <span>KtoOstatni - Panel Reklamowy</span>
                        <?php endif; ?>
                    </a>
                </div>

                <nav class="main-nav">
                    <ul class="nav-menu">
                        <li>
                            <a class="<?= isset($current_page) &&
                            ($current_page === "admin/dashboard" ||
                                $current_page === "advertiser/dashboard" ||
                                $current_page === "client/dashboard")
                                ? "active"
                                : "" ?>" href="<?= UrlHelper::url($_SESSION["user_role"]) ?>">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                <span><?= $_SESSION["user_role"] === "client"
                                    ? "Panel główny"
                                    : "Dashboard" ?></span>
                            </a>
                        </li>

                        <?php if ($_SESSION["user_role"] === "admin"): ?>
                            <li>
                                <a class="<?= isset($current_page) &&
                                $current_page === "admin/users"
                                    ? "active"
                                    : "" ?>" href="<?= UrlHelper::url('admin/users') ?>">
                                    <i class="fas fa-users me-2"></i>
                                    <span>Użytkownicy</span>
                                </a>
                            </li>
                            <li>
                                <a class="<?= isset($current_page) &&
                                strpos($current_page, "admin/categories") !==
                                    false
                                    ? "active"
                                    : "" ?>" href="<?= UrlHelper::url('admin/categories') ?>">
                                    <i class="fas fa-tags me-2"></i>
                                    <span>Kategorie</span>
                                </a>
                            </li>
                            <li>
                                <a class="<?= isset($current_page) &&
                                strpos($current_page, "admin/queue") !== false
                                    ? "active"
                                    : "" ?>" href="<?= UrlHelper::url('admin/queue-management') ?>">
                                    <i class="fas fa-list-ol me-2"></i>
                                    <span>System kolejkowy</span>
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if ($_SESSION["user_role"] !== "client"): ?>
                        <li>
                            <a class="<?= isset($current_page) &&
                            strpos($current_page, "campaigns") !== false
                                ? "active"
                                : "" ?>" href="<?= UrlHelper::url($_SESSION["user_role"] . '/campaigns') ?>">
                                <i class="fas fa-bullhorn me-2"></i>
                                <span>Kampanie</span>
                            </a>
                        </li>
                        <?php endif; ?>

                        <li>
                            <a class="<?= isset($current_page) &&
                            strpos($current_page, "statistics") !== false
                                ? "active"
                                : "" ?>" href="<?= UrlHelper::url($_SESSION["user_role"] . '/statistics') ?>">
                                <i class="fas fa-chart-bar me-2"></i>
                                <span>Statystyki</span>
                            </a>
                        </li>

                        <?php if ($_SESSION["user_role"] === "client"): ?>
                            <li>
                                <a class="<?= isset($current_page) &&
                                strpos($current_page, "client/queue") !== false
                                    ? "active"
                                    : "" ?>" href="<?= UrlHelper::url('client/queue') ?>">
                                    <i class="fas fa-list-ol me-2"></i>
                                    <span><strong>System kolejkowy</strong></span>
                                </a>
                            </li>
                            <li>
                                <a class="<?= isset($current_page) &&
                                $current_page === "client/displays"
                                    ? "active"
                                    : "" ?>" href="<?= UrlHelper::url('client/displays') ?>">
                                    <i class="fas fa-tv me-2"></i>
                                    <span>Wyświetlacze</span>
                                </a>
                            </li>
                            <li>
                                <a class="<?= isset($current_page) &&
                                $current_page === "client/available_campaigns"
                                    ? "active"
                                    : "" ?>" href="<?= UrlHelper::url('client/available-campaigns') ?>">
                                    <i class="fas fa-list me-2"></i>
                                    <span>Dostępne kampanie</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>

                <div class="user-menu">
                    <div class="dropdown">
                        <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <span class="user-name"><?= $_SESSION[
                                "username"
                            ] ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="<?= UrlHelper::url('profile') ?>"><i class="fas fa-user-circle me-2"></i>Profil</a></li>
                            <li><a class="dropdown-item" href="<?= UrlHelper::url('settings') ?>"><i class="fas fa-cog me-2"></i>Ustawienia</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?= UrlHelper::url('logout') ?>"><i class="fas fa-sign-out-alt me-2"></i>Wyloguj się</a></li>
                        </ul>
                    </div>
                </div>

                <button class="mobile-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Główna zawartość -->
    <div class="main-container">
        <?php if (isset($content)): ?>
            <?= $content ?>
        <?php else: ?>
            <div class="content-wrapper">
                <!-- Zawartość strony -->
            </div>
        <?php endif; ?>
    </div>

    <?php else: ?>
        <!-- Strona logowania/rejestracji -->
        <div class="auth-container">
            <div class="auth-box">
                <div class="auth-logo">
                    <i class="fas fa-user-clock"></i>
                    <h1>KtoOstatni - System Kolejkowy</h1>
                </div>
                <?php if (isset($content)): ?>
                    <?= $content ?>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
    <script src="<?= UrlHelper::asset('js/app.js') ?>"></script>
</body>
</html>
