<?php require_once 'views/partials/header.php'; ?>

<div class="container-fluid">
    <h1 class="h3 mb-2 text-gray-800">Dostępne kampanie reklamowe</h1>
    
    <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="alert alert-success">
            <?= $_SESSION['flash_message'] ?>
            <?php unset($_SESSION['flash_message']); ?>
        </div>
    <?php endif; ?>
    
    <!-- Ustawienia automatycznej akceptacji -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Automatyczna akceptacja kategorii</h6>
        </div>
        <div class="card-body">
            <p>Wybierz kategorie kampanii, które chcesz automatycznie akceptować:</p>
            
            <form action="<?= UrlHelper::url('client/update-auto-accept') ?>" method="post">
                <div class="row">
                    <?php foreach ($categories as $category): ?>
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" 
                                       name="auto_accept_categories[]" 
                                       value="<?= $category['id'] ?>" 
                                       id="category-<?= $category['id'] ?>"
                                       <?= in_array($category['id'], $autoAcceptCategories) ? 'checked' : '' ?>>
                                <label class="form-check-label" for="category-<?= $category['id'] ?>">
                                    <?= htmlspecialchars($category['name']) ?>
                                </label>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <button type="submit" class="btn btn-primary mt-3">
                    <i class="fas fa-save"></i> Zapisz ustawienia
                </button>
            </form>
        </div>
    </div>
    
    <!-- Lista dostępnych kampanii -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Lista kampanii</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nazwa</th>
                            <th>Reklamodawca</th>
                            <th>Kategoria</th>
                            <th>Typ</th>
                            <th>Status</th>
                            <th>Akcje</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($campaigns as $campaign): ?>
                        <tr>
                            <td><?= $campaign['id'] ?></td>
                            <td><?= htmlspecialchars($campaign['name']) ?></td>
                            <td><?= htmlspecialchars($campaign['advertiser_name']) ?></td>
                            <td><?= htmlspecialchars($campaign['category_name'] ?? 'Brak') ?></td>
                            <td>
                                <?php if ($campaign['media_type'] === 'image'): ?>
                                    <span class="badge bg-info">Obraz</span>
                                <?php elseif ($campaign['media_type'] === 'video'): ?>
                                    <span class="badge bg-primary">Wideo</span>
                                <?php elseif ($campaign['media_type'] === 'youtube'): ?>
                                    <span class="badge bg-danger">YouTube</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($campaign['is_accepted'] === null): ?>
                                    <span class="badge bg-secondary">Oczekująca</span>
                                <?php elseif ($campaign['is_accepted'] == 1): ?>
                                    <span class="badge bg-success">Zaakceptowana</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Odrzucona</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <form action="<?= UrlHelper::url('client/join-campaign') ?>" method="post" class="d-inline">
                                    <input type="hidden" name="campaign_id" value="<?= $campaign['id'] ?>">
                                    <div class="btn-group" role="group">
                                        <button type="submit" name="accept" class="btn btn-sm btn-success mr-1">
                                            <i class="fas fa-check"></i> Akceptuj
                                        </button>
                                        <button type="submit" name="reject" class="btn btn-sm btn-danger">
                                            <i class="fas fa-times"></i> Odrzuć
                                        </button>
                                    </div>
                                </form>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php require_once 'views/partials/footer.php'; ?> 