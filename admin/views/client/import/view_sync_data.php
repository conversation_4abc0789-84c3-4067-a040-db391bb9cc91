<div class="content-wrapper">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-file-code me-2"></i>
            Podgląd danych synchronizacji
        </h1>
        <div>
            <a href="<?= UrlHelper::url('client/import/doctor-mappings/' . $importSetting['id']) ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Powrót do mapowań
            </a>
        </div>
    </div>

    <!-- Informacje o synchronizacji -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="card-title">Szczegóły synchronizacji</h6>
                    <ul class="list-unstyled mb-0">
                        <li><strong>System:</strong> <?= htmlspecialchars(ucfirst($importSetting['system_name'])) ?></li>
                        <li><strong>Kod synchronizacji:</strong> <code><?= htmlspecialchars($importSetting['sync_code']) ?></code></li>
                        <li><strong>Data synchronizacji:</strong> <?= date('d.m.Y H:i:s', strtotime($syncData['created_at'])) ?></li>
                        <li><strong>Rozmiar danych:</strong> <?= round(strlen($syncData['raw_data']) / 1024, 2) ?> KB</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Dane JSON -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-code me-2"></i>
                Dane JSON
            </h5>
            <div class="btn-group">
                <button class="btn btn-sm btn-outline-secondary" id="formatJson">
                    <i class="fas fa-indent me-1"></i>
                    Formatuj
                </button>
                <button class="btn btn-sm btn-outline-secondary" id="copyJson">
                    <i class="fas fa-copy me-1"></i>
                    Kopiuj
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="bg-light p-3" style="max-height: 500px; overflow: auto;">
                <pre id="jsonDisplay" class="mb-0"><?= htmlspecialchars($syncData['raw_data']) ?></pre>
            </div>
        </div>
    </div>

    <!-- Podsumowanie danych -->
    <?php
    // Sprawdź format danych
    $hasValidFormat = $jsonData && isset($jsonData['syncData']) && isset($jsonData['syncData']['days']);
    ?>

    <?php if ($hasValidFormat): ?>
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Podsumowanie danych
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Informacje ogólne</h6>
                        <ul class="list-unstyled">
                            <li><strong>Data eksportu:</strong>
                                <?php
                                if (isset($jsonData['exportDate']) && $jsonData['exportDate']) {
                                    try {
                                        // Konwertuj UTC na lokalny czas polski
                                        $utcDate = new DateTime($jsonData['exportDate']);
                                        $utcDate->setTimezone(new DateTimeZone('Europe/Warsaw'));
                                        echo htmlspecialchars($utcDate->format('d.m.Y H:i:s'));
                                        echo ' <small class="text-muted">(czas lokalny)</small>';
                                    } catch (Exception $e) {
                                        echo htmlspecialchars($jsonData['exportDate']);
                                        echo ' <small class="text-muted">(błąd konwersji)</small>';
                                    }
                                } else {
                                    echo 'Brak';
                                }
                                ?>
                            </li>
                            <li><strong>Kod synchronizacji:</strong> <?= htmlspecialchars($jsonData['syncCode'] ?? 'Brak') ?></li>
                            <li><strong>Liczba dni:</strong> <?= count($jsonData['syncData']['days'] ?? []) ?></li>
                            <?php
                            // Oblicz łączną liczbę lekarzy i wizyt
                            $totalDoctors = 0;
                            $totalAppointments = 0;
                            if (isset($jsonData['syncData']['days']) && is_array($jsonData['syncData']['days'])) {
                                foreach ($jsonData['syncData']['days'] as $day) {
                                    if (isset($day['doctors']) && is_array($day['doctors'])) {
                                        $totalDoctors += count($day['doctors']);
                                        foreach ($day['doctors'] as $doctor) {
                                            $totalAppointments += count($doctor['appointments'] ?? []);
                                        }
                                    }
                                }
                            }
                            ?>
                            <li><strong>Liczba lekarzy:</strong> <?= $totalDoctors ?></li>
                            <li><strong>Liczba wizyt:</strong> <?= $totalAppointments ?></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Lekarze</h6>
                        <?php if (isset($jsonData['syncData']['days']) && is_array($jsonData['syncData']['days'])): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Data</th>
                                            <th>Lekarz</th>
                                            <th>Liczba wizyt</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($jsonData['syncData']['days'] as $day): ?>
                                            <?php if (isset($day['doctors']) && is_array($day['doctors'])): ?>
                                                <?php foreach ($day['doctors'] as $doctor): ?>
                                                    <tr>
                                                        <td><?= htmlspecialchars($day['date'] ?? 'Brak daty') ?></td>
                                                        <td><?= htmlspecialchars($doctor['doctorName'] ?? 'Brak nazwy') ?></td>
                                                        <td><?= count($doctor['appointments'] ?? []) ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-muted">Brak danych o lekarzach</p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Szczegóły wizyt -->
                <?php if ($totalAppointments > 0): ?>
                    <div class="mt-4">
                        <h6>Przykładowe wizyty</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Data</th>
                                        <th>Lekarz</th>
                                        <th>Pacjent</th>
                                        <th>Godzina</th>
                                        <th>Czas trwania</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $appointmentCount = 0;
                                    $maxAppointments = 10; // Pokaż maksymalnie 10 przykładowych wizyt

                                    foreach ($jsonData['syncData']['days'] as $day):
                                        if (isset($day['doctors']) && is_array($day['doctors'])):
                                            foreach ($day['doctors'] as $doctor):
                                                if (isset($doctor['appointments']) && is_array($doctor['appointments'])):
                                                    foreach ($doctor['appointments'] as $appointment):
                                                        if ($appointmentCount >= $maxAppointments) break 3;
                                                        $appointmentCount++;
                                    ?>
                                                        <tr>
                                                            <td><?= htmlspecialchars($day['date'] ?? 'Brak daty') ?></td>
                                                            <td><?= htmlspecialchars($doctor['doctorName'] ?? 'Brak nazwy') ?></td>
                                                            <td><?= htmlspecialchars(($appointment['patientFirstName'] ?? '') . ' ' . ($appointment['patientLastName'] ?? '')) ?></td>
                                                            <td><?= htmlspecialchars($appointment['appointmentStart'] ?? '') ?> - <?= htmlspecialchars($appointment['appointmentEnd'] ?? '') ?></td>
                                                            <td><?= htmlspecialchars($appointment['appointmentDuration'] ?? '') ?> min</td>
                                                        </tr>
                                    <?php
                                                    endforeach;
                                                endif;
                                            endforeach;
                                        endif;
                                    endforeach;
                                    ?>
                                </tbody>
                            </table>
                            <?php if ($appointmentCount >= $maxAppointments): ?>
                                <p class="text-muted">Pokazano <?= $maxAppointments ?> z <?= $totalAppointments ?> wizyt.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
    // Formatowanie i kopiowanie JSON
    document.addEventListener('DOMContentLoaded', function() {
        const jsonDisplay = document.getElementById('jsonDisplay');
        const formatBtn = document.getElementById('formatJson');
        const copyBtn = document.getElementById('copyJson');

        // Funkcja do formatowania JSON
        formatBtn.addEventListener('click', function() {
            try {
                const jsonData = JSON.parse(jsonDisplay.textContent);
                jsonDisplay.textContent = JSON.stringify(jsonData, null, 2);
            } catch (e) {
                alert('Nie udało się sformatować JSON: ' + e.message);
            }
        });

        // Funkcja do kopiowania JSON
        copyBtn.addEventListener('click', function() {
            try {
                navigator.clipboard.writeText(jsonDisplay.textContent).then(() => {
                    // Zmień tekst przycisku na "Skopiowano"
                    const originalText = copyBtn.innerHTML;
                    copyBtn.innerHTML = '<i class="fas fa-check me-1"></i> Skopiowano';

                    // Przywróć oryginalny tekst po 2 sekundach
                    setTimeout(() => {
                        copyBtn.innerHTML = originalText;
                    }, 2000);
                });
            } catch (e) {
                alert('Nie udało się skopiować: ' + e.message);
            }
        });

        // Automatycznie sformatuj JSON przy ładowaniu
        formatBtn.click();
    });
</script>