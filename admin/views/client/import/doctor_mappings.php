<div class="content-wrapper">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-user-md me-2"></i>
            Mapowania lekarzy - <?= htmlspecialchars(ucfirst($importSetting['system_name'])) ?>
        </h1>
        <div>
            <a href="<?= UrlHelper::url('client/import/edit/' . $importSetting['id']) ?>" class="btn btn-outline-secondary me-2">
                <i class="fas fa-cog me-2"></i>
                Ustawienia
            </a>
            <a href="<?= UrlHelper::url('client/import') ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Powrót do listy
            </a>
        </div>
    </div>

    <!-- Informacje o ustawieniu -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="card-title">Szczegóły ustawienia</h6>
                    <ul class="list-unstyled mb-0">
                        <li><strong>System:</strong> <?= htmlspecialchars(ucfirst($importSetting['system_name'])) ?></li>
                        <li><strong>Kod synchronizacji:</strong> <code><?= htmlspecialchars($importSetting['sync_code']) ?></code></li>
                        <li><strong>Status:</strong>
                            <?php if ($importSetting['is_active']): ?>
                                <span class="badge bg-success">Aktywny</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">Nieaktywny</span>
                            <?php endif; ?>
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="card-title">Status synchronizacji</h6>
                    <ul class="list-unstyled mb-0">
                        <li><strong>Ostatnia synchronizacja:</strong>
                            <?= $importSetting['last_sync'] ? date('d.m.Y H:i', strtotime($importSetting['last_sync'])) : 'Brak' ?>
                        </li>
                        <li><strong>Częstotliwość:</strong>
                            <?php
                            $frequency = $importSetting['sync_frequency'];
                            if ($frequency < 60) {
                                echo $frequency . 's';
                            } elseif ($frequency < 3600) {
                                echo floor($frequency / 60) . 'm';
                            } else {
                                echo floor($frequency / 3600) . 'h';
                            }
                            ?>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Mapowania lekarzy -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        Mapowania lekarzy
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($mappings)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-user-md fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Brak mapowań lekarzy</h5>
                            <p class="text-muted">Mapowania zostaną utworzone automatycznie po pierwszej synchronizacji z systemem zewnętrznym.</p>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Wskazówka:</strong> Użyj dodatku Chrome z kodem synchronizacji
                                <code><?= htmlspecialchars($importSetting['sync_code']) ?></code>
                                aby rozpocząć synchronizację.
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Lekarz zewnętrzny</th>
                                        <th>Specjalizacja</th>
                                        <th>Mapowanie</th>
                                        <th>Ostatnio widziany</th>
                                        <th>Akcje</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($mappings as $mapping): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($mapping['photo_url']): ?>
                                                        <img src="<?= htmlspecialchars($mapping['photo_url']) ?>"
                                                            alt="Zdjęcie lekarza"
                                                            class="rounded-circle me-2"
                                                            width="32" height="32">
                                                    <?php else: ?>
                                                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                            <?= strtoupper(substr($mapping['external_doctor_name'], 0, 1)) ?>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <div class="fw-bold"><?= htmlspecialchars($mapping['external_doctor_name']) ?></div>
                                                        <small class="text-muted">ID: <?= htmlspecialchars($mapping['external_doctor_id']) ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($mapping['external_doctor_specialization']): ?>
                                                    <span class="badge bg-info"><?= htmlspecialchars($mapping['external_doctor_specialization']) ?></span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($mapping['is_mapped'] && $mapping['system_doctor_id']): ?>
                                                    <div class="d-flex align-items-center">
                                                        <span class="badge bg-success me-2">
                                                            <i class="fas fa-link me-1"></i>
                                                            Przypisany
                                                        </span>
                                                        <div>
                                                            <div class="fw-bold"><?= htmlspecialchars($mapping['first_name'] . ' ' . $mapping['last_name']) ?></div>
                                                            <?php if ($mapping['system_specialization']): ?>
                                                                <small class="text-muted"><?= htmlspecialchars($mapping['system_specialization']) ?></small>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">
                                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                                        Nieprzypisany
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($mapping['last_seen']): ?>
                                                    <span class="text-muted">
                                                        <?php
                                                        try {
                                                            // Sprawdź czy data zawiera informację o strefie czasowej
                                                            if (strpos($mapping['last_seen'], 'T') !== false && strpos($mapping['last_seen'], 'Z') !== false) {
                                                                // Data w formacie ISO 8601 UTC - konwertuj na lokalny czas
                                                                $utcDate = new DateTime($mapping['last_seen']);
                                                                $utcDate->setTimezone(new DateTimeZone('Europe/Warsaw'));
                                                                echo $utcDate->format('d.m.Y H:i');
                                                            } else {
                                                                // Data prawdopodobnie już w lokalnym czasie
                                                                echo date('d.m.Y H:i', strtotime($mapping['last_seen']));
                                                            }
                                                        } catch (Exception $e) {
                                                            echo date('d.m.Y H:i', strtotime($mapping['last_seen']));
                                                        }
                                                        ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button"
                                                        class="btn btn-sm btn-outline-primary"
                                                        onclick="editMapping(<?= $mapping['id'] ?>, '<?= htmlspecialchars($mapping['external_doctor_name']) ?>', <?= $mapping['system_doctor_id'] ?: 'null' ?>)"
                                                        title="Edytuj mapowanie">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button"
                                                        class="btn btn-sm btn-outline-danger"
                                                        onclick="deleteMapping(<?= $mapping['id'] ?>, '<?= htmlspecialchars($mapping['external_doctor_name']) ?>')"
                                                        title="Usuń mapowanie">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Informacje
                    </h6>
                </div>
                <div class="card-body">
                    <h6>Jak działa mapowanie?</h6>
                    <p class="small text-muted">
                        Mapowanie pozwala na przypisanie lekarzy z systemu zewnętrznego
                        do lekarzy w Twoim systemie kolejkowym.
                    </p>

                    <h6>Automatyczne mapowanie</h6>
                    <p class="small text-muted">
                        Po pierwszej synchronizacji z systemem zewnętrznym,
                        lekarze zostaną automatycznie dodani do listy mapowań.
                    </p>

                    <h6>Ręczne przypisanie</h6>
                    <p class="small text-muted">
                        Kliknij ikonę edycji przy każdym lekarzu, aby przypisać go
                        do odpowiedniego lekarza w Twoim systemie.
                    </p>

                    <hr>

                    <h6>Statystyki</h6>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border rounded p-2">
                                <div class="h4 mb-0 text-primary">
                                    <?= count(array_filter($mappings, fn($m) => $m['is_mapped'])) ?>
                                </div>
                                <small class="text-muted">Przypisani</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-2">
                                <div class="h4 mb-0 text-warning">
                                    <?= count(array_filter($mappings, fn($m) => !$m['is_mapped'])) ?>
                                </div>
                                <small class="text-muted">Nieprzypisani</small>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- Ostatnia synchronizacja -->
                    <h6>Ostatnia synchronizacja</h6>
                    <?php if (isset($latestSyncData) && $latestSyncData): ?>
                        <p class="small text-muted mb-2">
                            Ostatnia synchronizacja: <?= date('d.m.Y H:i:s', strtotime($latestSyncData['created_at'])) ?>
                        </p>
                        <a href="<?= UrlHelper::url('client/import/view-sync-data/' . $importSetting['id'] . '/' . $latestSyncData['id']) ?>"
                            class="btn btn-sm btn-outline-primary mb-3">
                            <i class="fas fa-eye me-1"></i>
                            Podgląd ostatnich danych JSON
                        </a>

                        <?php if (isset($syncHistory) && !empty($syncHistory)): ?>
                            <h6 class="mt-3">Historia synchronizacji</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead>
                                        <tr>
                                            <th>Data</th>
                                            <th>Rozmiar</th>
                                            <th>Akcje</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($syncHistory as $historyItem): ?>
                                            <tr>
                                                <td><?= date('d.m.Y H:i:s', strtotime($historyItem['created_at'])) ?></td>
                                                <td><?= round($historyItem['data_size'] / 1024, 2) ?> KB</td>
                                                <td>
                                                    <a href="<?= UrlHelper::url('client/import/view-sync-data/' . $importSetting['id'] . '/' . $historyItem['id']) ?>"
                                                        class="btn btn-sm btn-outline-secondary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="alert alert-info small">
                            <i class="fas fa-info-circle me-2"></i>
                            Brak danych synchronizacji. Użyj dodatku Chrome, aby zsynchronizować dane.
                        </div>
                    <?php endif; ?>

                    <hr>

                    <h6>Dodatek Chrome</h6>
                    <p class="small text-muted">
                        Użyj dodatku Chrome z katalogu <code>chrome1/</code>
                        do synchronizacji danych z systemem zewnętrznym.
                    </p>
                    <div class="alert alert-warning small">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Ważne:</strong> Kod synchronizacji musi być identyczny
                        w dodatku Chrome i w tym ustawieniu.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal edycji mapowania -->
<div class="modal fade" id="editMappingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edytuj mapowanie lekarza</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="<?= UrlHelper::url('client/import/update-doctor-mapping') ?>">
                <div class="modal-body">
                    <input type="hidden" name="mapping_id" id="mapping_id">
                    <input type="hidden" name="import_setting_id" value="<?= $importSetting['id'] ?>">

                    <div class="mb-3">
                        <label class="form-label">Lekarz zewnętrzny</label>
                        <input type="text" class="form-control" id="external_doctor_name" readonly>
                    </div>

                    <div class="mb-3">
                        <label for="system_doctor_id" class="form-label">Przypisz do lekarza w systemie</label>
                        <select class="form-select" id="system_doctor_id" name="system_doctor_id">
                            <option value="">Brak przypisania</option>
                            <?php foreach ($systemDoctors as $doctor): ?>
                                <option value="<?= $doctor['id'] ?>">
                                    <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                    <?php if ($doctor['specialization']): ?>
                                        (<?= htmlspecialchars($doctor['specialization']) ?>)
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">
                            Wybierz lekarza z Twojego systemu lub zostaw puste, aby usunąć przypisanie
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anuluj</button>
                    <button type="submit" class="btn btn-primary">Zapisz mapowanie</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function editMapping(mappingId, doctorName, systemDoctorId) {
        document.getElementById('mapping_id').value = mappingId;
        document.getElementById('external_doctor_name').value = doctorName;
        document.getElementById('system_doctor_id').value = systemDoctorId || '';

        const modal = new bootstrap.Modal(document.getElementById('editMappingModal'));
        modal.show();
    }

    function deleteMapping(mappingId, doctorName) {
        if (confirm(`Czy na pewno chcesz usunąć mapowanie dla lekarza "${doctorName}"?\n\nTa operacja jest nieodwracalna.`)) {
            // Utwórz formularz do wysłania żądania DELETE
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/admin/client/import/delete-doctor-mapping';

            // Dodaj ukryte pola
            const mappingIdField = document.createElement('input');
            mappingIdField.type = 'hidden';
            mappingIdField.name = 'mapping_id';
            mappingIdField.value = mappingId;
            form.appendChild(mappingIdField);

            const importSettingIdField = document.createElement('input');
            importSettingIdField.type = 'hidden';
            importSettingIdField.name = 'import_setting_id';
            importSettingIdField.value = '<?= $importSetting['id'] ?>';
            form.appendChild(importSettingIdField);

            // Dodaj formularz do strony i wyślij
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>