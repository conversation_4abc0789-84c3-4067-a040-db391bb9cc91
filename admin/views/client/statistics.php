<div class="container mt-4">
    <h1 class="mb-4">Statystyki</h1>
    
    <ul class="nav nav-tabs mb-4" id="statsTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="advertiser-tab" data-bs-toggle="tab" data-bs-target="#advertiser" type="button" role="tab" aria-controls="advertiser" aria-selected="true">
                Jako reklamodawca
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="client-tab" data-bs-toggle="tab" data-bs-target="#client" type="button" role="tab" aria-controls="client" aria-selected="false">
                Jako reklamobiorca
            </button>
        </li>
    </ul>
    
    <div class="tab-content" id="statsTabsContent">
        <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> reklamodawcy -->
        <div class="tab-pane fade show active" id="advertiser" role="tabpanel" aria-labelledby="advertiser-tab">
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            Wyświetlenia Twoich reklam w ostatnich 30 dniach
                        </div>
                        <div class="card-body">
                            <canvas id="advertiserViewsChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            Wydatki na reklamę
                        </div>
                        <div class="card-body">
                            <canvas id="advertiserRevenueChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            Szczegółowe statystyki
                        </div>
                        <div class="card-body">
                            <?php
                            $totalViews = 0;
                            $totalRevenue = 0;
                            
                            foreach ($advertiserStats as $stat) {
                                $totalViews += $stat['views'];
                                $totalRevenue += $stat['revenue'];
                            }
                            ?>
                            <div class="row mb-3">
                                <div class="col-6">
                                    <h5>Łączna liczba wyświetleń:</h5>
                                </div>
                                <div class="col-6 text-end">
                                    <h5><?= number_format($totalViews, 0, ',', ' ') ?></h5>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <h5>Łączny koszt:</h5>
                                </div>
                                <div class="col-6 text-end">
                                    <h5><?= number_format($totalRevenue, 2, ',', ' ') ?> zł</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            Historia wyświetleń
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Data</th>
                                            <th class="text-end">Liczba wyświetleń</th>
                                            <th class="text-end">Koszt</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($advertiserStats as $stat): ?>
                                        <tr>
                                            <td><?= date('d.m.Y', strtotime($stat['date'])) ?></td>
                                            <td class="text-end"><?= number_format($stat['views'], 0, ',', ' ') ?></td>
                                            <td class="text-end"><?= number_format($stat['revenue'], 2, ',', ' ') ?> zł</td>
                                        </tr>
                                        <?php endforeach; ?>
                                        
                                        <?php if (count($advertiserStats) == 0): ?>
                                        <tr>
                                            <td colspan="3" class="text-center">Brak danych do wyświetlenia</td>
                                        </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Zakładka reklamobiorcy -->
        <div class="tab-pane fade" id="client" role="tabpanel" aria-labelledby="client-tab">
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            Wyświetlenia reklam na Twoich wyświetlaczach w ostatnich 30 dniach
                        </div>
                        <div class="card-body">
                            <canvas id="clientViewsChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            Zarobki z reklam
                        </div>
                        <div class="card-body">
                            <canvas id="clientEarningsChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            Szczegółowe statystyki
                        </div>
                        <div class="card-body">
                            <?php
                            $totalClientViews = 0;
                            $totalEarned = 0;
                            
                            foreach ($clientStats as $stat) {
                                $totalClientViews += $stat['views'];
                                $totalEarned += $stat['earned'];
                            }
                            ?>
                            <div class="row mb-3">
                                <div class="col-6">
                                    <h5>Łączna liczba wyświetleń:</h5>
                                </div>
                                <div class="col-6 text-end">
                                    <h5><?= number_format($totalClientViews, 0, ',', ' ') ?></h5>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <h5>Łączny zarobek:</h5>
                                </div>
                                <div class="col-6 text-end">
                                    <h5><?= number_format($totalEarned, 2, ',', ' ') ?> zł</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            Historia wyświetleń
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Data</th>
                                            <th class="text-end">Liczba wyświetleń</th>
                                            <th class="text-end">Zarobek</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($clientStats as $stat): ?>
                                        <tr>
                                            <td><?= date('d.m.Y', strtotime($stat['date'])) ?></td>
                                            <td class="text-end"><?= number_format($stat['views'], 0, ',', ' ') ?></td>
                                            <td class="text-end"><?= number_format($stat['earned'], 2, ',', ' ') ?> zł</td>
                                        </tr>
                                        <?php endforeach; ?>
                                        
                                        <?php if (count($clientStats) == 0): ?>
                                        <tr>
                                            <td colspan="3" class="text-center">Brak danych do wyświetlenia</td>
                                        </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Przygotowanie danych dla wykresów reklamodawcy
    const advertiserDates = <?= json_encode(array_map(function($stat) { 
        return date('d.m', strtotime($stat['date'])); 
    }, array_reverse($advertiserStats))) ?>;
    
    const advertiserViews = <?= json_encode(array_map(function($stat) { 
        return $stat['views']; 
    }, array_reverse($advertiserStats))) ?>;
    
    const advertiserRevenue = <?= json_encode(array_map(function($stat) { 
        return $stat['revenue']; 
    }, array_reverse($advertiserStats))) ?>;
    
    // Przygotowanie danych dla wykresów reklamobiorcy
    const clientDates = <?= json_encode(array_map(function($stat) { 
        return date('d.m', strtotime($stat['date'])); 
    }, array_reverse($clientStats))) ?>;
    
    const clientViews = <?= json_encode(array_map(function($stat) { 
        return $stat['views']; 
    }, array_reverse($clientStats))) ?>;
    
    const clientEarned = <?= json_encode(array_map(function($stat) { 
        return $stat['earned']; 
    }, array_reverse($clientStats))) ?>;
    
    // Wykres wyświetleń reklamodawcy
    new Chart(document.getElementById('advertiserViewsChart'), {
        type: 'line',
        data: {
            labels: advertiserDates,
            datasets: [{
                label: 'Liczba wyświetleń',
                data: advertiserViews,
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 2,
                tension: 0.1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            },
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Liczba wyświetleń Twoich reklam'
                }
            }
        }
    });
    
    // Wykres kosztów reklamodawcy
    new Chart(document.getElementById('advertiserRevenueChart'), {
        type: 'line',
        data: {
            labels: advertiserDates,
            datasets: [{
                label: 'Koszt reklam (zł)',
                data: advertiserRevenue,
                backgroundColor: 'rgba(153, 102, 255, 0.2)',
                borderColor: 'rgba(153, 102, 255, 1)',
                borderWidth: 2,
                tension: 0.1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toFixed(2) + ' zł';
                        }
                    }
                }
            },
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Koszt wyświetlania Twoich reklam'
                }
            }
        }
    });
    
    // Wykres wyświetleń reklamobiorcy
    new Chart(document.getElementById('clientViewsChart'), {
        type: 'line',
        data: {
            labels: clientDates,
            datasets: [{
                label: 'Liczba wyświetleń',
                data: clientViews,
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                tension: 0.1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            },
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Liczba wyświetleń reklam na Twoich wyświetlaczach'
                }
            }
        }
    });
    
    // Wykres zarobków reklamobiorcy
    new Chart(document.getElementById('clientEarningsChart'), {
        type: 'line',
        data: {
            labels: clientDates,
            datasets: [{
                label: 'Zarobek (zł)',
                data: clientEarned,
                backgroundColor: 'rgba(255, 159, 64, 0.2)',
                borderColor: 'rgba(255, 159, 64, 1)',
                borderWidth: 2,
                tension: 0.1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toFixed(2) + ' zł';
                        }
                    }
                }
            },
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Zarobki z wyświetlania reklam'
                }
            }
        }
    });
});
</script>