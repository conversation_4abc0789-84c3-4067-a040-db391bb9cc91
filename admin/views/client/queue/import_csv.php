<?php require_once 'views/partials/header.php'; ?>

<div class="bg-primary text-white py-3 mb-4">
    <div class="container-fluid d-flex justify-content-between align-items-center">
        <h4 class="mb-0 d-flex align-items-center">
            <i class="fas fa-file-csv me-2"></i> Import danych z pliku CSV
        </h4>
        <div class="d-flex gap-2">
            <a href="<?= UrlHelper::url('client/queue') ?>" class="btn btn-light text-primary" title="Powrót do systemu kolejkowego">
                <i class="fas fa-arrow-left"></i> Powrót
            </a>
        </div>
    </div>
</div>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-file-csv me-2"></i>
                        Import danych z pliku CSV
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Instrukcja importu:</h6>
                        <ul class="mb-0">
                            <li>Plik musi być w formacie CSV</li>
                            <li>System automatycznie wykryje separator (przecinek, średnik lub pionowa kreska)</li>
                            <li>System automatycznie obsłuży kodowanie Windows (Windows-1250/CP1250)</li>
                            <li>Plik powinien zawierać sekcje z lekarzami w formacie "Lekarz: NAZWA LEKARZA"</li>
                            <li><strong>Ważna jest tylko kolumna "Godz.zaplanowana" (kolumna 3)</strong></li>
                            <li>Wiersze bez godziny w kolumnie "Godz.zaplanowana" zostaną pominięte</li>
                            <li>Wizyty oznaczone jako "poza kol." lub "Powtórka Rp." zostaną pominięte</li>
                            <li>Dane wizyt powinny być w kolumnach: Lp., Godz.przyjęcia, <strong>Godz.zaplanowana</strong>, Godz.przyjęcia, Pacjent, Adres</li>
                        </ul>
                    </div>

                    <form action="<?= UrlHelper::url('client/queue/process-csv') ?>" method="post" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="csv_file" class="form-label">Wybierz plik CSV:</label>
                            <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                            <div class="form-text">Maksymalny rozmiar pliku: 10MB</div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?= UrlHelper::url('client/queue') ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Powrót
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload me-2"></i>Prześlij i analizuj
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Przykład struktury pliku -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        Przykład struktury pliku CSV
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Lp.</th>
                                    <th>Godz.przyjęcia</th>
                                    <th>Godz.zaplanowana</th>
                                    <th>Godz.przyjęcia</th>
                                    <th>Pacjent</th>
                                    <th>Adres</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>10:51</td>
                                    <td>10:00</td>
                                    <td>10:51</td>
                                    <td>ANTONINA GRZYBOWSKA</td>
                                    <td>ul. UNISŁAW ŚLĄSKI 32, 58-350, UNISŁAW ŚLĄSKI</td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>10:35</td>
                                    <td>10:15</td>
                                    <td>10:35</td>
                                    <td>DAWID MIELCZAREK</td>
                                    <td>ul. KWIATOWA 5, 58-350, MIEROSZÓW</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'views/partials/footer.php'; ?> 