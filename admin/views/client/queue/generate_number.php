<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pobierz numerek - <?= htmlspecialchars($room['name']) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f5f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            max-width: 600px;
            margin: 50px auto;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: none;
        }
        .card-header {
            background-color: #2c3e50;
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
            text-align: center;
        }
        .card-body {
            padding: 30px;
        }
        .btn-primary {
            background-color: #3498db;
            border-color: #3498db;
            padding: 12px 20px;
            font-size: 18px;
            border-radius: 30px;
            width: 100%;
            margin-top: 20px;
        }
        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
        }
        .room-info {
            text-align: center;
            margin-bottom: 20px;
        }
        .room-name {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .number-display {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
            display: none;
        }
        .number-value {
            font-size: 60px;
            font-weight: bold;
            color: #e74c3c;
            margin: 10px 0;
        }
        .info-text {
            color: #7f8c8d;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .spinner-border {
            width: 3rem;
            height: 3rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header">
                <h2>System kolejkowy</h2>
                <p class="mb-0">Pobierz numer do kolejki</p>
            </div>
            <div class="card-body">
                <div class="room-info">
                    <div class="room-name"><?= htmlspecialchars($room['name']) ?></div>
                    <?php if (!empty($room['description'])): ?>
                        <p class="text-muted"><?= htmlspecialchars($room['description']) ?></p>
                    <?php endif; ?>
                </div>
                
                <div class="info-text">
                    Kliknij poniższy przycisk, aby pobrać numer w kolejce.
                </div>
                
                <div id="loadingIndicator" class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Ładowanie...</span>
                    </div>
                    <p class="mt-2">Generowanie numeru...</p>
                </div>
                
                <div id="numberDisplay" class="number-display">
                    <p>Twój numer to:</p>
                    <div id="numberValue" class="number-value">0</div>
                    <p class="text-muted">Zachowaj ten numer i czekaj na wywołanie.</p>
                </div>
                
                <button id="generateBtn" class="btn btn-primary">
                    <i class="fas fa-ticket-alt me-2"></i> Pobierz numerek
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // Identyfikatory potrzebne do generowania numeru
        const roomId = <?= $room['id'] ?>;
        const clientId = <?= $room['client_id'] ?>;
        
        // Elementy DOM
        const generateBtn = document.getElementById('generateBtn');
        const loadingIndicator = document.getElementById('loadingIndicator');
        const numberDisplay = document.getElementById('numberDisplay');
        const numberValue = document.getElementById('numberValue');
        
        // Obsługa przycisku generowania numeru
        generateBtn.addEventListener('click', function() {
            // Pokaż ładowanie
            generateBtn.style.display = 'none';
            loadingIndicator.style.display = 'block';
            
            // Wyślij żądanie o nowy numer
            fetch('/client/queue/generate-number', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: `room_id=${roomId}&client_id=${clientId}`
            })
            .then(response => response.json())
            .then(data => {
                // Ukryj ładowanie
                loadingIndicator.style.display = 'none';
                
                if (data.success) {
                    // Pokaż wygenerowany numer
                    numberValue.textContent = data.number;
                    numberDisplay.style.display = 'block';
                    
                    // Ukryj przycisk (nie pozwól generować wielu numerów)
                    generateBtn.style.display = 'none';
                } else {
                    // Pokaż błąd
                    alert(data.error || 'Wystąpił błąd podczas generowania numeru.');
                    generateBtn.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Błąd:', error);
                alert('Wystąpił błąd podczas komunikacji z serwerem.');
                loadingIndicator.style.display = 'none';
                generateBtn.style.display = 'block';
            });
        });
    </script>
</body>
</html> 