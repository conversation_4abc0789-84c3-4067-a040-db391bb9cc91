<?php require_once 'views/partials/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Panel lekarza - <?= htmlspecialchars($room['name']) ?></h1>
                <a href="<?= UrlHelper::url('client/queue') ?>" class="btn btn-secondary">Powrót do systemu kolejkowego</a>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Aktualny numer</h5>
                        </div>
                        <div class="card-body text-center">
                            <?php if ($currentNumber): ?>
                                <div class="display-1 mb-3"><?= $currentNumber['number'] ?></div>
                                <p>Wywołany o: <?= date('H:i:s', strtotime($currentNumber['called_at'])) ?></p>
                                <div class="d-flex justify-content-center gap-2">
                                    <button id="completeBtn" class="btn btn-success btn-lg">
                                        <i class="fas fa-check"></i> Zakończ wizytę
                                    </button>
                                    <button id="skipBtn" class="btn btn-warning btn-lg">
                                        <i class="fas fa-forward"></i> Pomiń
                                    </button>
                                </div>
                            <?php else: ?>
                                <div class="text-muted my-5">
                                    <p class="h3">Brak aktualnego numeru</p>
                                    <?php if (empty($waitingNumbers)): ?>
                                        <p>Brak oczekujących w kolejce.</p>
                                    <?php else: ?>
                                        <button id="callFirstBtn" class="btn btn-primary btn-lg mt-3">
                                            <i class="fas fa-bullhorn"></i> Wywołaj pierwszy numer
                                        </button>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Oczekujący (<?= count($waitingNumbers) ?>)</h5>
                        </div>
                        <?php if (empty($waitingNumbers)): ?>
                            <div class="card-body text-center text-muted py-4">
                                <p>Brak oczekujących w kolejce</p>
                            </div>
                        <?php else: ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($waitingNumbers as $index => $number): ?>
                                    <div class="list-group-item">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong class="me-2">Nr <?= $number['number'] ?></strong>
                                                <small class="text-muted">
                                                    (dodany o <?= date('H:i:s', strtotime($number['created_at'])) ?>)
                                                </small>
                                            </div>
                                            <?php if ($index === 0 && !$currentNumber): ?>
                                                <button class="call-next-btn btn btn-sm btn-primary" data-number-id="<?= $number['id'] ?>">
                                                    Wywołaj
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Generowanie nowego numeru</h5>
                        </div>
                        <div class="card-body">
                            <p>Podaj ten adres pacjentom, aby mogli pobrać numer:</p>
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" readonly value="<?= (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'] ?>/wyswietlacz/numerek/<?= $room['id'] ?>">
                                <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard(this.previousElementSibling)">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            
                            <hr>
                            
                            <p>Lub wygeneruj nowy numer ręcznie:</p>
                            <button id="generateNumberBtn" class="btn btn-primary">
                                Wygeneruj nowy numer
                            </button>
                            <div id="generatedNumber" class="mt-3" style="display: none;">
                                <div class="alert alert-success">
                                    Wygenerowano numer: <strong id="newNumberValue"></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Statystyki dzisiejszego dnia</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6 mb-3">
                                    <div class="h5">Obsłużonych</div>
                                    <div class="display-6" id="completedCount">0</div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="h5">Oczekujących</div>
                                    <div class="display-6" id="waitingCount"><?= count($waitingNumbers) ?></div>
                                </div>
                                <div class="col-6">
                                    <div class="h5">Pominiętych</div>
                                    <div class="display-6" id="skippedCount">0</div>
                                </div>
                                <div class="col-6">
                                    <div class="h5">Wszystkich</div>
                                    <div class="display-6" id="totalCount">0</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Funkcja do kopiowania tekstu do schowka
    function copyToClipboard(element) {
        element.select();
        document.execCommand('copy');
        alert('Skopiowano do schowka!');
    }
    
    // Dane pokoju
    const roomId = <?= $room['id'] ?>;
    const clientId = <?= $user['id'] ?>;
    
    // Obsługa przycisku zakończenia wizyty
    document.getElementById('completeBtn')?.addEventListener('click', function() {
        callNext();
    });
    
    // Obsługa przycisku pomijania numeru
    document.getElementById('skipBtn')?.addEventListener('click', function() {
        skipCurrent();
    });
    
    // Obsługa przycisku wywołania pierwszego numeru
    document.getElementById('callFirstBtn')?.addEventListener('click', function() {
        callNext();
    });
    
    // Obsługa przycisku generowania nowego numeru
    document.getElementById('generateNumberBtn').addEventListener('click', function() {
        generateNumber();
    });
    
    // Obsługa przycisków wywołania następnego numeru
    document.querySelectorAll('.call-next-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            callNext();
        });
    });
    
    // Funkcja wywołująca następny numer
    function callNext() {
        fetch(`/client/queue/callNext/${roomId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Odśwież stronę, aby pokazać nowy stan
                location.reload();
            } else {
                alert(data.message || 'Brak kolejnych numerów w kolejce.');
            }
        })
        .catch(error => {
            console.error('Błąd:', error);
            alert('Wystąpił błąd podczas wywoływania następnego numeru.');
        });
    }
    
    // Funkcja pomijająca aktualny numer
    function skipCurrent() {
        fetch(`/client/queue/skipCurrent/${roomId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Odśwież stronę, aby pokazać nowy stan
                location.reload();
            } else {
                alert(data.message || 'Nie można pominąć aktualnego numeru.');
            }
        })
        .catch(error => {
            console.error('Błąd:', error);
            alert('Wystąpił błąd podczas pomijania numeru.');
        });
    }
    
    // Funkcja generująca nowy numer
    function generateNumber() {
        fetch('/client/queue/generate-number', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: `room_id=${roomId}&client_id=${clientId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Pokaż wygenerowany numer
                document.getElementById('newNumberValue').textContent = data.number;
                document.getElementById('generatedNumber').style.display = 'block';
                
                // Zaktualizuj licznik oczekujących
                const waitingCount = document.getElementById('waitingCount');
                waitingCount.textContent = (parseInt(waitingCount.textContent) + 1).toString();
                
                // Zaktualizuj licznik wszystkich
                const totalCount = document.getElementById('totalCount');
                totalCount.textContent = (parseInt(totalCount.textContent) + 1).toString();
                
                // Po 3 sekundach odśwież stronę
                setTimeout(() => {
                    location.reload();
                }, 3000);
            } else {
                alert(data.error || 'Nie udało się wygenerować numeru.');
            }
        })
        .catch(error => {
            console.error('Błąd:', error);
            alert('Wystąpił błąd podczas generowania numeru.');
        });
    }
    
    // Funkcja do ładowania statystyk
    function loadStats() {
        fetch(`/api/queue/${clientId}`)
        .then(response => response.json())
        .then(data => {
            const queueInfo = data.queue.find(q => q.room.id == roomId);
            if (queueInfo) {
                // Pobierz statystyki dla tej sali
                const completedCount = document.getElementById('completedCount');
                const skippedCount = document.getElementById('skippedCount');
                const totalCount = document.getElementById('totalCount');
                
                // Zaktualizuj dane
                fetch(`/client/queue/stats/${roomId}`)
                .then(response => response.json())
                .then(stats => {
                    completedCount.textContent = stats.completed_count || 0;
                    skippedCount.textContent = stats.cancelled_count || 0;
                    totalCount.textContent = (stats.waiting_count + stats.current_count + stats.completed_count + stats.cancelled_count) || 0;
                })
                .catch(error => {
                    console.error('Błąd podczas pobierania statystyk:', error);
                });
            }
        })
        .catch(error => {
            console.error('Błąd podczas pobierania informacji o kolejce:', error);
        });
    }
    
    // Załaduj statystyki przy wczytywaniu strony
    // loadStats();
</script>

<?php require_once 'views/partials/footer.php'; ?>