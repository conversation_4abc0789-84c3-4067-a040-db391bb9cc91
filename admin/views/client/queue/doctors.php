<?php require_once 'views/partials/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><PERSON>arz<PERSON><PERSON><PERSON><PERSON> le<PERSON>zami</h1>
                <div>
                    <a href="<?= UrlHelper::url('client/queue') ?>" class="btn btn-secondary me-2">Powrót do systemu kolejkowego</a>
                    <a href="<?= UrlHelper::url('client/queue/doctors/create') ?>" class="btn btn-primary">Dodaj lekarza</a>
                </div>
            </div>
            
            <?php if (isset($_SESSION['flash_message'])): ?>
                <div class="alert alert-success">
                    <?= $_SESSION['flash_message'] ?>
                    <?php unset($_SESSION['flash_message']); ?>
                </div>
            <?php endif; ?>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Lista lekarzy</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($doctors)): ?>
                        <div class="alert alert-info">
                            Nie masz jeszcze dodanych lekarzy. Dodaj pierwszego lekarza, aby rozpocząć korzystanie z systemu kolejkowego.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Zdjęcie</th>
                                        <th>Imię i nazwisko</th>
                                        <th>Specjalizacja</th>
                                        <th>Domyślny gabinet</th>
                                        <th>Kod dostępu</th>
                                        <th>Status</th>
                                        <th>Akcje</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($doctors as $doctor): ?>
                                        <tr>
                                            <td>
                                                <?php if ($doctor['photo_url']): ?>
                                                    <img src="<?= htmlspecialchars($doctor['photo_url']) ?>" 
                                                         alt="Zdjęcie lekarza" 
                                                         style="width: 50px; height: 50px; border-radius: 50%; object-fit: cover;">
                                                <?php else: ?>
                                                    <div style="width: 50px; height: 50px; border-radius: 50%; background-color: #ccc; display: flex; align-items: center; justify-content: center;">
                                                        <i class="fas fa-user-md"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?></strong>
                                            </td>
                                            <td><?= htmlspecialchars($doctor['specialization'] ?: 'Brak specjalizacji') ?></td>
                                            <td>
                                                <?php if ($doctor['default_room_id']): ?>
                                                    <?php 
                                                    // Znajdź nazwę domyślnego gabinetu
                                                    $defaultRoom = null;
                                                    foreach ($rooms ?? [] as $room) {
                                                        if ($room['id'] == $doctor['default_room_id']) {
                                                            $defaultRoom = $room;
                                                            break;
                                                        }
                                                    }
                                                    ?>
                                                    <?php if ($defaultRoom): ?>
                                                        <span class="badge bg-info">
                                                            <?= htmlspecialchars($defaultRoom['name']) ?>
                                                            <?= $defaultRoom['room_number'] ? ' (' . htmlspecialchars($defaultRoom['room_number']) . ')' : '' ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-muted">Gabinet nie istnieje</span>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Brak domyślnego gabinetu</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($doctor['access_code']): ?>
                                                    <code class="bg-light p-1 rounded"><?= htmlspecialchars($doctor['access_code']) ?></code>
                                                <?php else: ?>
                                                    <span class="text-muted">Brak kodu</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($doctor['active']): ?>
                                                    <span class="badge bg-success">Aktywny</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Nieaktywny</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= UrlHelper::url('client/queue/doctors/edit/' . $doctor['id']) ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-edit"></i> Edytuj
                                                    </a>
                                                    <a href="<?= UrlHelper::url('client/queue/doctors/generate-code/' . $doctor['id']) ?>" 
                                                       class="btn btn-sm btn-outline-info"
                                                       title="Generuj kod dostępu">
                                                        <i class="fas fa-key"></i> Kod
                                                    </a>
                                                    <a href="<?= UrlHelper::url('client/queue/doctors/delete/' . $doctor['id']) ?>" 
                                                       class="btn btn-sm btn-outline-danger"
                                                       onclick="return confirm('Czy na pewno chcesz usunąć tego lekarza?')">
                                                        <i class="fas fa-trash"></i> Usuń
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Informacje o systemie zarządzania gabinetami</h6>
                                <ul class="mb-0">
                                    <li><strong>Domyślny gabinet:</strong> Automatycznie przypisywany do nowych wizyt lekarza</li>
                                    <li><strong>Zmiana gabinetu:</strong> Lekarz może zmienić gabinet przy dodawaniu wizyt na dany dzień</li>
                                    <li><strong>Wielu lekarzy w gabinecie:</strong> W tym samym gabinecie mogą przyjmować różni lekarze w ciągu dnia</li>
                                    <li><strong>Kod dostępu:</strong> Format xxxx-xxxx-xxxx, używany do logowania w aplikacji PWA</li>
                                </ul>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'views/partials/footer.php'; ?> 