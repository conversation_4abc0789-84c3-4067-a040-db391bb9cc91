<?php require_once 'views/partials/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Panel lekarza - <?= htmlspecialchars($room['name']) ?></h1>
                <div>
                    <a href="<?= UrlHelper::url('client/queue') ?>" class="btn btn-secondary">Powrót do systemu kolejkowego</a>
                </div>
            </div>
            
            <!-- Wybór daty -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Wybór daty</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="<?= UrlHelper::url('client/queue/appointments/' . $room['id']) ?>" class="row align-items-end">
                        <div class="col-md-4">
                            <label for="selected_date" class="form-label">Data wizyt</label>
                            <div class="d-flex gap-2">
                                <button type="submit" name="date" value="<?= date('Y-m-d', strtotime(($_GET['date'] ?? date('Y-m-d')) . ' -1 day')) ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <input type="date" class="form-control" id="selected_date" name="date" 
                                       value="<?= htmlspecialchars($_GET['date'] ?? date('Y-m-d')) ?>">
                                <button type="submit" name="date" value="<?= date('Y-m-d', strtotime(($_GET['date'] ?? date('Y-m-d')) . ' +1 day')) ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Pokaż wizyty
                            </button>
                            <a href="<?= UrlHelper::url('client/queue/appointments/' . $room['id']) ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-calendar-day"></i> Dzisiaj
                            </a>
                        </div>
                        <div class="col-md-4 text-end">
                            <small class="text-muted">
                                Wybierz datę, aby przeglądać wizyty z różnych dni
                            </small>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Informacje o lekarzu -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Informacje o lekarzu</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <?php if ($room['doctor_photo']): ?>
                                <img src="<?= htmlspecialchars($room['doctor_photo']) ?>" 
                                     alt="Zdjęcie lekarza" 
                                     style="width: 100px; height: 100px; border-radius: 50%; object-fit: cover;">
                            <?php else: ?>
                                <div style="width: 100px; height: 100px; border-radius: 50%; background-color: #ccc; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-user-md fa-2x"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-10">
                            <h4><?= htmlspecialchars($room['doctor_first_name'] . ' ' . $room['doctor_last_name']) ?></h4>
                            <p class="text-muted">
                                <strong>Specjalizacja:</strong> <?= htmlspecialchars($room['doctor_specialization'] ?: 'Brak specjalizacji') ?><br>
                                <strong>Gabinet:</strong> <?= htmlspecialchars($room['room_number'] ?: 'Brak numeru') ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <!-- Aktualna wizyta -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Aktualna wizyta</h5>
                        </div>
                        <div class="card-body text-center">
                            <?php if ($currentAppointment): ?>
                                <div class="display-4 text-primary mb-3">
                                    <?= htmlspecialchars($currentAppointment['appointment_time']) ?>
                                </div>
                                <h5><?= htmlspecialchars($currentAppointment['patient_name'] ?: 'Pacjent') ?></h5>
                                <div class="mt-3">
                                    <button class="btn btn-success btn-lg me-2" onclick="completeCurrent()">
                                        <i class="fas fa-check"></i> Zakończ wizytę
                                    </button>
                                    <button class="btn btn-warning btn-lg me-2" onclick="skipCurrent()">
                                        <i class="fas fa-forward"></i> Pomiń
                                    </button>
                                    <div class="btn-group btn-group-sm mt-2">
                                        <a href="<?= UrlHelper::url('client/queue/appointments/edit/' . $currentAppointment['id']) ?>" 
                                           class="btn btn-outline-primary" title="Edytuj">
                                            <i class="fas fa-edit"></i> Edytuj
                                        </a>
                                        <a href="<?= UrlHelper::url('client/queue/appointments/delete/' . $currentAppointment['id']) ?>" 
                                           class="btn btn-outline-danger" title="Usuń"
                                           onclick="return confirm('Czy na pewno chcesz usunąć tę wizytę?')">
                                            <i class="fas fa-trash"></i> Usuń
                                        </a>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="display-4 text-muted mb-3">--:--</div>
                                <h5>Brak aktualnej wizyty</h5>
                                <div class="mt-3">
                                    <button class="btn btn-primary btn-lg" onclick="callNext()">
                                        <i class="fas fa-play"></i> Wywołaj następną
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Oczekujące wizyty -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">Oczekujące wizyty</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($waitingAppointments)): ?>
                                <div class="text-center text-muted">
                                    <i class="fas fa-clock fa-3x mb-3"></i>
                                    <p>Brak oczekujących wizyt</p>
                                </div>
                            <?php else: ?>
                                <div class="list-group">
                                    <?php foreach ($waitingAppointments as $appointment): ?>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1"><?= htmlspecialchars($appointment['appointment_time']) ?></h6>
                                                <small class="text-muted">
                                                    <?= htmlspecialchars($appointment['patient_name'] ?: 'Pacjent') ?>
                                                </small>
                                            </div>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-primary rounded-pill me-2">
                                                    <?= date('H:i', strtotime($appointment['appointment_time'])) ?>
                                                </span>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?= UrlHelper::url('client/queue/appointments/edit/' . $appointment['id']) ?>" 
                                                       class="btn btn-outline-primary" title="Edytuj">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="<?= UrlHelper::url('client/queue/appointments/delete/' . $appointment['id']) ?>" 
                                                       class="btn btn-outline-danger" title="Usuń"
                                                       onclick="return confirm('Czy na pewno chcesz usunąć tę wizytę?')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Lista wszystkich wizyt -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        Wszystkie wizyty 
                        <?php 
                        $selectedDate = $_GET['date'] ?? date('Y-m-d');
                        $dateObj = new DateTime($selectedDate);
                        $today = new DateTime();
                        
                        if ($dateObj->format('Y-m-d') === $today->format('Y-m-d')) {
                            echo 'dzisiaj';
                        } else {
                            echo 'z dnia ' . $dateObj->format('d.m.Y');
                        }
                        ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($allAppointments)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-calendar fa-3x mb-3"></i>
                            <p>
                                Brak wizyt 
                                <?php 
                                $selectedDate = $_GET['date'] ?? date('Y-m-d');
                                $dateObj = new DateTime($selectedDate);
                                $today = new DateTime();
                                
                                if ($dateObj->format('Y-m-d') === $today->format('Y-m-d')) {
                                    echo 'na dzisiaj';
                                } else {
                                    echo 'na dzień ' . $dateObj->format('d.m.Y');
                                }
                                ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Godzina</th>
                                        <th>Pacjent</th>
                                        <th>Lekarz</th>
                                        <th>Status</th>
                                        <th>Utworzona</th>
                                        <th>Akcje</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($allAppointments as $appointment): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($appointment['appointment_time']) ?></strong>
                                            </td>
                                            <td>
                                                <?= htmlspecialchars($appointment['patient_name'] ?: 'Pacjent') ?>
                                            </td>
                                            <td>
                                                <?php if ($appointment['doctor_id']): ?>
                                                    <?php 
                                                    // Znajdź informacje o lekarzu
                                                    $doctor = null;
                                                    foreach ($doctors as $d) {
                                                        if ($d['id'] == $appointment['doctor_id']) {
                                                            $doctor = $d;
                                                            break;
                                                        }
                                                    }
                                                    ?>
                                                    <?php if ($doctor): ?>
                                                        <span class="badge bg-info">
                                                            <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                                            <?= $doctor['specialization'] ? ' (' . htmlspecialchars($doctor['specialization']) . ')' : '' ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-muted">Lekarz nie istnieje</span>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Brak przypisanego lekarza</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $appointment['status'] === 'waiting' ? 'warning' : ($appointment['status'] === 'in_progress' ? 'primary' : ($appointment['status'] === 'completed' ? 'success' : 'secondary')) ?>">
                                                    <?= $appointment['status'] === 'waiting' ? 'Oczekująca' : ($appointment['status'] === 'in_progress' ? 'Aktualna' : ($appointment['status'] === 'completed' ? 'Zakończona' : 'Anulowana')) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('H:i', strtotime($appointment['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?= UrlHelper::url('client/queue/appointments/edit/' . $appointment['id']) ?>" 
                                                       class="btn btn-outline-primary" title="Edytuj">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($appointment['status'] !== 'completed' && $appointment['status'] !== 'cancelled'): ?>
                                                        <a href="<?= UrlHelper::url('client/queue/appointments/delete/' . $appointment['id']) ?>" 
                                                           class="btn btn-outline-danger" title="Usuń"
                                                           onclick="return confirm('Czy na pewno chcesz usunąć tę wizytę?')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Dodawanie nowej wizyty -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Dodaj nową wizytę</h5>
                </div>
                <div class="card-body">
                    <form id="addAppointmentForm">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="appointment_date" class="form-label">Data wizyty *</label>
                                    <input type="date" class="form-control" id="appointment_date" name="appointment_date" 
                                           value="<?= htmlspecialchars($_GET['date'] ?? date('Y-m-d')) ?>" 
                                           min="<?= date('Y-m-d') ?>" required>
                                    <div class="form-text">Data wizyty</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="appointment_time" class="form-label">Godzina wizyty *</label>
                                    <input type="time" class="form-control" id="appointment_time" name="appointment_time" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="doctor_id" class="form-label">Lekarz</label>
                                    <select class="form-control" id="doctor_id" name="doctor_id">
                                        <option value="">-- Wybierz lekarza --</option>
                                        <?php 
                                        // Pobierz listę lekarzy dla tego klienta
                                        $queueSystem = new QueueSystem();
                                        $doctors = $queueSystem->getDoctors($user['id']);
                                        foreach ($doctors as $doctor): 
                                        ?>
                                            <option value="<?= $doctor['id'] ?>" 
                                                    <?= ($room['doctor_id'] == $doctor['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                                <?= $doctor['specialization'] ? ' (' . htmlspecialchars($doctor['specialization']) . ')' : '' ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text">
                                        Jeśli nie wybierzesz lekarza, zostanie przypisany domyślny lekarz gabinetu
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="patient_name" class="form-label">Imię i nazwisko pacjenta</label>
                                    <input type="text" class="form-control" id="patient_name" name="patient_name" 
                                           placeholder="Opcjonalne">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-plus"></i> Dodaj
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    const roomId = <?= $room['id'] ?>;
    const clientId = <?= $user['id'] ?>;
    
    // Wywołanie następnej wizyty
    function callNext() {
        fetch(`/client/queue/callNextAppointment/${roomId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Nie można wywołać następnej wizyty.');
            }
        })
        .catch(error => {
            console.error('Błąd:', error);
            alert('Wystąpił błąd podczas wywoływania wizyty.');
        });
    }
    
    // Zakończenie aktualnej wizyty
    function completeCurrent() {
        callNext(); // To automatycznie zakończy aktualną i wywoła następną
    }
    
    // Pomijanie aktualnej wizyty
    function skipCurrent() {
        fetch(`/client/queue/skipCurrentAppointment/${roomId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Nie można pominąć aktualnej wizyty.');
            }
        })
        .catch(error => {
            console.error('Błąd:', error);
            alert('Wystąpił błąd podczas pomijania wizyty.');
        });
    }
    
    // Dodawanie nowej wizyty
    document.getElementById('addAppointmentForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData();
        formData.append('room_id', roomId);
        formData.append('client_id', clientId);
        formData.append('appointment_date', document.getElementById('appointment_date').value);
        formData.append('appointment_time', document.getElementById('appointment_time').value);
        formData.append('doctor_id', document.getElementById('doctor_id').value);
        formData.append('patient_name', document.getElementById('patient_name').value);
        
        fetch('/client/queue/add-appointment', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Przekieruj na stronę z wybraną datą
                const selectedDate = document.getElementById('appointment_date').value;
                window.location.href = `/client/queue/appointments/${roomId}?date=${selectedDate}`;
            } else {
                alert(data.error || 'Nie można dodać wizyty.');
            }
        })
        .catch(error => {
            console.error('Błąd:', error);
            alert('Wystąpił błąd podczas dodawania wizyty.');
        });
    });
    
    // Automatyczne odświeżanie co 30 sekund
    setInterval(function() {
        location.reload();
    }, 30000);
</script>

<?php require_once 'views/partials/footer.php'; ?> 