<?php require_once 'views/partials/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Edycja wizyty</h1>
                <div>
                    <a href="<?= UrlHelper::url('client/queue/appointments/' . $appointment['room_id']) ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Powrót do panelu lekarza
                    </a>
                </div>
            </div>
            
            <?php if (isset($_SESSION['flash_message'])): ?>
                <div class="alert alert-info">
                    <?= $_SESSION['flash_message'] ?>
                    <?php unset($_SESSION['flash_message']); ?>
                </div>
            <?php endif; ?>
            
            <!-- Informacje o sali i lekarzu -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Informacje o gabinecie</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Gabinet:</strong> <?= htmlspecialchars($room['name']) ?></p>
                            <p><strong>Numer gabinetu:</strong> <?= htmlspecialchars($room['room_number'] ?: 'Brak numeru') ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Lekarz:</strong> 
                                <?php if (isset($room['doctor_first_name']) && $room['doctor_first_name']): ?>
                                    dr <?= htmlspecialchars($room['doctor_first_name'] . ' ' . ($room['doctor_last_name'] ?? '')) ?>
                                <?php else: ?>
                                    Nie przypisany
                                <?php endif; ?>
                            </p>
                            <p><strong>Status wizyty:</strong> 
                                <span class="badge bg-<?= $appointment['status'] === 'waiting' ? 'warning' : ($appointment['status'] === 'current' ? 'primary' : ($appointment['status'] === 'completed' ? 'success' : 'secondary')) ?>">
                                    <?= $appointment['status'] === 'waiting' ? 'Oczekująca' : ($appointment['status'] === 'current' ? 'Aktualna' : ($appointment['status'] === 'completed' ? 'Zakończona' : 'Anulowana')) ?>
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Formularz edycji -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Edycja wizyty</h5>
                </div>
                <div class="card-body">
                    <form action="<?= UrlHelper::url('client/queue/appointments/update/' . $appointment['id']) ?>" method="POST">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="appointment_date" class="form-label">Data wizyty *</label>
                                    <input type="date" class="form-control" id="appointment_date" name="appointment_date" 
                                           value="<?= htmlspecialchars($appointment['appointment_date'] ?? date('Y-m-d', strtotime($appointment['created_at']))) ?>" 
                                           min="<?= date('Y-m-d') ?>" required>
                                    <div class="form-text">Wybierz datę wizyty</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="appointment_time" class="form-label">Godzina wizyty *</label>
                                    <input type="time" class="form-control" id="appointment_time" name="appointment_time" 
                                           value="<?= htmlspecialchars($appointment['appointment_time']) ?>" required>
                                    <div class="form-text">Wybierz godzinę wizyty</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="doctor_id" class="form-label">Lekarz</label>
                                    <select class="form-control" id="doctor_id" name="doctor_id">
                                        <option value="">-- Wybierz lekarza --</option>
                                        <?php foreach ($doctors as $doctor): ?>
                                            <option value="<?= $doctor['id'] ?>" <?= ($appointment['doctor_id'] == $doctor['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                                <?= $doctor['specialization'] ? ' (' . htmlspecialchars($doctor['specialization']) . ')' : '' ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text">Jeśli nie wybierzesz lekarza, zostanie przypisany domyślny lekarz gabinetu</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="patient_name" class="form-label">Imię i nazwisko pacjenta</label>
                                    <input type="text" class="form-control" id="patient_name" name="patient_name" 
                                           value="<?= htmlspecialchars($appointment['patient_name']) ?>" 
                                           placeholder="Opcjonalne">
                                    <div class="form-text">Możesz zostawić puste, jeśli pacjent nie podał danych</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Data utworzenia</label>
                                    <input type="text" class="form-control" value="<?= date('d.m.Y H:i', strtotime($appointment['created_at'])) ?>" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Ostatnia aktualizacja</label>
                                    <input type="text" class="form-control" value="<?= $appointment['called_at'] ? date('d.m.Y H:i', strtotime($appointment['called_at'])) : 'Brak' ?>" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="<?= UrlHelper::url('client/queue/appointments/delete/' . $appointment['id']) ?>" 
                               class="btn btn-danger"
                               onclick="return confirm('Czy na pewno chcesz usunąć tę wizytę? Tej operacji nie można cofnąć.')">
                                <i class="fas fa-trash"></i> Usuń wizytę
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Zapisz zmiany
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'views/partials/footer.php'; ?> 