<?php require_once "views/partials/header.php"; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Dodaj nową salę</h1>
                <a href="<?= UrlHelper::url('client/queue') ?>" class="btn btn-secondary">Powrót do systemu kolejkowego</a>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Informacje o sali</h5>
                </div>
                <div class="card-body">
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger">
                            <?= $error ?>
                        </div>
                    <?php endif; ?>

                    <form method="post" action="<?= UrlHelper::url('client/queue/rooms/store') ?>">
                        <div class="mb-3">
                            <label for="name" class="form-label">Nazwa sali *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="form-text">Np. "Gabinet 1", "Sala operacyjna", "Recepcja"</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Opis (opcjonalny)</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            <div class="form-text">Krótki opis sali lub dodatkowe informacje.</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="room_number" class="form-label">Numer gabinetu</label>
                                    <input type="text" class="form-control" id="room_number" name="room_number"
                                           placeholder="np. 1, 2, A1">
                                    <div class="form-text">Numer gabinetu wyświetlany pacjentom</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="doctor_id" class="form-label">Przypisany lekarz</label>
                                    <select class="form-control" id="doctor_id" name="doctor_id">
                                        <option value="">-- Wybierz lekarza --</option>
                                        <?php
                                        // Pobierz listę lekarzy dla tego klienta
                                        $queueSystem = new QueueSystem();
                                        $doctors = $queueSystem->getDoctors(
                                            $user["id"],
                                        );
                                        foreach ($doctors as $doctor): ?>
                                            <option value="<?= $doctor[
                                                "id"
                                            ] ?>"> <?= htmlspecialchars(
    $doctor["first_name"] . " " . $doctor["last_name"],
) ?> <?= $doctor["specialization"]
     ? " (" . htmlspecialchars($doctor["specialization"]) . ")"
     : "" ?> </option>
                                        <?php endforeach;
                                        ?>
                                    </select>
                                    <div class="form-text">
                                        <a href="<?= UrlHelper::url('client/queue/doctors/create') ?>" target="_blank">Dodaj nowego lekarza</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?= UrlHelper::url('client/queue') ?>" class="btn btn-secondary">Anuluj</a>
                            <button type="submit" class="btn btn-primary">Dodaj salę</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once "views/partials/footer.php"; ?>
