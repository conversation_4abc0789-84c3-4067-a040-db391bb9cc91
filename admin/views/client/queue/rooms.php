<?php require_once 'views/partials/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Lista gabinetów</h1>
                <a href="<?= UrlHelper::url('client/queue/rooms/create') ?>" class="btn btn-primary">Dodaj gabinet</a>
            </div>
            <?php if (isset($_SESSION['flash_message'])): ?>
                <div class="alert alert-success">
                    <?= $_SESSION['flash_message'] ?>
                    <?php unset($_SESSION['flash_message']); ?>
                </div>
            <?php endif; ?>
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Twoje gabinety</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($rooms)): ?>
                        <div class="alert alert-info"><PERSON><PERSON> masz jeszcze dodanych gabinetów.</div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Nazwa</th>
                                        <th>Numer</th>
                                        <th>Lekarz</th>
                                        <th>Status</th>
                                        <th>Akcje</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($rooms as $room): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($room['name']) ?></td>
                                            <td><?= htmlspecialchars($room['room_number'] ?: '-') ?></td>
                                            <td>
                                                <?php if ($room['doctor_first_name']): ?>
                                                    <?= htmlspecialchars($room['doctor_first_name'] . ' ' . $room['doctor_last_name']) ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Brak</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($room['active']): ?>
                                                    <span class="badge bg-success">Aktywny</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Nieaktywny</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="<?= UrlHelper::url('client/queue/rooms/edit/' . $room['id']) ?>" class="btn btn-outline-secondary btn-sm" title="Edytuj"><i class="fas fa-edit"></i></a>
                                                <a href="<?= UrlHelper::url('client/queue/rooms/delete/' . $room['id']) ?>" class="btn btn-outline-danger btn-sm ms-1" title="Usuń" onclick="return confirm('Czy na pewno chcesz usunąć ten gabinet?')"><i class="fas fa-trash"></i></a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'views/partials/footer.php'; ?> 