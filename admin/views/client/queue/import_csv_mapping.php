<?php require_once 'views/partials/header.php'; ?>

<div class="bg-primary text-white py-3 mb-4">
    <div class="container-fluid d-flex justify-content-between align-items-center">
        <h4 class="mb-0 d-flex align-items-center">
            <i class="fas fa-cogs me-2"></i> Konfiguracja importu - <?= htmlspecialchars($fileName) ?>
        </h4>
        <div class="d-flex gap-2">
            <a href="<?= UrlHelper::url('client/queue/import-csv') ?>" class="btn btn-light text-primary" title="Powrót do importu">
                <i class="fas fa-arrow-left"></i> Powrót
            </a>
        </div>
    </div>
</div>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-10 offset-md-1">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        Konfiguracja importu - <?= htmlspecialchars($fileName) ?>
                    </h4>
                </div>
                <div class="card-body">
                    <form action="<?= UrlHelper::url('client/queue/execute-import') ?>" method="post">
                        <!-- Data wizyt -->
                        <div class="mb-4">
                            <label for="appointment_date" class="form-label">Data wizyt:</label>
                            <input type="date" class="form-control" id="appointment_date" name="appointment_date" 
                                   value="<?= htmlspecialchars($defaultDate) ?>" required>
                            <div class="form-text">
                                <?php if (isset($defaultDate) && $defaultDate !== date('Y-m-d')): ?>
                                    <i class="fas fa-info-circle text-info"></i> 
                                    Wykryto datę z pliku CSV: <?= date('d.m.Y', strtotime($defaultDate)) ?>
                                <?php else: ?>
                                    Wybierz datę, na którą mają być zaimportowane wizyty
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Mapowanie lekarzy -->
                        <div class="mb-4">
                            <h5><i class="fas fa-user-md me-2"></i>Mapowanie lekarzy</h5>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Dla każdego lekarza z pliku CSV wybierz odpowiadającego mu lekarza w systemie.
                            </div>
                            
                            <?php foreach ($parsedData as $sectionIndex => $section): ?>
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-user me-2"></i>
                                            <?= htmlspecialchars($section['doctor_name']) ?>
                                            <span class="badge bg-primary ms-2"><?= count($section['appointments']) ?> wizyt</span>
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label class="form-label">Przypisz do lekarza w systemie:</label>
                                                <select class="form-select doctor-mapping" 
                                                        data-section="<?= $sectionIndex ?>" required>
                                                    <option value="">-- Wybierz lekarza --</option>
                                                    <?php foreach ($doctors as $doctor): ?>
                                                        <?php 
                                                        $isSelected = isset($savedMappings[$section['doctor_name']]) && 
                                                                     $savedMappings[$section['doctor_name']] == $doctor['id'];
                                                        ?>
                                                        <option value="<?= $doctor['id'] ?>" <?= $isSelected ? 'selected' : '' ?>>
                                                            <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                                <input type="hidden" name="doctor_mappings[<?= $sectionIndex ?>][csv_doctor]" 
                                                       value="<?= htmlspecialchars($section['doctor_name']) ?>">
                                                <input type="hidden" name="doctor_mappings[<?= $sectionIndex ?>][system_doctor]" 
                                                       class="doctor-id-input">
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">Podgląd wizyt:</label>
                                                <div class="small text-muted">
                                                    <?php 
                                                    $previewCount = 0;
                                                    foreach ($section['appointments'] as $appointment): 
                                                        if ($previewCount >= 3) break;
                                                    ?>
                                                        <div><?= htmlspecialchars($appointment['time']) ?> - <?= htmlspecialchars($appointment['patient']) ?></div>
                                                    <?php 
                                                        $previewCount++;
                                                    endforeach; 
                                                    if (count($section['appointments']) > 3): ?>
                                                        <div class="text-muted">... i <?= count($section['appointments']) - 3 ?> więcej</div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- Opcje dodatkowe -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="remember_mappings" name="remember_mappings">
                                <label class="form-check-label" for="remember_mappings">
                                    Zapamiętaj mapowania lekarzy dla przyszłych importów
                                </label>
                                <div class="form-text">Mapowania będą automatycznie wypełniane przy kolejnych importach</div>
                            </div>
                        </div>

                        <!-- Podsumowanie -->
                        <div class="mb-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Podsumowanie importu:</h6>
                                <ul class="mb-0">
                                    <li>Liczba lekarzy do zaimportowania: <strong><?= count($parsedData) ?></strong></li>
                                    <li>Łączna liczba wizyt: <strong><?= array_sum(array_map(function($section) { return count($section['appointments']); }, $parsedData)) ?></strong></li>
                                    <li><strong>Używana jest tylko kolumna "Godz.zaplanowana"</strong></li>
                                    <li>Wiersze bez poprawnej godziny zostaną pominięte</li>
                                    <li>Wizyty poza kolejką zostaną pominięte</li>
                                    <li>Duplikaty wizyt o tej samej godzinie zostaną pominięte</li>
                                </ul>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?= UrlHelper::url('client/queue/import-csv') ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Powrót
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check me-2"></i>Wykonaj import
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Automatyczne wypełnianie mapowań lekarzy
    const doctorSelects = document.querySelectorAll('.doctor-mapping');
    
    doctorSelects.forEach((select) => {
        // Wypełnij ukryte pole przy ładowaniu strony
        const sectionIndex = select.getAttribute('data-section');
        const doctorId = select.value;
        const doctorIdInput = document.querySelector(`input[name="doctor_mappings[${sectionIndex}][system_doctor]"]`);
        if (doctorId && doctorIdInput) {
            doctorIdInput.value = doctorId;
        }
        
        // Obsługa zmiany wyboru
        select.addEventListener('change', function() {
            const sectionIndex = this.getAttribute('data-section');
            const doctorId = this.value;
            const doctorIdInput = document.querySelector(`input[name="doctor_mappings[${sectionIndex}][system_doctor]"]`);
            if (doctorIdInput) {
                doctorIdInput.value = doctorId;
            }
        });
    });
});
</script>

<?php require_once 'views/partials/footer.php'; ?> 