<?php require_once 'views/partials/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><PERSON><PERSON><PERSON><PERSON> le<PERSON></h1>
                <a href="<?= UrlHelper::url('client/queue/doctors') ?>" class="btn btn-secondary">Powrót do listy lekarzy</a>
            </div>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <?= $error ?>
                </div>
            <?php endif; ?>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Informacje o lekarzu</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="<?= UrlHelper::url('client/queue/doctors/update/' . $doctor['id']) ?>" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">Imię *</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" 
                                           value="<?= htmlspecialchars($doctor['first_name']) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">Nazwisko *</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" 
                                           value="<?= htmlspecialchars($doctor['last_name']) ?>" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="specialization" class="form-label">Specjalizacja</label>
                            <input type="text" class="form-control" id="specialization" name="specialization" 
                                   value="<?= htmlspecialchars($doctor['specialization']) ?>"
                                   placeholder="np. Kardiolog, Neurolog, Ortopeda">
                        </div>
                        
                        <div class="mb-3">
                            <label for="default_room_id" class="form-label">Domyślny gabinet</label>
                            <select class="form-control" id="default_room_id" name="default_room_id">
                                <option value="">-- Wybierz domyślny gabinet --</option>
                                <?php foreach ($rooms as $room): ?>
                                    <option value="<?= $room['id'] ?>" 
                                            <?= ($doctor['default_room_id'] == $room['id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($room['name']) ?>
                                        <?= $room['room_number'] ? ' (Gabinet ' . htmlspecialchars($room['room_number']) . ')' : '' ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">
                                Domyślny gabinet będzie automatycznie przypisywany do nowych wizyt tego lekarza.
                                <a href="<?= UrlHelper::url('client/queue/rooms/create') ?>" target="_blank">Dodaj nowy gabinet</a>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="photo" class="form-label">Zdjęcie</label>
                            <?php if ($doctor['photo_url']): ?>
                                <div class="mb-2">
                                    <img src="<?= htmlspecialchars($doctor['photo_url']) ?>" 
                                         alt="Aktualne zdjęcie" 
                                         style="width: 100px; height: 100px; border-radius: 50%; object-fit: cover;">
                                    <div class="form-text">Aktualne zdjęcie</div>
                                </div>
                            <?php endif; ?>
                            <input type="file" class="form-control" id="photo" name="photo" accept="image/*">
                            <div class="form-text">Zalecane wymiary: 200x200 pikseli. Maksymalny rozmiar: 2MB. Pozostaw puste, aby zachować obecne zdjęcie.</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="active" name="active" value="1" 
                                       <?= $doctor['active'] ? 'checked' : '' ?>>
                                <label class="form-check-label" for="active">
                                    Lekarz aktywny
                                </label>
                                <div class="form-text">Tylko aktywni lekarze mogą logować się do systemu PWA.</div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="<?= UrlHelper::url('client/queue/doctors') ?>" class="btn btn-secondary me-md-2">Anuluj</a>
                            <button type="submit" class="btn btn-primary">Zapisz zmiany</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'views/partials/footer.php'; ?> 