<div class="content-wrapper">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Zarządzanie kampaniami</h1>
        <a href="/admin/add-campaign" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            Nowa kampania
        </a>
    </div>

    <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $_SESSION['flash_message'] ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <?php unset($_SESSION['flash_message']); ?>
    <?php endif; ?>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title">
                <i class="fas fa-bullhorn me-2"></i>
                Lista kampanii
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nazwa</th>
                            <th>Reklamodawca</th>
                            <th>Typ</th>
                            <th>Kategoria</th>
                            <th>Status</th>
                            <th>Wyświetlenia</th>
                            <th>Utworzono</th>
                            <th>Akcje</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($campaigns as $campaign): ?>
                        <tr>
                            <td><?= $campaign["id"] ?></td>
                            <td><?= htmlspecialchars($campaign["name"]) ?></td>
                            <td><?= htmlspecialchars($campaign["company_name"]) ?></td>
                            <td><?= $campaign["media_type"] ?></td>
                            <td><?= $campaign["category_name"] ? htmlspecialchars($campaign["category_name"]) : '<span class="text-muted">Brak</span>' ?></td>
                            <td>
                                <?php if ($campaign["is_active"]): ?>
                                    <span class="badge bg-success">Aktywna</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Nieaktywna</span>
                                <?php endif; ?>
                            </td>
                            <td><?= $campaign["views"] ?? 0 ?></td>
                            <td><?= date("d.m.Y", strtotime($campaign["created_at"])) ?></td>
                            <td>
                                <a href="/admin/edit-campaign/<?= $campaign["id"] ?>" class="btn btn-sm btn-primary">Edytuj</a>
                                <a href="/admin/delete-campaign/<?= $campaign["id"] ?>" class="btn btn-sm btn-danger">Usuń</a>
                                <a href="/admin/force-accept-campaign/<?= $campaign["id"] ?>" class="btn btn-sm btn-warning">
                                    <i class="fas fa-check-circle"></i> Wymuś akceptację
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
