<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edytuj użytkownika</h1>
        <a href="/admin/users" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Powrót
        </a>
    </div>

    <div class="row">
        <div class="col-xl-12 col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Formularz edycji użytkownika</h6>
                </div>
                <div class="card-body">
                    <?php if (isset($errors['general'])): ?>
                        <div class="alert alert-danger"><?= $errors['general'] ?></div>
                    <?php endif; ?>
                    
                    <form method="post" action="/admin/edit-user/<?= $user['id'] ?>">
                        <div class="form-group">
                            <label for="username">Nazwa użytkownika</label>
                            <input type="text" class="form-control" id="username" value="<?= $user['username'] ?>" readonly>
                            <small class="form-text text-muted">Nazwa użytkownika nie może być zmieniona.</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" class="form-control <?= isset($errors['email']) ? 'is-invalid' : '' ?>" 
                                id="email" name="email" value="<?= $formData['email'] ?? $user['email'] ?>">
                            <?php if (isset($errors['email'])): ?>
                                <div class="invalid-feedback"><?= $errors['email'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="form-group">
                            <label for="password">Hasło</label>
                            <input type="password" class="form-control <?= isset($errors['password']) ? 'is-invalid' : '' ?>" 
                                id="password" name="password">
                            <small class="form-text text-muted">Pozostaw puste, aby zachować aktualne hasło.</small>
                            <?php if (isset($errors['password'])): ?>
                                <div class="invalid-feedback"><?= $errors['password'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="form-group">
                            <label for="role">Rola</label>
                            <select class="form-control <?= isset($errors['role']) ? 'is-invalid' : '' ?>" 
                                id="role" name="role">
                                <?php if ($user['role'] == 'admin'): ?>
                                <option value="admin" selected>Administrator</option>
                                <?php else: ?>
                                <option value="client" selected>Klient</option>
                                <?php endif; ?>
                            </select>
                            <small class="form-text text-muted">Konto klienta pozwala zarówno tworzyć kampanie reklamowe, jak i wyświetlać reklamy innych firm.</small>
                            <?php if (isset($errors['role'])): ?>
                                <div class="invalid-feedback"><?= $errors['role'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="form-group">
                            <label for="company_name">Nazwa firmy</label>
                            <input type="text" class="form-control <?= isset($errors['company_name']) ? 'is-invalid' : '' ?>" 
                                id="company_name" name="company_name" value="<?= $formData['company_name'] ?? $user['company_name'] ?>">
                            <?php if (isset($errors['company_name'])): ?>
                                <div class="invalid-feedback"><?= $errors['company_name'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="form-group">
                            <label for="balance">Saldo</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">PLN</span>
                                </div>
                                <input type="number" step="0.01" min="0" class="form-control <?= isset($errors['balance']) ? 'is-invalid' : '' ?>" 
                                    id="balance" name="balance" value="<?= $formData['balance'] ?? $user['balance'] ?>">
                                <?php if (isset($errors['balance'])): ?>
                                    <div class="invalid-feedback"><?= $errors['balance'] ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" 
                                    <?= (isset($formData['is_active']) ? $formData['is_active'] : $user['is_active']) ? 'checked' : '' ?>>
                                <label class="custom-control-label" for="is_active">Aktywny</label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Zapisz zmiany</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div> 