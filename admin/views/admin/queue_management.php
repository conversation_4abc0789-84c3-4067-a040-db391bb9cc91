<?php require_once 'views/partials/header.php'; ?>

<div class="content-wrapper">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Zarządzanie systemem kolejkowym</h1>
        <a href="/admin" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            Powrót do panelu głównego
        </a>
    </div>

    <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <?= $_SESSION['flash_message'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            <?php unset($_SESSION['flash_message']); ?>
        </div>
    <?php endif; ?>

    <!-- Karty statystyk -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary h-100">
                <div class="card-body">
                    <div class="stats-card">
                        <div class="stats-icon primary">
                            <i class="fas fa-hospital"></i>
                        </div>
                        <div class="stats-info">
                            <h3><?= $stats['active_clients'] ?? 0 ?></h3>
                            <p>Aktywne przychodnie</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success h-100">
                <div class="card-body">
                    <div class="stats-card">
                        <div class="stats-icon success">
                            <i class="fas fa-door-open"></i>
                        </div>
                        <div class="stats-info">
                            <h3><?= $stats['total_rooms'] ?? 0 ?></h3>
                            <p>Wszystkie gabinety</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info h-100">
                <div class="card-body">
                    <div class="stats-card">
                        <div class="stats-icon info">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="stats-info">
                            <h3><?= $stats['appointments_today'] ?? 0 ?></h3>
                            <p>Dzisiejsze wizyty</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning h-100">
                <div class="card-body">
                    <div class="stats-card">
                        <div class="stats-icon warning">
                            <i class="fas fa-tv"></i>
                        </div>
                        <div class="stats-info">
                            <h3><?= $stats['active_displays'] ?? 0 ?></h3>
                            <p>Aktywne wyświetlacze</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtry i wyszukiwanie -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Filtrowanie i wyszukiwanie</h5>
        </div>
        <div class="card-body">
            <form action="/admin/queue-management" method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Wyszukaj przychodnię</label>
                    <input type="text" class="form-control" id="search" name="search" placeholder="Nazwa przychodni..."
                        value="<?= htmlspecialchars($_GET['search'] ?? '') ?>">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status systemu</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">Wszystkie</option>
                        <option value="1" <?= isset($_GET['status']) && $_GET['status'] == '1' ? 'selected' : '' ?>>Aktywne</option>
                        <option value="0" <?= isset($_GET['status']) && $_GET['status'] == '0' ? 'selected' : '' ?>>Nieaktywne</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="sort" class="form-label">Sortowanie</label>
                    <select class="form-select" id="sort" name="sort">
                        <option value="name_asc" <?= (isset($_GET['sort']) && $_GET['sort'] == 'name_asc') ? 'selected' : '' ?>>Nazwa (A-Z)</option>
                        <option value="name_desc" <?= (isset($_GET['sort']) && $_GET['sort'] == 'name_desc') ? 'selected' : '' ?>>Nazwa (Z-A)</option>
                        <option value="rooms_desc" <?= (isset($_GET['sort']) && $_GET['sort'] == 'rooms_desc') ? 'selected' : '' ?>>Najwięcej gabinetów</option>
                        <option value="activity_desc" <?= (isset($_GET['sort']) && $_GET['sort'] == 'activity_desc') ? 'selected' : '' ?>>Ostatnia aktywność</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>
                        Filtruj
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Lista przychodni -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Zarządzanie przychodniami</h5>
            <div>
                <button type="button" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#queueSettingsModal">
                    <i class="fas fa-cog me-1"></i>
                    Ustawienia globalne
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <?php if (empty($clients)): ?>
                <div class="alert alert-info m-3">
                    Brak przychodni w systemie lub żadna przychodnia nie spełnia kryteriów wyszukiwania.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Przychodnia</th>
                                <th>Gabinety</th>
                                <th>Wyświetlacze</th>
                                <th>Status</th>
                                <th>Dzisiejsze wizyty</th>
                                <th>Ostatnia aktywność</th>
                                <th>Akcje</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($clients as $client): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="status-indicator <?= $client['is_online'] ? 'bg-success' : 'bg-danger' ?> me-2"></div>
                                            <div>
                                                <div class="fw-bold"><?= htmlspecialchars($client['company_name']) ?></div>
                                                <small class="text-muted"><?= htmlspecialchars($client['email']) ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <a href="/admin/client-rooms/<?= $client['id'] ?>" class="badge bg-info text-decoration-none">
                                            <?= $client['rooms_count'] ?> <?= $client['rooms_count'] == 1 ? 'gabinet' : 'gabinety' ?>
                                        </a>
                                    </td>
                                    <td>
                                        <?php if ($client['displays_count'] > 0): ?>
                                            <span class="badge bg-success"><?= $client['displays_count'] ?> online</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Brak urządzeń</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($client['queue_enabled']): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check-circle me-1"></i>
                                                Włączony
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">
                                                <i class="fas fa-times-circle me-1"></i>
                                                Wyłączony
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?= $client['today_appointments'] ?? 0 ?>
                                        <span class="text-muted small">/</span>
                                        <?= $client['completed_appointments'] ?? 0 ?>
                                        <span class="text-muted small">zakończone</span>
                                    </td>
                                    <td>
                                        <?php if ($client['last_heartbeat']): ?>
                                            <div data-bs-toggle="tooltip" title="<?= date('d.m.Y H:i:s', strtotime($client['last_heartbeat'])) ?>">
                                                <?= $client['time_elapsed'] ?? date('d.m.Y H:i', strtotime($client['last_heartbeat'])) ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">Nigdy</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="d-flex">
                                            <a href="/admin/client-rooms/<?= $client['id'] ?>" class="btn btn-sm btn-info me-1" data-bs-toggle="tooltip" title="Zarządzaj gabinetami">
                                                <i class="fas fa-door-open"></i>
                                            </a>
                                            <form method="post" action="/admin/toggle-queue-system" class="d-inline">
                                                <input type="hidden" name="client_id" value="<?= $client['id'] ?>">
                                                <input type="hidden" name="is_enabled" value="<?= $client['queue_enabled'] ? '0' : '1' ?>">
                                                <button type="submit" class="btn btn-sm <?= $client['queue_enabled'] ? 'btn-danger' : 'btn-success' ?>" data-bs-toggle="tooltip"
                                                    title="<?= $client['queue_enabled'] ? 'Wyłącz system kolejkowy' : 'Włącz system kolejkowy' ?>">
                                                    <i class="fas fa-<?= $client['queue_enabled'] ? 'power-off' : 'play' ?>"></i>
                                                </button>
                                            </form>
                                            <button type="button" class="btn btn-sm btn-secondary ms-1" data-bs-toggle="modal"
                                                data-bs-target="#clientStatsModal"
                                                data-client-id="<?= $client['id'] ?>"
                                                data-client-name="<?= htmlspecialchars($client['company_name']) ?>"
                                                data-bs-toggle="tooltip" title="Statystyki">
                                                <i class="fas fa-chart-bar"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
        <div class="card-footer">
            <nav aria-label="Nawigacja stronicowania">
                <ul class="pagination justify-content-center mb-0">
                    <?php if ($pagination['current_page'] > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $pagination['current_page'] - 1 ?>&<?= $pagination['query_string'] ?>">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="page-item disabled">
                            <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                        </li>
                    <?php endif; ?>

                    <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                        <li class="page-item <?= $i == $pagination['current_page'] ? 'active' : '' ?>">
                            <a class="page-link" href="?page=<?= $i ?>&<?= $pagination['query_string'] ?>"><?= $i ?></a>
                        </li>
                    <?php endfor; ?>

                    <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $pagination['current_page'] + 1 ?>&<?= $pagination['query_string'] ?>">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="page-item disabled">
                            <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    </div>

    <!-- Informacje o systemie -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-info-circle me-2"></i>
                Informacje o systemie kolejkowym
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="fw-bold mb-3">System kolejkowy dla przychodni</h6>
                    <p>System kolejkowy pozwala przychodniom na zarządzanie kolejkami pacjentów w ich gabinetach, z możliwością:</p>
                    <ul>
                        <li>Dodawania, edytowania i usuwania gabinetów</li>
                        <li>Rejestracji wizyt na konkretne daty i godziny</li>
                        <li>Zarządzania aktualną kolejką pacjentów</li>
                        <li>Wyświetlania informacji o kolejkach na ekranach w poczekalni</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="fw-bold mb-3">Korzyści dla pacjentów</h6>
                    <p>System zapewnia pacjentom:</p>
                    <ul>
                        <li>Informacje o aktualnym statusie kolejek w gabinetach</li>
                        <li>Szacowany czas oczekiwania</li>
                        <li>Możliwość rejestracji na wizyty</li>
                        <li>Powiadomienia o zbliżającej się wizycie</li>
                    </ul>
                    <div class="alert alert-info mt-3">
                        <p class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>Wskazówka:</strong> System kolejkowy działa niezależnie od systemu reklamowego. Możliwe jest wyświetlanie reklam między informacjami o kolejkach, ale nie jest to wymagane.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal: Ustawienia globalne systemu kolejkowego -->
<div class="modal fade" id="queueSettingsModal" tabindex="-1" aria-labelledby="queueSettingsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="queueSettingsModalLabel">Globalne ustawienia systemu kolejkowego</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="/admin/update-queue-settings" method="post">
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="default_queue_mode" class="form-label">Domyślny tryb kolejkowy</label>
                            <select class="form-select" id="default_queue_mode" name="default_queue_mode">
                                <option value="scheduled" <?= $settings['default_queue_mode'] == 'scheduled' ? 'selected' : '' ?>>Zaplanowane wizyty</option>
                                <option value="walk_in" <?= $settings['default_queue_mode'] == 'walk_in' ? 'selected' : '' ?>>Wizyty bez rejestracji</option>
                                <option value="mixed" <?= $settings['default_queue_mode'] == 'mixed' ? 'selected' : '' ?>>Tryb mieszany</option>
                            </select>
                            <div class="form-text">Tryb domyślny dla nowych klientów</div>
                        </div>
                        <div class="col-md-6">
                            <label for="appointment_duration" class="form-label">Domyślny czas wizyty (min)</label>
                            <input type="number" class="form-control" id="appointment_duration" name="appointment_duration"
                                value="<?= $settings['appointment_duration'] ?? 15 ?>" min="5" max="120">
                            <div class="form-text">Domyślny czas trwania wizyty w minutach</div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="display_refresh_rate" class="form-label">Częstotliwość odświeżania (sek)</label>
                            <input type="number" class="form-control" id="display_refresh_rate" name="display_refresh_rate"
                                value="<?= $settings['display_refresh_rate'] ?? 30 ?>" min="10" max="300">
                            <div class="form-text">Jak często wyświetlacze powinny odświeżać dane</div>
                        </div>
                        <div class="col-md-6">
                            <label for="queue_per_page" class="form-label">Liczba wizyt na stronie</label>
                            <input type="number" class="form-control" id="queue_per_page" name="queue_per_page"
                                value="<?= $settings['queue_per_page'] ?? 10 ?>" min="5" max="50">
                            <div class="form-text">Liczba wizyt wyświetlanych na jednej stronie</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enable_queue_notifications" name="enable_queue_notifications"
                                    <?= isset($settings['enable_queue_notifications']) && $settings['enable_queue_notifications'] ? 'checked' : '' ?>>
                                <label class="form-check-label" for="enable_queue_notifications">Włącz powiadomienia o zmianach w kolejce</label>
                            </div>
                            <div class="form-text">Powiadomienia dźwiękowe na wyświetlaczach</div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="show_estimated_wait_time" name="show_estimated_wait_time"
                                    <?= isset($settings['show_estimated_wait_time']) && $settings['show_estimated_wait_time'] ? 'checked' : '' ?>>
                                <label class="form-check-label" for="show_estimated_wait_time">Pokaż szacowany czas oczekiwania</label>
                            </div>
                            <div class="form-text">Wyświetla szacowany czas oczekiwania dla pacjentów</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anuluj</button>
                    <button type="submit" class="btn btn-primary">Zapisz ustawienia</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal: Statystyki klienta -->
<div class="modal fade" id="clientStatsModal" tabindex="-1" aria-labelledby="clientStatsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="clientStatsModalLabel">Statystyki dla przychodni: <span id="client-name-placeholder"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center py-5" id="stats-loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Ładowanie...</span>
                    </div>
                    <p class="mt-2">Ładowanie statystyk...</p>
                </div>
                <div id="stats-content" class="d-none">
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-muted">Łączna liczba wizyt</h6>
                                    <h2 class="display-4 fw-bold" id="total-appointments">0</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-muted">Wizyty w tym miesiącu</h6>
                                    <h2 class="display-4 fw-bold" id="month-appointments">0</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-muted">Średni czas wizyty</h6>
                                    <h2 class="display-4 fw-bold" id="avg-appointment-time">0</h2>
                                    <p class="mb-0">minut</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-muted">Aktywne gabinety</h6>
                                    <h2 class="display-4 fw-bold" id="active-rooms">0</h2>
                                    <p class="mb-0">z <span id="total-rooms">0</span></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Wizyty w ostatnich 30 dniach</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="appointmentsChart" height="250"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Najpopularniejsze gabinety</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="roomsChart" height="250"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Zamknij</button>
                <a href="#" id="detailed-stats-link" class="btn btn-primary">Szczegółowe statystyki</a>
            </div>
        </div>
    </div>
</div>

<style>
/* Status indicator dla przychodni */
.status-indicator {
    height: 0.75rem;
    width: 0.75rem;
    border-radius: 50%;
    display: inline-block;
}

/* Style dla kart */
.border-left-primary {
    border-left: 4px solid #4e73df !important;
}
.border-left-success {
    border-left: 4px solid #1cc88a !important;
}
.border-left-info {
    border-left: 4px solid #36b9cc !important;
}
.border-left-warning {
    border-left: 4px solid #f6c23e !important;
}
</style>

<script>
// Funkcja do obsługi modalu statystyk
$(document).ready(function() {
    $('#clientStatsModal').on('show.bs.modal', function (event) {
        var button = $(event.relatedTarget);
        var clientId = button.data('client-id');
        var clientName = button.data('client-name');
        var modal = $(this);

        modal.find('#client-name-placeholder').text(clientName);
        modal.find('#detailed-stats-link').attr('href', '/admin/client-statistics/' + clientId);

        // Pokaż ładowanie, ukryj zawartość
        $('#stats-loading').removeClass('d-none');
        $('#stats-content').addClass('d-none');

        // Tutaj byłoby wywołanie AJAX do pobrania statystyk
        // Przykładowa implementacja:
        setTimeout(function() {
            // Symulacja ładowania danych (w rzeczywistej implementacji byłoby to pobierane z serwera)
            $('#total-appointments').text('245');
            $('#month-appointments').text('42');
            $('#avg-appointment-time').text('18');
            $('#active-rooms').text('4');
            $('#total-rooms').text('5');

            // Ukryj ładowanie, pokaż zawartość
            $('#stats-loading').addClass('d-none');
            $('#stats-content').removeClass('d-none');

            // Przykładowe wykresy (należy zaimplementować z wykorzystaniem Chart.js)
            // Kod dla wykresów byłby tutaj
        }, 1000);
    });

    // Inicjalizacja tooltipów
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Funkcja pomocnicza do wyświetlania czasu, który upłynął
// Funkcja pozostawiona dla kompatybilności z JavaScript
function timeElapsed(timestamp) {
    var now = new Date().getTime() / 1000;
    var diff = now - timestamp;

    if (diff < 60) {
        return 'przed chwilą';
    } else if (diff < 3600) {
        var minutes = Math.floor(diff / 60);
        return minutes + ' min. temu';
    } else if (diff < 86400) {
        var hours = Math.floor(diff / 3600);
        return hours + ' godz. temu';
    } else {
        var days = Math.floor(diff / 86400);
        return days + ' dni temu';
    }
}
</script>

<?php require_once 'views/partials/footer.php'; ?>
