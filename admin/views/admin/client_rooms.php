<?php require_once 'views/partials/header.php'; ?>

<div class="content-wrapper">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Gabinety przychodni: <?= htmlspecialchars($client['company_name']) ?></h1>
        <a href="/admin/queue-management" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            Powrót do zarządzania systemem kolejkowym
        </a>
    </div>

    <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <?= $_SESSION['flash_message'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            <?php unset($_SESSION['flash_message']); ?>
        </div>
    <?php endif; ?>

    <div class="row mb-4">
        <!-- <PERSON>rta informacji o przychodni -->
        <div class="col-xl-4 col-md-12 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-hospital me-2"></i>
                        Informacje o przychodni
                    </h5>
                    <span class="badge <?= $client['queue_enabled'] ? 'bg-success' : 'bg-danger' ?>">
                        <?= $client['queue_enabled'] ? 'System aktywny' : 'System nieaktywny' ?>
                    </span>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted d-block">Nazwa przychodni</small>
                        <div class="fw-bold"><?= htmlspecialchars($client['company_name']) ?></div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted d-block">Email kontaktowy</small>
                        <div><?= htmlspecialchars($client['email']) ?></div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted d-block">Login</small>
                        <div><?= htmlspecialchars($client['username']) ?></div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted d-block">Data rejestracji</small>
                        <div><?= date('d.m.Y H:i', strtotime($client['created_at'])) ?></div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted d-block">Ostatnia aktywność</small>
                        <div>
                            <?php if ($client['last_heartbeat']): ?>
                                <?= date('d.m.Y H:i:s', strtotime($client['last_heartbeat'])) ?>
                                <div class="badge <?= (time() - strtotime($client['last_heartbeat']) < 300) ? 'bg-success' : 'bg-danger' ?>">
                                    <?= (time() - strtotime($client['last_heartbeat']) < 300) ? 'Online' : 'Offline' ?>
                                </div>
                            <?php else: ?>
                                <span class="text-muted">Nigdy</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="mb-0">
                        <small class="text-muted d-block">Ustawienia systemu</small>
                        <div>
                            Tryb kolejkowy: <span class="fw-bold"><?= ucfirst($client['queue_mode'] ?? 'mieszany') ?></span>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <form method="post" action="/admin/toggle-queue-system" class="d-grid">
                        <input type="hidden" name="client_id" value="<?= $client['id'] ?>">
                        <input type="hidden" name="is_enabled" value="<?= $client['queue_enabled'] ? '0' : '1' ?>">
                        <button type="submit" class="btn <?= $client['queue_enabled'] ? 'btn-danger' : 'btn-success' ?>">
                            <i class="fas fa-<?= $client['queue_enabled'] ? 'power-off' : 'play' ?> me-2"></i>
                            <?= $client['queue_enabled'] ? 'Wyłącz system kolejkowy' : 'Włącz system kolejkowy' ?>
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Karta statystyk -->
        <div class="col-xl-8 col-md-12 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Statystyki systemu kolejkowego
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light h-100">
                                <div class="card-body text-center py-4">
                                    <h6 class="text-muted mb-2">Łączna liczba gabinetów</h6>
                                    <h2 class="display-4 fw-bold"><?= count($rooms) ?></h2>
                                    <small class="text-muted">
                                        <?= array_sum(array_column($rooms, 'active')) ?> aktywnych /
                                        <?= count($rooms) - array_sum(array_column($rooms, 'active')) ?> nieaktywnych
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="card bg-light h-100">
                                <div class="card-body text-center py-4">
                                    <h6 class="text-muted mb-2">Dzisiejsze wizyty</h6>
                                    <h2 class="display-4 fw-bold"><?= $stats['today_appointments'] ?? 0 ?></h2>
                                    <small class="text-muted">
                                        <?= $stats['completed_appointments'] ?? 0 ?> zakończonych /
                                        <?= $stats['waiting_appointments'] ?? 0 ?> oczekujących
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="card bg-light h-100">
                                <div class="card-body text-center py-4">
                                    <h6 class="text-muted mb-2">Aktywne wyświetlacze</h6>
                                    <h2 class="display-4 fw-bold"><?= $stats['active_displays'] ?? 0 ?></h2>
                                    <small class="text-muted">
                                        Ostatnie połączenie:
                                        <?php if (isset($stats['last_display_connection'])): ?>
                                            <?= date('H:i', strtotime($stats['last_display_connection'])) ?>
                                        <?php else: ?>
                                            --:--
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info mb-0 mt-2">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-info-circle fa-2x"></i>
                            </div>
                            <div>
                                <h6 class="alert-heading">Informacja</h6>
                                <p class="mb-0">Jako administrator możesz tylko przeglądać gabinety klienta. Zarządzanie gabinetami (dodawanie, edycja, usuwanie) jest dostępne tylko dla przychodni w ich panelu w sekcji System Kolejkowy.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Lista gabinetów -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-door-open me-2"></i>
                Gabinety
            </h5>
            <div>
                <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#appointmentsModal">
                    <i class="fas fa-calendar-alt me-1"></i>
                    Dzisiejsze wizyty
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <?php if (empty($rooms)): ?>
                <div class="alert alert-info m-3">
                    Przychodnia nie ma jeszcze dodanych gabinetów. Klient może dodać gabinety w swoim panelu w sekcji System Kolejkowy.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nazwa gabinetu</th>
                                <th>Opis</th>
                                <th>Lekarz / Personel</th>
                                <th>Status</th>
                                <th>Dzisiejsze wizyty</th>
                                <th>Data utworzenia</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($rooms as $room): ?>
                                <tr>
                                    <td><?= $room['id'] ?></td>
                                    <td>
                                        <div class="fw-bold"><?= htmlspecialchars($room['name']) ?></div>
                                        <?php if (!empty($room['room_number'])): ?>
                                            <small class="text-muted">Pokój <?= htmlspecialchars($room['room_number']) ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= htmlspecialchars($room['description'] ?: '—') ?></td>
                                    <td>
                                        <?php if (!empty($room['doctor_name'])): ?>
                                            <?= htmlspecialchars($room['doctor_name']) ?>
                                        <?php else: ?>
                                            <span class="text-muted">Nie przypisano</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($room['active']): ?>
                                            <span class="badge bg-success">Aktywny</span>
                                            <?php if ($room['current_queue_status'] == 'serving'): ?>
                                                <span class="badge bg-info">Obsługuje pacjentów</span>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Nieaktywny</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (isset($room['appointments_count'])): ?>
                                            <div class="d-flex align-items-center">
                                                <div class="me-2"><?= $room['appointments_count'] ?></div>
                                                <?php if ($room['appointments_count'] > 0): ?>
                                                    <button type="button" class="btn btn-sm btn-outline-info"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#appointmentsModal"
                                                            data-room-id="<?= $room['id'] ?>"
                                                            data-room-name="<?= htmlspecialchars($room['name']) ?>">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">0</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= date('d.m.Y H:i', strtotime($room['created_at'])) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Dodatkowe informacje -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-cog me-2"></i>
                Ustawienia systemu kolejkowego dla przychodni
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="fw-bold">Ustawienia systemu</h6>
                    <table class="table table-sm">
                        <tr>
                            <th>Tryb systemu:</th>
                            <td><?= ucfirst($client['queue_mode'] ?? 'mieszany') ?></td>
                        </tr>
                        <tr>
                            <th>Czas wyświetlania:</th>
                            <td><?= $client['display_time'] ?? '30' ?> sekund</td>
                        </tr>
                        <tr>
                            <th>Powiadomienia dźwiękowe:</th>
                            <td>
                                <?php if (isset($client['sound_notifications']) && $client['sound_notifications']): ?>
                                    <span class="badge bg-success">Włączone</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">Wyłączone</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Format numerów:</th>
                            <td><?= htmlspecialchars($client['number_format'] ?? 'Standard') ?></td>
                        </tr>
                    </table>
                </div>

                <div class="col-md-6">
                    <h6 class="fw-bold">Godziny pracy</h6>
                    <table class="table table-sm">
                        <tr>
                            <th>Dni robocze:</th>
                            <td><?= htmlspecialchars($client['working_days'] ?? 'Poniedziałek - Piątek') ?></td>
                        </tr>
                        <tr>
                            <th>Godziny otwarcia:</th>
                            <td><?= htmlspecialchars($client['working_hours'] ?? '08:00 - 18:00') ?></td>
                        </tr>
                        <tr>
                            <th>Czas na wizytę:</th>
                            <td><?= $client['appointment_duration'] ?? '15' ?> minut</td>
                        </tr>
                        <tr>
                            <th>Dni wolne:</th>
                            <td>
                                <?php if (!empty($client['holidays'])): ?>
                                    <?= htmlspecialchars($client['holidays']) ?>
                                <?php else: ?>
                                    <span class="text-muted">Nie określono</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal: Dzisiejsze wizyty -->
<div class="modal fade" id="appointmentsModal" tabindex="-1" aria-labelledby="appointmentsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="appointmentsModalLabel">Dzisiejsze wizyty</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center py-5" id="appointments-loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Ładowanie...</span>
                    </div>
                    <p class="mt-2">Ładowanie wizyt...</p>
                </div>

                <div id="appointments-content" class="d-none">
                    <div class="mb-3">
                        <div class="dropdown d-inline-block">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" id="roomFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-door-open me-1"></i>
                                <span id="selected-room-name">Wszystkie gabinety</span>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="roomFilterDropdown">
                                <li><a class="dropdown-item active" href="#" data-room-id="all">Wszystkie gabinety</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <?php foreach ($rooms as $room): ?>
                                    <li><a class="dropdown-item" href="#" data-room-id="<?= $room['id'] ?>"><?= htmlspecialchars($room['name']) ?></a></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>

                        <div class="btn-group ms-2">
                            <button type="button" class="btn btn-outline-secondary active" data-filter="all">Wszystkie</button>
                            <button type="button" class="btn btn-outline-secondary" data-filter="waiting">Oczekujące</button>
                            <button type="button" class="btn btn-outline-secondary" data-filter="serving">W trakcie</button>
                            <button type="button" class="btn btn-outline-secondary" data-filter="completed">Zakończone</button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover" id="appointments-table">
                            <thead>
                                <tr>
                                    <th>Nr</th>
                                    <th>Gabinet</th>
                                    <th>Pacjent</th>
                                    <th>Godzina</th>
                                    <th>Czas oczekiwania</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Dane będą ładowane dynamicznie przez JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <div id="no-appointments-message" class="alert alert-info d-none">
                        Brak wizyt dla wybranych kryteriów.
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Zamknij</button>
            </div>
        </div>
    </div>
</div>

<style>
/* Style dla kart */
.border-left-primary {
    border-left: 4px solid #4e73df !important;
}
.border-left-success {
    border-left: 4px solid #1cc88a !important;
}
.border-left-info {
    border-left: 4px solid #36b9cc !important;
}
.border-left-warning {
    border-left: 4px solid #f6c23e !important;
}

/* Status indicator */
.status-indicator {
    height: 0.75rem;
    width: 0.75rem;
    border-radius: 50%;
    display: inline-block;
}
</style>

<script>
// Obsługa modalu z wizytami
$(document).ready(function() {
    $('#appointmentsModal').on('show.bs.modal', function (event) {
        var button = $(event.relatedTarget);
        var roomId = button.data('room-id');
        var roomName = button.data('room-name');
        var modal = $(this);

        // Jeśli przekazano ID gabinetu, ustaw filtr na ten gabinet
        if (roomId) {
            modal.find('#selected-room-name').text(roomName);
            loadAppointments(roomId);
        } else {
            modal.find('#selected-room-name').text('Wszystkie gabinety');
            loadAppointments('all');
        }

        // Pokazuj ładowanie, ukryj zawartość
        $('#appointments-loading').removeClass('d-none');
        $('#appointments-content').addClass('d-none');
    });

    // Obsługa kliknięć w filtry gabinetów
    $(document).on('click', '.dropdown-menu a', function(e) {
        e.preventDefault();
        var roomId = $(this).data('room-id');
        var roomName = $(this).text();

        $('#selected-room-name').text(roomName);
        $('.dropdown-item').removeClass('active');
        $(this).addClass('active');

        loadAppointments(roomId);
    });

    // Obsługa kliknięć w filtry statusów
    $('.btn-group button').on('click', function() {
        $('.btn-group button').removeClass('active');
        $(this).addClass('active');

        var filter = $(this).data('filter');
        filterAppointments(filter);
    });

    // Funkcja ładująca wizyty z serwera
    function loadAppointments(roomId) {
        // Tutaj byłoby wywołanie AJAX do pobrania wizyt
        // Przykładowa implementacja:
        setTimeout(function() {
            // Symulacja ładowania danych (w rzeczywistej implementacji byłoby to pobierane z serwera)
            var appointments = [
                {id: 1, room_name: "Gabinet 1", patient_name: "Jan Kowalski", time: "09:00", wait_time: "0 min", status: "completed"},
                {id: 2, room_name: "Gabinet 1", patient_name: "Anna Nowak", time: "09:15", wait_time: "0 min", status: "completed"},
                {id: 3, room_name: "Gabinet 2", patient_name: "Piotr Wiśniewski", time: "10:00", wait_time: "0 min", status: "serving"},
                {id: 4, room_name: "Gabinet 1", patient_name: "Marek Kowalczyk", time: "10:30", wait_time: "10 min", status: "waiting"},
                {id: 5, room_name: "Gabinet 3", patient_name: "Katarzyna Zielińska", time: "11:00", wait_time: "30 min", status: "waiting"}
            ];

            // Filtruj wizyty po gabinecie jeśli trzeba
            if (roomId !== 'all') {
                // W prawdziwej implementacji filtracja byłaby po ID gabinetu
                // Tu używam nazwy gabinetu jako przykład
                appointments = appointments.filter(function(app) {
                    return app.room_name === "Gabinet " + roomId;
                });
            }

            // Wypełnij tabelę danymi
            var tableBody = $('#appointments-table tbody');
            tableBody.empty();

            if (appointments.length > 0) {
                appointments.forEach(function(app) {
                    var statusBadge;
                    if (app.status === 'completed') {
                        statusBadge = '<span class="badge bg-success">Zakończona</span>';
                    } else if (app.status === 'serving') {
                        statusBadge = '<span class="badge bg-primary">W trakcie</span>';
                    } else {
                        statusBadge = '<span class="badge bg-warning text-dark">Oczekująca</span>';
                    }

                    tableBody.append(`
                        <tr data-status="${app.status}">
                            <td>${app.id}</td>
                            <td>${app.room_name}</td>
                            <td>${app.patient_name}</td>
                            <td>${app.time}</td>
                            <td>${app.wait_time}</td>
                            <td>${statusBadge}</td>
                        </tr>
                    `);
                });

                $('#no-appointments-message').addClass('d-none');
            } else {
                $('#no-appointments-message').removeClass('d-none');
            }

            // Zastosuj aktywny filtr statusu
            var activeFilter = $('.btn-group button.active').data('filter');
            filterAppointments(activeFilter);

            // Ukryj ładowanie, pokaż zawartość
            $('#appointments-loading').addClass('d-none');
            $('#appointments-content').removeClass('d-none');
        }, 800);
    }

    // Funkcja filtrująca wizyty po statusie
    function filterAppointments(filter) {
        var rows = $('#appointments-table tbody tr');

        if (filter === 'all') {
            rows.show();
        } else {
            rows.hide();
            rows.filter('[data-status="' + filter + '"]').show();
        }

        // Pokaż/ukryj komunikat o braku wizyt
        if ($('#appointments-table tbody tr:visible').length === 0) {
            $('#no-appointments-message').removeClass('d-none');
        } else {
            $('#no-appointments-message').addClass('d-none');
        }
    }
});
</script>

<?php require_once 'views/partials/footer.php'; ?>
