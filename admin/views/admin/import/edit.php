<?php include 'views/layout.php'; ?>

<div class="content-wrapper">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-edit me-2"></i>
            Edytuj ustawienie importu
        </h1>
        <a href="<?= UrlHelper::url('import') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            Powrót do listy
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>
                        Konfiguracja importu - <?= htmlspecialchars($importSetting['system_name']) ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="client_id" class="form-label">Klient</label>
                                    <input type="text" class="form-control" value="<?= htmlspecialchars($importSetting['client_name'] ?? '') ?>" readonly>
                                    <div class="form-text">Klient nie może być zmieniony po utworzeniu</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="system_name" class="form-label">System *</label>
                                    <select class="form-select" id="system_name" name="system_name" required>
                                        <option value="">Wybierz system</option>
                                        <option value="igabinet" <?= $importSetting['system_name'] == 'igabinet' ? 'selected' : '' ?>>
                                            iGabinet.pl
                                        </option>
                                        <option value="medinet" <?= $importSetting['system_name'] == 'medinet' ? 'selected' : '' ?>>
                                            Medinet
                                        </option>
                                        <option value="other" <?= $importSetting['system_name'] == 'other' ? 'selected' : '' ?>>
                                            Inny
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sync_code" class="form-label">Kod synchronizacji *</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="sync_code" name="sync_code" 
                                               value="<?= htmlspecialchars($importSetting['sync_code']) ?>"
                                               placeholder="16-znakowy kod" maxlength="16" required>
                                        <button type="button" class="btn btn-outline-secondary" onclick="generateSyncCode()">
                                            <i class="fas fa-magic"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">
                                        Unikalny kod 16-znakowy używany do identyfikacji podczas synchronizacji
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sync_frequency" class="form-label">Częstotliwość synchronizacji</label>
                                    <select class="form-select" id="sync_frequency" name="sync_frequency">
                                        <option value="900" <?= $importSetting['sync_frequency'] == 900 ? 'selected' : '' ?>>
                                            Co 15 minut
                                        </option>
                                        <option value="1800" <?= $importSetting['sync_frequency'] == 1800 ? 'selected' : '' ?>>
                                            Co 30 minut
                                        </option>
                                        <option value="3600" <?= $importSetting['sync_frequency'] == 3600 ? 'selected' : '' ?>>
                                            Co godzinę
                                        </option>
                                        <option value="7200" <?= $importSetting['sync_frequency'] == 7200 ? 'selected' : '' ?>>
                                            Co 2 godziny
                                        </option>
                                        <option value="14400" <?= $importSetting['sync_frequency'] == 14400 ? 'selected' : '' ?>>
                                            Co 4 godziny
                                        </option>
                                        <option value="86400" <?= $importSetting['sync_frequency'] == 86400 ? 'selected' : '' ?>>
                                            Codziennie
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="api_endpoint" class="form-label">Endpoint API</label>
                            <input type="url" class="form-control" id="api_endpoint" name="api_endpoint" 
                                   value="<?= htmlspecialchars($importSetting['api_endpoint'] ?? '') ?>"
                                   placeholder="https://example.com/api/endpoint">
                            <div class="form-text">
                                Opcjonalny endpoint API dla systemu zewnętrznego
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       <?= $importSetting['is_active'] ? 'checked' : '' ?>>
                                <label class="form-check-label" for="is_active">
                                    Aktywne ustawienie importu
                                </label>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?= UrlHelper::url('import') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>
                                Anuluj
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Zaktualizuj ustawienie
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Informacje
                    </h6>
                </div>
                <div class="card-body">
                    <h6>Szczegóły ustawienia</h6>
                    <ul class="list-unstyled small text-muted">
                        <li><strong>Utworzone:</strong> <?= date('d.m.Y H:i', strtotime($importSetting['created_at'])) ?></li>
                        <?php if ($importSetting['updated_at'] && $importSetting['updated_at'] != $importSetting['created_at']): ?>
                            <li><strong>Ostatnia edycja:</strong> <?= date('d.m.Y H:i', strtotime($importSetting['updated_at'])) ?></li>
                        <?php endif; ?>
                        <?php if ($importSetting['last_sync']): ?>
                            <li><strong>Ostatnia synchronizacja:</strong> <?= date('d.m.Y H:i', strtotime($importSetting['last_sync'])) ?></li>
                        <?php endif; ?>
                    </ul>
                    
                    <hr>
                    
                    <h6>Dostępne akcje</h6>
                    <div class="d-grid gap-2">
                        <a href="<?= UrlHelper::url('doctor-mappings/' . $importSetting['id']) ?>" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-user-md me-2"></i>
                            Mapowania lekarzy
                        </a>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="testConnection()">
                            <i class="fas fa-plug me-2"></i>
                            Test połączenia
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function generateSyncCode() {
    fetch('<?= UrlHelper::url('generate-sync-code') ?>', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('sync_code').value = data.sync_code;
    })
    .catch(error => {
        console.error('Błąd podczas generowania kodu:', error);
        alert('Wystąpił błąd podczas generowania kodu synchronizacji.');
    });
}

function testConnection() {
    if (confirm('Czy chcesz przetestować połączenie z systemem zewnętrznym?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= UrlHelper::url('test-connection') ?>';
        
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'import_setting_id';
        input.value = '<?= $importSetting['id'] ?>';
        
        form.appendChild(input);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

