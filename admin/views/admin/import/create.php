<?php include 'views/layout.php'; ?>

<div class="content-wrapper">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-plus me-2"></i>
            Dodaj nowe ustawienie importu
        </h1>
        <a href="<?= UrlHelper::url('import') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            Powrót do listy
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>
                        Konfiguracja importu
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="client_id" class="form-label">Klient *</label>
                                    <select class="form-select" id="client_id" name="client_id" required>
                                        <option value="">Wybierz klienta</option>
                                        <?php foreach ($clients as $client): ?>
                                            <option value="<?= $client['id'] ?>" 
                                                    <?= isset($data['client_id']) && $data['client_id'] == $client['id'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($client['company_name']) ?> (<?= htmlspecialchars($client['username']) ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="system_name" class="form-label">System *</label>
                                    <select class="form-select" id="system_name" name="system_name" required>
                                        <option value="">Wybierz system</option>
                                        <option value="igabinet" <?= isset($data['system_name']) && $data['system_name'] == 'igabinet' ? 'selected' : '' ?>>
                                            iGabinet.pl
                                        </option>
                                        <option value="medinet" <?= isset($data['system_name']) && $data['system_name'] == 'medinet' ? 'selected' : '' ?>>
                                            Medinet
                                        </option>
                                        <option value="other" <?= isset($data['system_name']) && $data['system_name'] == 'other' ? 'selected' : '' ?>>
                                            Inny
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sync_code" class="form-label">Kod synchronizacji *</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="sync_code" name="sync_code" 
                                               value="<?= isset($data['sync_code']) ? htmlspecialchars($data['sync_code']) : '' ?>"
                                               placeholder="16-znakowy kod" maxlength="16" required>
                                        <button type="button" class="btn btn-outline-secondary" onclick="generateSyncCode()">
                                            <i class="fas fa-magic"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">
                                        Unikalny kod 16-znakowy używany do identyfikacji podczas synchronizacji
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sync_frequency" class="form-label">Częstotliwość synchronizacji</label>
                                    <select class="form-select" id="sync_frequency" name="sync_frequency">
                                        <option value="900" <?= isset($data['sync_frequency']) && $data['sync_frequency'] == 900 ? 'selected' : '' ?>>
                                            Co 15 minut
                                        </option>
                                        <option value="1800" <?= isset($data['sync_frequency']) && $data['sync_frequency'] == 1800 ? 'selected' : '' ?>>
                                            Co 30 minut
                                        </option>
                                        <option value="3600" <?= isset($data['sync_frequency']) && $data['sync_frequency'] == 3600 ? 'selected' : '' ?>>
                                            Co godzinę
                                        </option>
                                        <option value="7200" <?= isset($data['sync_frequency']) && $data['sync_frequency'] == 7200 ? 'selected' : '' ?>>
                                            Co 2 godziny
                                        </option>
                                        <option value="14400" <?= isset($data['sync_frequency']) && $data['sync_frequency'] == 14400 ? 'selected' : '' ?>>
                                            Co 4 godziny
                                        </option>
                                        <option value="86400" <?= isset($data['sync_frequency']) && $data['sync_frequency'] == 86400 ? 'selected' : '' ?>>
                                            Codziennie
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="api_endpoint" class="form-label">Endpoint API</label>
                            <input type="url" class="form-control" id="api_endpoint" name="api_endpoint" 
                                   value="<?= isset($data['api_endpoint']) ? htmlspecialchars($data['api_endpoint']) : '' ?>"
                                   placeholder="https://example.com/api/endpoint">
                            <div class="form-text">
                                Opcjonalny endpoint API dla systemu zewnętrznego
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       <?= !isset($data['is_active']) || $data['is_active'] ? 'checked' : '' ?>>
                                <label class="form-check-label" for="is_active">
                                    Aktywne ustawienie importu
                                </label>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?= UrlHelper::url('import') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>
                                Anuluj
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Zapisz ustawienie
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Informacje
                    </h6>
                </div>
                <div class="card-body">
                    <h6>System iGabinet.pl</h6>
                    <p class="small text-muted">
                        Synchronizacja z systemem iGabinet.pl pozwala na automatyczne pobieranie 
                        harmonogramów wizyt i mapowanie lekarzy.
                    </p>
                    
                    <h6>Kod synchronizacji</h6>
                    <p class="small text-muted">
                        Unikalny kod 16-znakowy używany przez dodatek Chrome do identyfikacji 
                        przychodni podczas synchronizacji danych.
                    </p>
                    
                    <h6>Mapowanie lekarzy</h6>
                    <p class="small text-muted">
                        Po utworzeniu ustawienia będziesz mógł skonfigurować mapowania 
                        między lekarzami z systemu zewnętrznego a lekarzami w Twoim systemie.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function generateSyncCode() {
    fetch('<?= UrlHelper::url('generate-sync-code') ?>', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('sync_code').value = data.sync_code;
    })
    .catch(error => {
        console.error('Błąd podczas generowania kodu:', error);
        alert('Wystąpił błąd podczas generowania kodu synchronizacji.');
    });
}

// Automatyczne generowanie kodu przy zmianie systemu
document.getElementById('system_name').addEventListener('change', function() {
    if (this.value === 'igabinet' && !document.getElementById('sync_code').value) {
        generateSyncCode();
    }
});
</script>

