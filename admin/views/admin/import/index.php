<?php include 'views/layout.php'; ?>

<div class="content-wrapper">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-download me-2"></i>
            Ustawienia importu
        </h1>
        <a href="<?= UrlHelper::url('create-import') ?>" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            Dodaj nowe ustawienie
        </a>
    </div>

    <?php if (empty($importSettings)): ?>
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-download fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">Brak ustawień importu</h4>
                <p class="text-muted"><PERSON><PERSON><PERSON> pierwsze ustawienie importu, aby r<PERSON><PERSON> synchronizację z systemami zewnętrznymi.</p>
                <a href="<?= UrlHelper::url('create-import') ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Dodaj ustawienie
                </a>
            </div>
        </div>
    <?php else: ?>
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    Lista ustawień importu
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Klient</th>
                                <th>System</th>
                                <th>Kod synchronizacji</th>
                                <th>Status</th>
                                <th>Ostatnia synchronizacja</th>
                                <th>Częstotliwość</th>
                                <th>Akcje</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($importSettings as $setting): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <?= strtoupper(substr($setting['client_name'], 0, 1)) ?>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?= htmlspecialchars($setting['client_name']) ?></div>
                                                <small class="text-muted"><?= htmlspecialchars($setting['client_username']) ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <i class="fas fa-plug me-1"></i>
                                            <?= htmlspecialchars(ucfirst($setting['system_name'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <code class="bg-light px-2 py-1 rounded"><?= htmlspecialchars($setting['sync_code']) ?></code>
                                            <button class="btn btn-sm btn-outline-secondary ms-2" 
                                                    onclick="copyToClipboard('<?= $setting['sync_code'] ?>')"
                                                    title="Kopiuj kod">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($setting['is_active']): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>
                                                Aktywny
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-pause me-1"></i>
                                                Nieaktywny
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($setting['last_sync']): ?>
                                            <span class="text-muted">
                                                <?= date('d.m.Y H:i', strtotime($setting['last_sync'])) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">Brak</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $frequency = $setting['sync_frequency'];
                                        if ($frequency < 60) {
                                            echo $frequency . 's';
                                        } elseif ($frequency < 3600) {
                                            echo floor($frequency / 60) . 'm';
                                        } else {
                                            echo floor($frequency / 3600) . 'h';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= UrlHelper::url('doctor-mappings/' . $setting['id']) ?>" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="Mapowania lekarzy">
                                                <i class="fas fa-user-md"></i>
                                            </a>
                                            <a href="<?= UrlHelper::url('edit-import/' . $setting['id']) ?>" 
                                               class="btn btn-sm btn-outline-secondary" 
                                               title="Edytuj">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    title="Usuń"
                                                    onclick="confirmDelete(<?= $setting['id'] ?>, '<?= htmlspecialchars($setting['client_name'] . ' - ' . $setting['system_name']) ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Pokaż powiadomienie o skopiowaniu
        const button = event.target.closest('button');
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.remove('btn-outline-secondary');
        button.classList.add('btn-success');
        
        setTimeout(() => {
            button.innerHTML = originalIcon;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    });
}

function confirmDelete(id, name) {
    if (confirm(`Czy na pewno chcesz usunąć ustawienie importu "${name}"?`)) {
        window.location.href = '<?= UrlHelper::url('delete-import/') ?>' + id;
    }
}
</script>

