<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Usuń użytkownika</h1>
        <a href="/admin/users" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Powrót
        </a>
    </div>

    <div class="row">
        <div class="col-xl-12 col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Potwierdzenie usunięcia</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle"></i> Uwaga!</h5>
                        <p>Czy na pewno chcesz usunąć użytkownika <strong><?= $user['username'] ?></strong> (<?= $user['company_name'] ?>)?</p>
                        <p>Ta operacja jest nieodwracalna i spowoduje usunięcie wszystkich danych związanych z tym użytkownikiem.</p>
                        <p>Jeśli użytkownik ma powiązane dane (kampanie, wyświetlenia, wyświetlacze), operacja nie powiedzie się.</p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    Dane użytkownika
                                </div>
                                <div class="card-body">
                                    <table class="table">
                                        <tr>
                                            <th>Nazwa użytkownika:</th>
                                            <td><?= $user['username'] ?></td>
                                        </tr>
                                        <tr>
                                            <th>Email:</th>
                                            <td><?= $user['email'] ?></td>
                                        </tr>
                                        <tr>
                                            <th>Rola:</th>
                                            <td>
                                                <?php if ($user['role'] == 'advertiser'): ?>
                                                    <span class="badge badge-primary">Reklamodawca</span>
                                                <?php elseif ($user['role'] == 'client'): ?>
                                                    <span class="badge badge-success">Reklamobiorca</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Firma:</th>
                                            <td><?= $user['company_name'] ?></td>
                                        </tr>
                                        <tr>
                                            <th>Saldo:</th>
                                            <td><?= number_format($user['balance'], 2) ?> PLN</td>
                                        </tr>
                                        <tr>
                                            <th>Status:</th>
                                            <td>
                                                <?php if ($user['is_active']): ?>
                                                    <span class="badge badge-success">Aktywny</span>
                                                <?php else: ?>
                                                    <span class="badge badge-danger">Nieaktywny</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <form method="post" action="/admin/delete-user/<?= $user['id'] ?>">
                        <div class="form-group">
                            <a href="/admin/users" class="btn btn-secondary">Anuluj</a>
                            <button type="submit" class="btn btn-danger">Usuń użytkownika</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div> 