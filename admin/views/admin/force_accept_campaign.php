<?php require_once 'views/partials/header.php'; ?>

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Wymu<PERSON> akceptację kampanii</h1>
        <a href="/admin/campaigns" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Powrót
        </a>
    </div>

    <div class="row">
        <div class="col-xl-12 col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Szczegóły kampanii</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Nazwa:</dt>
                                <dd class="col-sm-8"><?= htmlspecialchars($campaign['name']) ?></dd>
                                
                                <dt class="col-sm-4">Kategoria:</dt>
                                <dd class="col-sm-8">
                                    <?= $campaign['category_name'] ? htmlspecialchars($campaign['category_name']) : '<span class="text-muted">Brak</span>' ?>
                                </dd>
                                
                                <dt class="col-sm-4">Status:</dt>
                                <dd class="col-sm-8">
                                    <?php if ($campaign['is_active']): ?>
                                        <span class="badge bg-success">Aktywna</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Nieaktywna</span>
                                    <?php endif; ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Wymuś akceptację</h6>
                </div>
                <div class="card-body">
                    <form method="post" action="/admin/force-accept-campaign/<?= $campaign['id'] ?>">
                        <div class="form-group">
                            <div class="custom-control custom-checkbox mb-3">
                                <input type="checkbox" class="custom-control-input" id="force_all" name="force_all">
                                <label class="custom-control-label" for="force_all">
                                    <strong>Wymuś dla wszystkich klientów</strong>
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-group" id="client_selection">
                            <label>Lub wybierz konkretnych klientów:</label>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th width="50px">Wybierz</th>
                                            <th>Klient</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($clients as $client): ?>
                                            <tr>
                                                <td>
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input client-checkbox" 
                                                               id="client_<?= $client['id'] ?>" name="clients[]" value="<?= $client['id'] ?>">
                                                        <label class="custom-control-label" for="client_<?= $client['id'] ?>"></label>
                                                    </div>
                                                </td>
                                                <td><?= htmlspecialchars($client['company_name']) ?> (<?= $client['username'] ?>)</td>
                                                <td>
                                                    <?php if (isset($assignments[$client['id']])): ?>
                                                        <?php if ($assignments[$client['id']]['is_accepted'] == 1): ?>
                                                            <span class="badge bg-success">Zaakceptowana</span>
                                                        <?php elseif ($assignments[$client['id']]['is_accepted'] == 0): ?>
                                                            <span class="badge bg-danger">Odrzucona</span>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">Brak przypisania</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Wybierz akcję:</label>
                            <div class="custom-control custom-radio mb-2">
                                <input type="radio" id="force_accept" name="action" value="force_accept" class="custom-control-input" checked>
                                <label class="custom-control-label" for="force_accept">
                                    Wymuś akceptację tylko tej kampanii
                                </label>
                            </div>
                            
                            <?php if ($campaign['category_id']): ?>
                                <div class="custom-control custom-radio">
                                    <input type="radio" id="force_category" name="action" value="force_category" class="custom-control-input">
                                    <label class="custom-control-label" for="force_category">
                                        Wymuś akceptację wszystkich kampanii z kategorii <strong><?= htmlspecialchars($campaign['category_name']) ?></strong>
                                    </label>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Wymuś akceptację</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const forceAllCheckbox = document.getElementById('force_all');
        const clientSelection = document.getElementById('client_selection');
        const clientCheckboxes = document.querySelectorAll('.client-checkbox');
        
        forceAllCheckbox.addEventListener('change', function() {
            if (this.checked) {
                clientSelection.style.opacity = '0.5';
                clientCheckboxes.forEach(checkbox => {
                    checkbox.disabled = true;
                    checkbox.checked = false;
                });
            } else {
                clientSelection.style.opacity = '1';
                clientCheckboxes.forEach(checkbox => {
                    checkbox.disabled = false;
                });
            }
        });
    });
</script>

<?php require_once 'views/partials/footer.php'; ?> 