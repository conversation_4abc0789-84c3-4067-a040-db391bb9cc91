<?php require_once 'views/partials/header.php'; ?>

<div class="container-fluid">
    <h1 class="h3 mb-2 text-gray-800">Zarządzanie kategoriami</h1>
    
    <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="alert alert-success">
            <?= $_SESSION['flash_message'] ?>
            <?php unset($_SESSION['flash_message']); ?>
        </div>
    <?php endif; ?>
    
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Lista kategorii</h6>
            <a href="/admin/categories/create" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> Dodaj nową kategorię
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nazwa</th>
                            <th>Opis</th>
                            <th>Liczba kampanii</th>
                            <th>Data utworzenia</th>
                            <th>Akcje</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($categories as $category): ?>
                        <tr>
                            <td><?= $category['id'] ?></td>
                            <td><?= htmlspecialchars($category['name']) ?></td>
                            <td><?= htmlspecialchars($category['description']) ?></td>
                            <td><?= $category['campaign_count'] ?></td>
                            <td><?= date('d.m.Y H:i', strtotime($category['created_at'])) ?></td>
                            <td>
                                <a href="/admin/categories/edit/<?= $category['id'] ?>" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit"></i> Edytuj
                                </a>
                                <a href="/admin/categories/delete/<?= $category['id'] ?>" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i> Usuń
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php require_once 'views/partials/footer.php'; ?> 