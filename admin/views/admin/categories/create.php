<?php require_once 'views/partials/header.php'; ?>

<div class="container-fluid">
    <h1 class="h3 mb-2 text-gray-800"><PERSON>da<PERSON> nową kategor<PERSON>ę</h1>
    
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Nowa kategoria</h6>
        </div>
        <div class="card-body">
            <form action="/admin/categories/store" method="POST">
                <div class="form-group">
                    <label for="name">Nazwa kategorii</label>
                    <input type="text" class="form-control <?= isset($errors['name']) ? 'is-invalid' : '' ?>" 
                           id="name" name="name" value="<?= htmlspecialchars($formData['name'] ?? '') ?>">
                    <?php if (isset($errors['name'])): ?>
                        <div class="invalid-feedback"><?= $errors['name'] ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="description">Opis kategorii</label>
                    <textarea class="form-control" id="description" name="description" rows="3"><?= htmlspecialchars($formData['description'] ?? '') ?></textarea>
                </div>
                
                <div class="form-group">
                    <a href="/admin/categories" class="btn btn-secondary">Anuluj</a>
                    <button type="submit" class="btn btn-primary">Zapisz kategorię</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once 'views/partials/footer.php'; ?> 