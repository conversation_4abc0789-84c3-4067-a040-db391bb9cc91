<div class="content-wrapper">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Zarządzanie użytkownikami</h1>
        <a href="/admin/add-user" class="btn btn-primary">
            <i class="fas fa-user-plus me-2"></i>
            Dodaj użytkownika
        </a>
    </div>

    <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $_SESSION['flash_message'] ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <?php unset($_SESSION['flash_message']); ?>
    <?php endif; ?>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title">
                <i class="fas fa-users me-2"></i>
                Lista użytkowników
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nazwa użytkownika</th>
                            <th>Email</th>
                            <th>Rola</th>
                            <th>Firma</th>
                            <th>Saldo</th>
                            <th>Status</th>
                            <th>Akcje</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td><?= $user["id"] ?></td>
                            <td><?= htmlspecialchars($user["username"]) ?></td>
                            <td><?= htmlspecialchars($user["email"]) ?></td>
                            <td><?= ($user["role"] === "advertiser") ? "Reklamodawca" : "Reklamobiorca" ?></td>
                            <td><?= htmlspecialchars($user["company_name"]) ?></td>
                            <td><?= number_format($user["balance"], 2) ?> zł</td>
                            <td>
                                <?php if ($user["is_active"]): ?>
                                    <span class="badge bg-success">Aktywny</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Nieaktywny</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <a href="/admin/edit-user/<?= $user["id"] ?>" class="btn btn-sm btn-primary">Edytuj</a>
                                <a href="/admin/delete-user/<?= $user["id"] ?>" class="btn btn-sm btn-danger">Usuń</a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
