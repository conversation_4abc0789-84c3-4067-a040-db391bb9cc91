<div class="content-wrapper">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Panel Zarządzania Systemem</h1>
        <a href="<?= UrlHelper::url('queue-management') ?>" class="btn btn-primary">
            <i class="fas fa-cog me-2"></i>
            Zarządzaj systemem kolejkowym
        </a>
    </div>

    <!-- Sekcja systemu kolejkowego -->
    <section class="mb-5">
        <h2 class="section-title">
            <i class="fas fa-user-clock me-2"></i>
            System Kolejkowy
        </h2>
        <div class="row mb-4">
            <!-- Karty statystyk kolejkowych -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary h-100">
                    <div class="card-body">
                        <div class="stats-card">
                            <div class="stats-icon primary">
                                <i class="fas fa-hospital"></i>
                            </div>
                            <div class="stats-info">
                                <h3><?= $stats['users']['client'] ?? 0 ?></h3>
                                <p>Aktywne przychodnie</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success h-100">
                    <div class="card-body">
                        <div class="stats-card">
                            <div class="stats-icon success">
                                <i class="fas fa-door-open"></i>
                            </div>
                            <div class="stats-info">
                                <h3><?= $roomCount ?? 0 ?></h3>
                                <p>Wszystkie gabinety</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info h-100">
                    <div class="card-body">
                        <div class="stats-card">
                            <div class="stats-icon info">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="stats-info">
                                <h3><?= $todayAppointments ?? 0 ?></h3>
                                <p>Dzisiejsze wizyty</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning h-100">
                    <div class="card-body">
                        <div class="stats-card">
                            <div class="stats-icon warning">
                                <i class="fas fa-tv"></i>
                            </div>
                            <div class="stats-info">
                                <h3><?= $activeDisplays ?? 0 ?></h3>
                                <p>Aktywne wyświetlacze</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status przychodni online -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clinic-medical me-2"></i>
                    Status przychodni
                </h5>
                <a href="<?= UrlHelper::url('queue-management') ?>" class="btn btn-sm btn-outline-primary">
                    Szczegółowe zarządzanie
                </a>
            </div>
            <div class="card-body p-0">
                <?php if (empty($clientStatus)): ?>
                    <p class="p-3 text-muted">Brak zarejestrowanych przychodni</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Przychodnia</th>
                                    <th>Gabinety</th>
                                    <th>Wyświetlacze</th>
                                    <th>Status systemu</th>
                                    <th>Dzisiejsze wizyty</th>
                                    <th>Akcje</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($clientStatus as $client): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="status-indicator <?= $client['is_online'] ? 'bg-success' : 'bg-danger' ?> me-2"></div>
                                                <?= htmlspecialchars($client['company_name']) ?>
                                            </div>
                                        </td>
                                        <td><?= $client['rooms_count'] ?? 0 ?></td>
                                        <td>
                                            <?php if (isset($client['displays_count']) && $client['displays_count'] > 0): ?>
                                                <span class="badge bg-success"><?= $client['displays_count'] ?> online</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Brak urządzeń</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if (isset($client['queue_enabled']) && $client['queue_enabled']): ?>
                                                <span class="badge bg-success">Włączony</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Wyłączony</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= $client['today_appointments'] ?? 0 ?></td>
                                        <td>
                                            <a href="<?= UrlHelper::url('client-rooms/' . $client['id']) ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-door-open me-1"></i> Gabinety
                                            </a>
                                            <form method="post" action="/admin/toggle-queue-system" class="d-inline ms-1">
                                                <input type="hidden" name="client_id" value="<?= $client['id'] ?>">
                                                <input type="hidden" name="is_enabled" value="<?= isset($client['queue_enabled']) && $client['queue_enabled'] ? '0' : '1' ?>">
                                                <button type="submit" class="btn btn-sm <?= isset($client['queue_enabled']) && $client['queue_enabled'] ? 'btn-danger' : 'btn-success' ?>">
                                                    <?= isset($client['queue_enabled']) && $client['queue_enabled'] ? 'Wyłącz' : 'Włącz' ?>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Sekcja systemu reklamowego -->
    <section>
        <h2 class="section-title">
            <i class="fas fa-ad me-2"></i>
            System Reklamowy (Dodatek)
        </h2>

        <div class="row mb-4">
            <!-- Karty statystyk reklamowych -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-secondary h-100">
                    <div class="card-body">
                        <div class="stats-card">
                            <div class="stats-icon secondary">
                                <i class="fas fa-bullhorn"></i>
                            </div>
                            <div class="stats-info">
                                <h3><?= $stats['users']['advertiser'] ?? 0 ?></h3>
                                <p>Reklamodawcy</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-secondary h-100">
                    <div class="card-body">
                        <div class="stats-card">
                            <div class="stats-icon secondary">
                                <i class="fas fa-ad"></i>
                            </div>
                            <div class="stats-info">
                                <h3><?= $stats['campaigns'] ?? 0 ?></h3>
                                <p>Kampanie reklamowe</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-secondary h-100">
                    <div class="card-body">
                        <div class="stats-card">
                            <div class="stats-icon secondary">
                                <i class="fas fa-eye"></i>
                            </div>
                            <div class="stats-info">
                                <h3><?= $stats['views'] ?? 0 ?></h3>
                                <p>Wyświetlenia reklam</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-secondary h-100">
                    <div class="card-body">
                        <div class="stats-card">
                            <div class="stats-icon secondary">
                                <i class="fas fa-coins"></i>
                            </div>
                            <div class="stats-info">
                                <h3><?= number_format($stats['revenue'] ?? 0, 2) ?> zł</h3>
                                <p>Przychody z reklam</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Skrót do zarządzania kampaniami -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-ad me-2"></i>
                    Zarządzanie reklamami
                </h5>
                <div>
                    <a href="<?= UrlHelper::url('campaigns') ?>" class="btn btn-sm btn-outline-secondary me-2">
                        <i class="fas fa-list me-1"></i> Kampanie
                    </a>
                    <a href="<?= UrlHelper::url('add-campaign') ?>" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-plus me-1"></i> Nowa kampania
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <p class="mb-2">Kampanie oczekujące na akceptację: <strong><?= $stats['pending_campaigns'] ?? 0 ?></strong></p>
                        <p class="mb-2">Aktywne kampanie: <strong><?= $stats['active_campaigns'] ?? 0 ?></strong></p>
                        <p class="mb-0">Wstrzymane kampanie: <strong><?= $stats['paused_campaigns'] ?? 0 ?></strong></p>
                    </div>
                    <div class="col-md-4">
                        <div class="alert alert-info mb-0">
                            <small>System reklamowy działa jako dodatek do systemu kolejkowego. Reklamy mogą być wyświetlane pomiędzy informacjami o kolejkach.</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
/* Styl dla sekcji */
.section-title {
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #e3e6f0;
    font-size: 1.25rem;
    color: #4e73df;
}

/* Style dla kart */
.border-left-primary {
    border-left: 4px solid #4e73df !important;
}
.border-left-success {
    border-left: 4px solid #1cc88a !important;
}
.border-left-info {
    border-left: 4px solid #36b9cc !important;
}
.border-left-warning {
    border-left: 4px solid #f6c23e !important;
}
.border-secondary {
    border: 1px solid #e3e6f0 !important;
    border-left: 4px solid #858796 !important;
}

/* Status indicator dla przychodni */
.status-indicator {
    height: 0.75rem;
    width: 0.75rem;
    border-radius: 50%;
    display: inline-block;
}
</style>
