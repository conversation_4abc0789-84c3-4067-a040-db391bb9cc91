<?php /* Plik widoku dla dodawania kampanii */ ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800"><PERSON>daj kampani<PERSON></h1>
        <a href="/admin/campaigns" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Powrót
        </a>
    </div>

    <div class="row">
        <div class="col-xl-12 col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Formularz dodawania kampanii</h6>
                </div>
                <div class="card-body">
                    <?php if (isset($errors['general'])): ?>
                        <div class="alert alert-danger"><?= $errors['general'] ?></div>
                    <?php endif; ?>
                    
                    <form method="post" action="/admin/add-campaign" enctype="multipart/form-data">
                        <div class="form-group">
                            <label for="advertiser_id">Reklamodawca</label>
                            <select class="form-control <?= isset($errors['advertiser_id']) ? 'is-invalid' : '' ?>" 
                                id="advertiser_id" name="advertiser_id">
                                <option value="">Wybierz reklamodawcę</option>
                                <?php foreach ($advertisers as $advertiser): ?>
                                    <option value="<?= $advertiser['id'] ?>" <?= (isset($formData['advertiser_id']) && $formData['advertiser_id'] == $advertiser['id']) ? 'selected' : '' ?>>
                                        <?= $advertiser['company_name'] ?> (<?= $advertiser['username'] ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <?php if (isset($errors['advertiser_id'])): ?>
                                <div class="invalid-feedback"><?= $errors['advertiser_id'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="form-group">
                            <label for="name">Nazwa kampanii</label>
                            <input type="text" class="form-control <?= isset($errors['name']) ? 'is-invalid' : '' ?>" 
                                id="name" name="name" value="<?= $formData['name'] ?? '' ?>">
                            <?php if (isset($errors['name'])): ?>
                                <div class="invalid-feedback"><?= $errors['name'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="form-group">
                            <label for="description">Opis</label>
                            <textarea class="form-control <?= isset($errors['description']) ? 'is-invalid' : '' ?>" 
                                id="description" name="description" rows="3"><?= $formData['description'] ?? '' ?></textarea>
                            <?php if (isset($errors['description'])): ?>
                                <div class="invalid-feedback"><?= $errors['description'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="form-group">
                            <label for="category_id">Kategoria</label>
                            <select class="form-control <?= isset($errors['category_id']) ? 'is-invalid' : '' ?>" 
                                id="category_id" name="category_id">
                                <option value="">Wybierz kategorię</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['id'] ?>" <?= (isset($formData['category_id']) && $formData['category_id'] == $category['id']) ? 'selected' : '' ?>>
                                        <?= $category['name'] ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <?php if (isset($errors['category_id'])): ?>
                                <div class="invalid-feedback"><?= $errors['category_id'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="form-group">
                            <label for="media_type">Typ mediów</label>
                            <select class="form-control <?= isset($errors['media_type']) ? 'is-invalid' : '' ?>" 
                                id="media_type" name="media_type">
                                <option value="">Wybierz typ mediów</option>
                                <option value="image" <?= (isset($formData['media_type']) && $formData['media_type'] == 'image') ? 'selected' : '' ?>>
                                    Obraz
                                </option>
                                <option value="video" <?= (isset($formData['media_type']) && $formData['media_type'] == 'video') ? 'selected' : '' ?>>
                                    Wideo
                                </option>
                                <option value="youtube" <?= (isset($formData['media_type']) && $formData['media_type'] == 'youtube') ? 'selected' : '' ?>>
                                    YouTube
                                </option>
                            </select>
                            <?php if (isset($errors['media_type'])): ?>
                                <div class="invalid-feedback"><?= $errors['media_type'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="form-group media-upload" id="media-file-group">
                            <label for="media_file">Plik mediów</label>
                            <input type="file" class="form-control-file <?= isset($errors['media_file']) ? 'is-invalid' : '' ?>" 
                                id="media_file" name="media_file">
                            <small class="form-text text-muted">
                                Dozwolone formaty: JPG, PNG, GIF (dla obrazów) lub MP4, WEBM, OGG (dla wideo)
                            </small>
                            <?php if (isset($errors['media_file'])): ?>
                                <div class="invalid-feedback"><?= $errors['media_file'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="form-group media-upload" id="youtube-group" style="display: none;">
                            <label for="youtube_url">Link do YouTube</label>
                            <input type="text" class="form-control <?= isset($errors['youtube_url']) ? 'is-invalid' : '' ?>" 
                                id="youtube_url" name="youtube_url" value="<?= $formData['youtube_url'] ?? '' ?>"
                                placeholder="np. https://www.youtube.com/watch?v=dQw4w9WgXcQ">
                            <?php if (isset($errors['youtube_url'])): ?>
                                <div class="invalid-feedback"><?= $errors['youtube_url'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="form-group">
                            <label for="duration">Czas trwania (sekundy)</label>
                            <input type="number" class="form-control <?= isset($errors['duration']) ? 'is-invalid' : '' ?>" 
                                id="duration" name="duration" value="<?= $formData['duration'] ?? '30' ?>" min="5" max="120">
                            <?php if (isset($errors['duration'])): ?>
                                <div class="invalid-feedback"><?= $errors['duration'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="form-group">
                            <label for="budget">Budżet</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">PLN</span>
                                </div>
                                <input type="number" step="0.01" min="0" class="form-control <?= isset($errors['budget']) ? 'is-invalid' : '' ?>" 
                                    id="budget" name="budget" value="<?= $formData['budget'] ?? '100.00' ?>">
                                <?php if (isset($errors['budget'])): ?>
                                    <div class="invalid-feedback"><?= $errors['budget'] ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="max_frequency_per_hour">Maksymalna częstotliwość na godzinę</label>
                            <input type="number" class="form-control <?= isset($errors['max_frequency_per_hour']) ? 'is-invalid' : '' ?>" 
                                id="max_frequency_per_hour" name="max_frequency_per_hour" value="<?= $formData['max_frequency_per_hour'] ?? '0' ?>" min="0" max="20">
                            <small class="form-text text-muted">
                                Maksymalna liczba wyświetleń reklamy w ciągu godziny (0 = bez ograniczeń)
                            </small>
                            <?php if (isset($errors['max_frequency_per_hour'])): ?>
                                <div class="invalid-feedback"><?= $errors['max_frequency_per_hour'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="start_date">Data rozpoczęcia</label>
                                    <input type="date" class="form-control <?= isset($errors['start_date']) ? 'is-invalid' : '' ?>" 
                                        id="start_date" name="start_date" value="<?= $formData['start_date'] ?? date('Y-m-d') ?>">
                                    <?php if (isset($errors['start_date'])): ?>
                                        <div class="invalid-feedback"><?= $errors['start_date'] ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="end_date">Data zakończenia</label>
                                    <input type="date" class="form-control <?= isset($errors['end_date']) ? 'is-invalid' : '' ?>" 
                                        id="end_date" name="end_date" value="<?= $formData['end_date'] ?? date('Y-m-d', strtotime('+30 days')) ?>">
                                    <?php if (isset($errors['end_date'])): ?>
                                        <div class="invalid-feedback"><?= $errors['end_date'] ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" 
                                    <?= (!isset($formData) || isset($formData['is_active'])) ? 'checked' : '' ?>>
                                <label class="custom-control-label" for="is_active">Aktywna</label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Dodaj kampanię</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const mediaTypeSelect = document.getElementById('media_type');
    const mediaFileGroup = document.getElementById('media-file-group');
    const youtubeGroup = document.getElementById('youtube-group');
    
    function updateMediaFields() {
        const selectedType = mediaTypeSelect.value;
        
        if (selectedType === 'youtube') {
            mediaFileGroup.style.display = 'none';
            youtubeGroup.style.display = 'block';
        } else if (selectedType === 'image' || selectedType === 'video') {
            mediaFileGroup.style.display = 'block';
            youtubeGroup.style.display = 'none';
        } else {
            mediaFileGroup.style.display = 'none';
            youtubeGroup.style.display = 'none';
        }
    }
    
    mediaTypeSelect.addEventListener('change', updateMediaFields);
    updateMediaFields(); // Wywołaj przy załadowaniu strony
});
</script>
