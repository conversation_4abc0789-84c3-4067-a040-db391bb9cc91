<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800"><PERSON><PERSON><PERSON> kampanię</h1>
        <a href="/admin/campaigns" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Powrót
        </a>
    </div>

    <div class="row">
        <div class="col-xl-12 col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Potwierdzenie usunięcia</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle"></i> Uwaga!</h5>
                        <p>Czy na pewno chcesz usunąć kampanię <strong><?= $campaign['name'] ?></strong>?</p>
                        <p>Ta operacja jest nieodwracalna i spowoduje usunięcie wszystkich danych związanych z tą kampanią.</p>
                        <p>Jeśli kampania ma powiązane wyświetlenia, operacja nie powiedzie się. W takim przypadku możesz dezaktywować kampanię zamiast ją usuwać.</p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    Dane kampanii
                                </div>
                                <div class="card-body">
                                    <table class="table">
                                        <tr>
                                            <th>Nazwa:</th>
                                            <td><?= $campaign['name'] ?></td>
                                        </tr>
                                        <tr>
                                            <th>Reklamodawca:</th>
                                            <td><?= $campaign['advertiser_name'] ?> (<?= $campaign['company_name'] ?>)</td>
                                        </tr>
                                        <tr>
                                            <th>Typ mediów:</th>
                                            <td>
                                                <?php if ($campaign['media_type'] == 'image'): ?>
                                                    <span class="badge badge-info">Obraz</span>
                                                <?php elseif ($campaign['media_type'] == 'video'): ?>
                                                    <span class="badge badge-primary">Wideo</span>
                                                <?php elseif ($campaign['media_type'] == 'youtube'): ?>
                                                    <span class="badge badge-danger">YouTube</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Budżet:</th>
                                            <td><?= number_format($campaign['budget'], 2) ?> PLN</td>
                                        </tr>
                                        <tr>
                                            <th>Wydano:</th>
                                            <td><?= number_format($campaign['spent'], 2) ?> PLN</td>
                                        </tr>
                                        <tr>
                                            <th>Okres:</th>
                                            <td><?= date('d.m.Y', strtotime($campaign['start_date'])) ?> - <?= date('d.m.Y', strtotime($campaign['end_date'])) ?></td>
                                        </tr>
                                        <tr>
                                            <th>Status:</th>
                                            <td>
                                                <?php if ($campaign['is_active']): ?>
                                                    <span class="badge badge-success">Aktywna</span>
                                                <?php else: ?>
                                                    <span class="badge badge-danger">Nieaktywna</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    Podgląd mediów
                                </div>
                                <div class="card-body text-center">
                                    <?php if ($campaign['media_type'] == 'image'): ?>
                                        <img src="<?= $campaign['media_url'] ?>" alt="Obraz kampanii" style="max-width: 100%; max-height: 200px;" class="img-thumbnail">
                                    <?php elseif ($campaign['media_type'] == 'video'): ?>
                                        <video controls style="max-width: 100%; max-height: 200px;">
                                            <source src="<?= $campaign['media_url'] ?>" type="video/mp4">
                                            Twoja przeglądarka nie obsługuje odtwarzania wideo.
                                        </video>
                                    <?php elseif ($campaign['media_type'] == 'youtube'): ?>
                                        <div class="embed-responsive embed-responsive-16by9">
                                            <iframe class="embed-responsive-item" src="<?= $campaign['media_url'] ?>" allowfullscreen></iframe>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <form method="post" action="/admin/delete-campaign/<?= $campaign['id'] ?>">
                        <div class="form-group">
                            <a href="/admin/campaigns" class="btn btn-secondary">Anuluj</a>
                            <button type="submit" class="btn btn-danger">Usuń kampanię</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
