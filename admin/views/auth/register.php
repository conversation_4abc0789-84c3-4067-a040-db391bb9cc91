<?php if (isset($errors)): ?>
    <div class="alert alert-danger">
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
                <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-body">
        <h5 class="card-title text-center">Rejestracja</h5>
        
        <form method="post" action="/admin/register">
            <div class="mb-3">
                <label for="username" class="form-label">Nazwa użytkownika</label>
                <input type="text" class="form-control" id="username" name="username" 
                       value="<?= htmlspecialchars($data['username'] ?? '') ?>" required>
            </div>
            
            <div class="mb-3">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" name="email" 
                       value="<?= htmlspecialchars($data['email'] ?? '') ?>" required>
            </div>
            
            <div class="mb-3">
                <label for="company_name" class="form-label">Nazwa firmy</label>
                <input type="text" class="form-control" id="company_name" name="company_name" 
                       value="<?= htmlspecialchars($data['company_name'] ?? '') ?>" required>
            </div>
            
            <div class="mb-3">
                <label for="role" class="form-label">Typ konta</label>
                <select class="form-control" id="role" name="role" required>
                    <option value="client" selected>Klient</option>
                </select>
                <small class="form-text text-muted">Konto klienta pozwala zarówno tworzyć kampanie reklamowe, jak i wyświetlać reklamy innych firm.</small>
            </div>
            
            <div class="mb-3">
                <label for="password" class="form-label">Hasło</label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>
            
            <div class="mb-3">
                <label for="confirm_password" class="form-label">Potwierdź hasło</label>
                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
            </div>
            
            <div class="d-grid">
                <button type="submit" class="btn btn-primary">Zarejestruj się</button>
            </div>
        </form>
        
        <div class="text-center mt-3">
            <small><a href="/admin/login">Masz już konto? Zaloguj się</a></small>
        </div>
    </div>
</div> 