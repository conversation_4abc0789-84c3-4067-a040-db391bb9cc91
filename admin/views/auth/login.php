<?php if (isset($error)): ?>
    <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
<?php endif; ?>

<?php if (isset($success)): ?>
    <div class="alert alert-success"><?= htmlspecialchars($success) ?></div>
<?php endif; ?>

<div class="card">
    <div class="card-body">
        <h5 class="card-title text-center">Logowanie</h5>
        
        <form method="post" action="/admin/login">
            <div class="mb-3">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" name="email" required>
            </div>
            
            <div class="mb-3">
                <label for="password" class="form-label">Hasło</label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>
            
            <div class="d-grid">
                <button type="submit" class="btn btn-primary">Zaloguj się</button>
            </div>
        </form>
        
        <div class="text-center mt-3">
            <small><a href="/admin/register">Nie masz konta? Zarejestruj się</a></small>
        </div>
    </div>
</div>

<div class="mt-3">
    <div class="card">
        <div class="card-body">
            <h6 class="card-title">Dane testowe</h6>
            <small class="text-muted">
                <strong>Administrator:</strong><br>
                Email: <EMAIL><br>
                Hasło: admin123
            </small>
        </div>
    </div>
</div> 