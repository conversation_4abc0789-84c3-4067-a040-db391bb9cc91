<div class="container py-4">
    <h2 class="mb-4"><i class="fas fa-user-circle me-2"></i>Mój profil</h2>

    <?php if (!empty($_SESSION['flash_message'])): ?>
        <div class="alert alert-success"><?= htmlspecialchars($_SESSION['flash_message']) ?></div>
        <?php unset($_SESSION['flash_message']); ?>
    <?php endif; ?>

    <?php if (!empty($errors)): ?>
        <div class="alert alert-danger">
            <ul class="mb-0">
                <?php foreach ($errors as $err): ?>
                    <li><?= htmlspecialchars($err) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-body">
            <form method="post" action="<?= UrlHelper::url('profile') ?>">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">Nazwa użytkownika</label>
                        <input type="text" name="username" class="form-control" value="<?= htmlspecialchars($user['username'] ?? '') ?>" required>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Email</label>
                        <input type="email" name="email" class="form-control" value="<?= htmlspecialchars($user['email'] ?? '') ?>" required>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Nazwa firmy</label>
                        <input type="text" name="company_name" class="form-control" value="<?= htmlspecialchars($user['company_name'] ?? '') ?>">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Nowe hasło</label>
                        <input type="password" name="password" class="form-control" placeholder="(opcjonalnie)">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Powtórz hasło</label>
                        <input type="password" name="password_confirm" class="form-control" placeholder="(opcjonalnie)">
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary"><i class="fas fa-save me-2"></i>Zapisz</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>


