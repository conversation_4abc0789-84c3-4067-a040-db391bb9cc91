<div class="container py-4">
    <h2 class="mb-4"><i class="fas fa-cog me-2"></i>Ustawienia</h2>

    <?php if (!empty($_SESSION['flash_message'])): ?>
        <div class="alert alert-success"><?= htmlspecialchars($_SESSION['flash_message']) ?></div>
        <?php unset($_SESSION['flash_message']); ?>
    <?php endif; ?>

    <div class="card">
        <div class="card-body">
            <form method="post" action="<?= UrlHelper::url('settings') ?>">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">Język interfejsu</label>
                        <select name="language" class="form-select">
                            <option value="pl" <?= ($language ?? 'pl') === 'pl' ? 'selected' : '' ?>>Polski</option>
                            <option value="en" <?= ($language ?? 'pl') === 'en' ? 'selected' : '' ?>>English</option>
                        </select>
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="dark_mode" id="dark_mode" <?= !empty($dark_mode) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="dark_mode">Tryb ciemny</label>
                        </div>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary"><i class="fas fa-save me-2"></i>Zapisz</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Sekcja ustawień importu -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-download me-2"></i> Ustawienia importu z systemów zewnętrznych
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <p class="mb-3">Skonfiguruj import danych z zewnętrznych systemów medycznych (np. iGabinet.pl) do automatycznej synchronizacji harmonogramów wizyt.</p>
                    
                    <?php
                    // Pobierz ustawienia importu dla tego użytkownika (jeśli jest klientem)
                    if (isset($user['role']) && $user['role'] === 'client') {
                        require_once 'models/ImportSetting.php';
                        $importSettingModel = new ImportSetting();
                        $importSettings = $importSettingModel->getByClientId($user['id']);
                    } else {
                        $importSettings = [];
                    }
                    ?>
                    
                    <?php if (empty($importSettings)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Brak skonfigurowanych ustawień importu.</strong> 
                            Dodaj pierwsze ustawienie, aby rozpocząć synchronizację z systemami zewnętrznymi.
                        </div>
                        <a href="<?= UrlHelper::url('client/import/create') ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Dodaj ustawienie importu
                        </a>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead class="table-light">
                                    <tr>
                                        <th>System</th>
                                        <th>Status</th>
                                        <th>Ostatnia synchronizacja</th>
                                        <th>Akcje</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($importSettings as $setting): ?>
                                        <tr>
                                            <td>
                                                <span class="badge bg-info">
                                                    <i class="fas fa-plug me-1"></i>
                                                    <?= htmlspecialchars(ucfirst($setting['system_name'])) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($setting['is_active']): ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check me-1"></i>
                                                        Aktywny
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-pause me-1"></i>
                                                        Nieaktywny
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($setting['last_sync']): ?>
                                                    <small class="text-muted">
                                                        <?= date('d.m.Y H:i', strtotime($setting['last_sync'])) ?>
                                                    </small>
                                                <?php else: ?>
                                                    <small class="text-muted">Brak</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="<?= UrlHelper::url('client/import/doctor-mappings/' . $setting['id']) ?>" 
                                                       class="btn btn-outline-primary btn-sm" 
                                                       title="Mapowania lekarzy">
                                                        <i class="fas fa-user-md"></i>
                                                    </a>
                                                    <a href="<?= UrlHelper::url('client/import/edit/' . $setting['id']) ?>" 
                                                       class="btn btn-outline-secondary btn-sm" 
                                                       title="Edytuj">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3">
                            <a href="<?= UrlHelper::url('client/import') ?>" class="btn btn-outline-primary me-2">
                                <i class="fas fa-cog me-2"></i>
                                Zarządzaj ustawieniami
                            </a>
                            <a href="<?= UrlHelper::url('client/import/create') ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                Dodaj nowe ustawienie
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="col-md-4">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-info-circle me-2"></i>
                                Informacje o imporcie
                            </h6>
                            <ul class="list-unstyled small mb-0">
                                <li><i class="fas fa-check text-success me-2"></i>Automatyczna synchronizacja harmonogramów</li>
                                <li><i class="fas fa-check text-success me-2"></i>Mapowanie lekarzy między systemami</li>
                                <li><i class="fas fa-check text-success me-2"></i>Obsługa różnych systemów medycznych</li>
                                <li><i class="fas fa-check text-success me-2"></i>Bezpieczne kody synchronizacji</li>
                            </ul>
                            
                            <hr>
                            
                            <h6 class="card-title">
                                <i class="fas fa-question-circle me-2"></i>
                                Jak to działa?
                            </h6>
                            <ol class="small mb-0">
                                <li>Dodaj ustawienie importu dla swojego systemu</li>
                                <li>Skopiuj kod synchronizacji</li>
                                <li>Użyj dodatku Chrome do eksportu danych</li>
                                <li>Automatyczna synchronizacja harmonogramów</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


