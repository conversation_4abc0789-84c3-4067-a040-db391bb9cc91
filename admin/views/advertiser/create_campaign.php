<?php /* Plik widoku dla dodawania kampanii przez reklamodawcę */ ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Utwórz nową kampanię</h1>
        <a href="/advertiser/campaigns" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Powrót
        </a>
    </div>

    <div class="row">
        <div class="col-xl-12 col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Formularz tworzenia kampanii</h6>
                </div>
                <div class="card-body">
                    <?php if (isset($errors) && !empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?= $error ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <form method="post" action="/advertiser/store-campaign" enctype="multipart/form-data">
                        <div class="form-group">
                            <label for="name">Nazwa kampanii</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                value="<?= $data['name'] ?? '' ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="description">Opis</label>
                            <textarea class="form-control" id="description" name="description" 
                                rows="3"><?= $data['description'] ?? '' ?></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="media_type">Typ mediów</label>
                            <select class="form-control" id="media_type" name="media_type" required>
                                <option value="">Wybierz typ mediów</option>
                                <option value="image" <?= (isset($data['media_type']) && $data['media_type'] == 'image') ? 'selected' : '' ?>>
                                    Obraz
                                </option>
                                <option value="video" <?= (isset($data['media_type']) && $data['media_type'] == 'video') ? 'selected' : '' ?>>
                                    Wideo
                                </option>
                                <option value="youtube" <?= (isset($data['media_type']) && $data['media_type'] == 'youtube') ? 'selected' : '' ?>>
                                    YouTube
                                </option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="budget">Budżet</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">PLN</span>
                                </div>
                                <input type="number" step="0.01" min="0" class="form-control" 
                                    id="budget" name="budget" value="<?= $data['budget'] ?? '100.00' ?>" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="rate_per_second">Stawka za sekundę wyświetlenia</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">PLN</span>
                                </div>
                                <input type="number" step="0.0001" min="0.0001" class="form-control" 
                                    id="rate_per_second" name="rate_per_second" value="<?= $data['rate_per_second'] ?? '0.0001' ?>" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="max_frequency_per_hour">Maksymalna częstotliwość na godzinę</label>
                            <input type="number" class="form-control" id="max_frequency_per_hour" 
                                name="max_frequency_per_hour" value="<?= $data['max_frequency_per_hour'] ?? '0' ?>" min="0" max="20">
                            <small class="form-text text-muted">
                                Maksymalna liczba wyświetleń reklamy w ciągu godziny (0 = bez ograniczeń)
                            </small>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="start_date">Data rozpoczęcia</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" 
                                        value="<?= $data['start_date'] ?? date('Y-m-d') ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="end_date">Data zakończenia</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" 
                                        value="<?= $data['end_date'] ?? date('Y-m-d', strtotime('+30 days')) ?>" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Wybierz klientów</label>
                            <div class="card">
                                <div class="card-body" style="max-height: 200px; overflow-y: auto;">
                                    <?php foreach ($clients as $client): ?>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" 
                                                id="client_<?= $client['id'] ?>" name="client_ids[]" 
                                                value="<?= $client['id'] ?>"
                                                <?= (isset($data['client_ids']) && in_array($client['id'], $data['client_ids'])) ? 'checked' : '' ?>>
                                            <label class="custom-control-label" for="client_<?= $client['id'] ?>">
                                                <?= $client['company_name'] ?> (<?= $client['username'] ?>)
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Utwórz kampanię</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const mediaTypeSelect = document.getElementById('media_type');
    
    // Tutaj możesz dodać dodatkową logikę JavaScript jeśli potrzebna
});
</script> 