<?php /* Plik widoku dla szczegółów kampanii reklamodawcy */ ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Szczegóły kampanii</h1>
        <a href="/advertiser/campaigns" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Powrót
        </a>
    </div>

    <div class="row">
        <div class="col-xl-12 col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary"><?= htmlspecialchars($campaign['name']) ?></h6>
                    <div>
                        <?php if ($campaign['is_active']): ?>
                            <span class="badge bg-success">Aktywna</span>
                        <?php else: ?>
                            <span class="badge bg-danger">Nieaktywna</span>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Informacje podstawowe</h5>
                            <table class="table">
                                <tr>
                                    <th>Opis:</th>
                                    <td><?= htmlspecialchars($campaign['description']) ?></td>
                                </tr>
                                <tr>
                                    <th>Typ mediów:</th>
                                    <td>
                                        <?php if ($campaign['media_type'] == 'image'): ?>
                                            <span class="badge badge-info">Obraz</span>
                                        <?php elseif ($campaign['media_type'] == 'video'): ?>
                                            <span class="badge badge-primary">Wideo</span>
                                        <?php elseif ($campaign['media_type'] == 'youtube'): ?>
                                            <span class="badge badge-danger">YouTube</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Czas trwania:</th>
                                    <td><?= $campaign['duration'] ?> sekund</td>
                                </tr>
                                <tr>
                                    <th>Maksymalna częstotliwość:</th>
                                    <td>
                                        <?php if ($campaign['max_frequency_per_hour'] > 0): ?>
                                            <?= $campaign['max_frequency_per_hour'] ?> wyświetleń na godzinę
                                        <?php else: ?>
                                            Bez ograniczeń
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Data rozpoczęcia:</th>
                                    <td><?= date('d.m.Y', strtotime($campaign['start_date'])) ?></td>
                                </tr>
                                <tr>
                                    <th>Data zakończenia:</th>
                                    <td><?= date('d.m.Y', strtotime($campaign['end_date'])) ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Finanse</h5>
                            <table class="table">
                                <tr>
                                    <th>Budżet:</th>
                                    <td><?= number_format($campaign['budget'], 2) ?> PLN</td>
                                </tr>
                                <tr>
                                    <th>Wydano:</th>
                                    <td><?= number_format($campaign['spent'], 2) ?> PLN</td>
                                </tr>
                                <tr>
                                    <th>Pozostało:</th>
                                    <td><?= number_format($campaign['budget'] - $campaign['spent'], 2) ?> PLN</td>
                                </tr>
                                <tr>
                                    <th>Stawka za sekundę:</th>
                                    <td><?= number_format($campaign['rate_per_second'], 4) ?> PLN</td>
                                </tr>
                                <tr>
                                    <th>Liczba wyświetleń:</th>
                                    <td><?= $campaign['total_views'] ?? 0 ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h5>Podgląd mediów</h5>
                            <div class="text-center">
                                <?php if ($campaign['media_type'] == 'image'): ?>
                                    <img src="<?= $campaign['media_url'] ?>" alt="Obraz kampanii" style="max-width: 100%; max-height: 300px;" class="img-thumbnail">
                                <?php elseif ($campaign['media_type'] == 'video'): ?>
                                    <video controls style="max-width: 100%; max-height: 300px;">
                                        <source src="<?= $campaign['media_url'] ?>" type="video/mp4">
                                        Twoja przeglądarka nie obsługuje odtwarzania wideo.
                                    </video>
                                <?php elseif ($campaign['media_type'] == 'youtube'): ?>
                                    <div class="embed-responsive embed-responsive-16by9">
                                        <iframe class="embed-responsive-item" src="<?= $campaign['media_url'] ?>" allowfullscreen></iframe>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 