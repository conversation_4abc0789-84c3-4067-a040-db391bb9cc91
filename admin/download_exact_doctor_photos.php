<?php
require_once 'core/Database.php';

class ExactDoctorPhotoDownloader {
    private $db;
    private $sonokardClientId = 2;
    private $uploadDir = '../uploads/doctors/';
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
        
        // Upewnij się, że katalog istnieje
        if (!is_dir($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }
    }
    
    /**
     * Mapowanie lekarzy na dokładne ścieżki zdjęć ze strony
     */
    private function getDoctorPhotoMappings() {
        return [
            'Małgorzata Olesiak-Andryszczak' => '/fileadmin/_processed_/2/0/csm_2291_01_698d3e2449.jpg',
            '<PERSON><PERSON><PERSON>' => '/fileadmin/_processed_/2/a/csm_11160poziom_www_3864d45b53.jpg',
            '<PERSON>' => '/fileadmin/_processed_/e/6/csm_062www_61a4374caa.jpg',
            '<PERSON><PERSON><PERSON><PERSON>' => '/fileadmin/_processed_/7/7/csm_162www_2090344a6a.jpg',
            'Aneta Walaszek-Gruszka' => '/fileadmin/_processed_/a/d/csm_Dr_Aneta_Walaszek-Gruszka-png-327x490_ab8b9f8370.jpg',
            'Yuliia Baraniak' => '/fileadmin/_processed_/8/e/csm_Yuliia_Baraniak-zaakceptowane_01_e32db26d86.jpg',
            'Beata Dawiec' => '/fileadmin/_processed_/8/b/csm_Beata_Dawiec_www_6997bb17e3.jpg',
            'Agnieszka Tyszko-Tymińska' => '/fileadmin/_processed_/6/2/csm_Tyszko_Tyminska_dfd54effd6.jpg',
            'Joanna Nestorowicz-Czernianin' => '/fileadmin/_processed_/1/c/csm_11286_poziom_f25c6505ad.jpg',
            'Tomasz Kościelniak' => '/fileadmin/_processed_/4/d/csm_T_Koscielniak_black_a5a73ca48b.jpg',
            'Jakub Andrzejewski' => '/fileadmin/_processed_/4/9/csm_11734www_ffc59627ea.jpg',
            'Przemysław Piec' => '/fileadmin/_processed_/b/9/csm_BLACK_pospieszni-153_8538b4b05b.jpg',
            'Sylwia Wnuk' => '/fileadmin/_processed_/5/4/csm_11858www_64be17b062.jpg',
            'Aleksandra Żurakowska' => '/fileadmin/_processed_/e/8/csm_123www_db7b429856.jpg',
            'Piotr Miśkiewicz' => '/fileadmin/_processed_/e/d/csm_076www_77a05dac4d.jpg',
            'Grzegorz Dobaczewski' => '/fileadmin/_processed_/9/9/csm_dobaczewski_01_062aac6763.jpg',
            'Justyna Kuliczkowska-Płaksej' => '/fileadmin/_processed_/3/c/csm_kuliczkowska_black_ab5eeacb7e.jpg',
            'Barbara Stachowska' => '/fileadmin/_processed_/1/8/csm_11038_34d47de9f6.jpg',
            'Ewelina Jasic-Szpak' => '/fileadmin/_processed_/c/5/csm_Ewelina_Jasic-Szpak_40301f6a6d.jpg',
            'Katarzyna Kulej-Łyko' => '/fileadmin/_processed_/9/2/csm_Kulej_Lyko_327129096b.jpg',
            'Marta Obremska' => '/fileadmin/_processed_/c/4/csm_avatar_32d3b4fed0.png',
            'Amelia Głowaczewska-Wójcik' => '/fileadmin/_processed_/2/f/csm_www_dr_Amelia_b052eac79e.jpg',
            'Marta Nogaj-Adamowicz' => '/fileadmin/_processed_/5/9/csm_Marta_Nogaj_e12b985100.jpg',
            'Łukasz Jabłoński' => '/fileadmin/_processed_/d/6/csm_Jablonski_ceaa0e7889.jpg',
            'Jerzy Płochowski' => '/fileadmin/_processed_/f/f/csm_plochowski_83e7623dbe.jpg',
            'Malwina Pawik' => '/fileadmin/_processed_/e/7/csm_malwina-pawlik_8bb01c073b.jpg',
            'Bożena Dołęga-Kozierowska' => '/fileadmin/_processed_/0/3/csm_BDK_po_korekcji_2a4589d5bb.jpg',
            'Ryszard Ślęzak' => '/fileadmin/_processed_/8/3/csm_ryszard_slezak_47f2e33716.jpg',
            'Piotr Siekanowicz' => '/fileadmin/_processed_/1/a/csm_Siekanowicz_Piotr_61b50b8b55.jpg',
            'Izabela Śliwińska' => '/fileadmin/_processed_/3/d/csm_kotynsk_5652a9c4bb.jpg',
            'Magdalena Dudziec' => '/fileadmin/_processed_/2/d/csm_434307234_914076367390906_269704848870807819_n_cd3a4edce5.jpg',
            'Klaudia Bonar' => '/fileadmin/_processed_/4/9/csm_11929www_671ee9119d.jpg'
        ];
    }
    
    /**
     * Pobiera wszystkich lekarzy
     */
    public function getAllDoctors() {
        $stmt = $this->db->prepare("
            SELECT id, first_name, last_name, photo_url 
            FROM queue_doctors 
            WHERE client_id = ? AND active = 1
            ORDER BY id
        ");
        $stmt->execute([$this->sonokardClientId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Czyści imię i nazwisko z tytułów
     */
    private function cleanDoctorName($firstName, $lastName) {
        $fullName = trim($firstName . ' ' . $lastName);
        
        // Usuń tytuły
        $fullName = preg_replace('/^(dr n\. med\.|lek\.|mgr|dr n\. k\. f\.|dr\. n\. med\.|dr)\s+/i', '', $fullName);
        $fullName = trim($fullName);
        
        return $fullName;
    }
    
    /**
     * Pobiera obraz z URL
     */
    private function downloadImage($url) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HEADER, false);
        
        $data = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        curl_close($ch);
        
        // Sprawdź czy to rzeczywiście obraz
        if ($httpCode === 200 && strpos($contentType, 'image/') === 0 && strlen($data) > 1000) {
            return $data;
        }
        
        return false;
    }
    
    /**
     * Zapisuje obraz do katalogu uploads
     */
    private function saveImage($imageData, $doctorName, $originalPath) {
        // Generuj nazwę pliku na podstawie nazwy lekarza
        $extension = pathinfo($originalPath, PATHINFO_EXTENSION);
        if (empty($extension)) {
            $extension = 'jpg';
        }
        
        // Czyść nazwę lekarza dla nazwy pliku
        $cleanName = strtolower($doctorName);
        $cleanName = str_replace(['ą', 'ć', 'ę', 'ł', 'ń', 'ó', 'ś', 'ź', 'ż'], 
                                ['a', 'c', 'e', 'l', 'n', 'o', 's', 'z', 'z'], $cleanName);
        $cleanName = preg_replace('/[^a-z0-9]/', '_', $cleanName);
        $cleanName = preg_replace('/_+/', '_', $cleanName);
        $cleanName = trim($cleanName, '_');
        
        $filename = 'sonokard_' . $cleanName . '.' . $extension;
        $filepath = $this->uploadDir . $filename;
        
        if (file_put_contents($filepath, $imageData)) {
            return '/uploads/doctors/' . $filename;
        }
        
        return false;
    }
    
    /**
     * Aktualizuje URL zdjęcia w bazie danych
     */
    private function updateDoctorPhoto($doctorId, $photoUrl) {
        $stmt = $this->db->prepare("
            UPDATE queue_doctors 
            SET photo_url = ? 
            WHERE id = ?
        ");
        return $stmt->execute([$photoUrl, $doctorId]);
    }
    
    /**
     * Główna metoda pobierania zdjęć
     */
    public function downloadAllPhotos() {
        echo "Rozpoczynam pobieranie dokładnych zdjęć lekarzy ze strony sonokard.pl...\n";
        
        $doctors = $this->getAllDoctors();
        $photoMappings = $this->getDoctorPhotoMappings();
        
        echo "Znaleziono " . count($doctors) . " lekarzy\n";
        echo "Dostępne mapowania zdjęć: " . count($photoMappings) . "\n\n";
        
        $downloaded = 0;
        $failed = 0;
        $notFound = 0;
        
        foreach ($doctors as $doctor) {
            $cleanName = $this->cleanDoctorName($doctor['first_name'], $doctor['last_name']);
            echo "Pobieranie zdjęcia dla: $cleanName (ID: {$doctor['id']})\n";
            
            if (isset($photoMappings[$cleanName])) {
                $photoPath = $photoMappings[$cleanName];
                $fullUrl = 'https://sonokard.pl' . $photoPath;
                
                echo "  URL: $fullUrl\n";
                
                $imageData = $this->downloadImage($fullUrl);
                if ($imageData !== false) {
                    $localPhotoUrl = $this->saveImage($imageData, $cleanName, $photoPath);
                    
                    if ($localPhotoUrl && $this->updateDoctorPhoto($doctor['id'], $localPhotoUrl)) {
                        echo "  ✓ Pobrano i zapisano: $localPhotoUrl\n";
                        $downloaded++;
                    } else {
                        echo "  ✗ Błąd podczas zapisywania\n";
                        $failed++;
                    }
                } else {
                    echo "  ✗ Nie udało się pobrać zdjęcia\n";
                    $failed++;
                }
            } else {
                echo "  ⚠ Brak mapowania dla tego lekarza\n";
                $notFound++;
            }
            
            echo "\n";
            sleep(1); // Pauza między pobieraniami
        }
        
        echo "Podsumowanie:\n";
        echo "- Pobrano zdjęć: $downloaded\n";
        echo "- Błędy pobierania: $failed\n";
        echo "- Brak mapowania: $notFound\n";
        echo "- Łącznie lekarzy: " . count($doctors) . "\n";
    }
    
    /**
     * Wyświetla listę lekarzy i ich mapowań
     */
    public function listDoctorMappings() {
        $doctors = $this->getAllDoctors();
        $photoMappings = $this->getDoctorPhotoMappings();
        
        echo "MAPOWANIA LEKARZY NA ZDJĘCIA:\n";
        echo str_repeat("=", 80) . "\n";
        
        foreach ($doctors as $doctor) {
            $cleanName = $this->cleanDoctorName($doctor['first_name'], $doctor['last_name']);
            $hasMapping = isset($photoMappings[$cleanName]) ? "✓" : "✗";
            $photoPath = $photoMappings[$cleanName] ?? "BRAK";
            
            echo "ID: {$doctor['id']} | $hasMapping | $cleanName\n";
            if ($hasMapping === "✓") {
                echo "    Ścieżka: $photoPath\n";
            }
            echo "\n";
        }
    }
}

// Uruchom pobieranie jeśli skrypt jest wywołany bezpośrednio
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $downloader = new ExactDoctorPhotoDownloader();
    
    // Sprawdź argumenty wiersza poleceń
    if (isset($argv[1]) && $argv[1] === 'list') {
        $downloader->listDoctorMappings();
    } else {
        $downloader->downloadAllPhotos();
    }
}
?>
