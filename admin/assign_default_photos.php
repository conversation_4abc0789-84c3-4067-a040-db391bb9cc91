<?php
require_once 'core/Database.php';

class DefaultPhotoAssigner {
    private $db;
    private $sonokardClientId = 2;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    /**
     * Pobiera listę lekarzy bez zdjęć
     */
    public function getDoctorsWithoutPhotos() {
        $stmt = $this->db->prepare("
            SELECT id, first_name, last_name, photo_url 
            FROM queue_doctors 
            WHERE client_id = ? AND active = 1 AND (photo_url IS NULL OR photo_url = '')
        ");
        $stmt->execute([$this->sonokardClientId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Aktualizuje URL zdjęcia w bazie danych
     */
    private function updateDoctorPhoto($doctorId, $photoUrl) {
        $stmt = $this->db->prepare("
            UPDATE queue_doctors 
            SET photo_url = ? 
            WHERE id = ?
        ");
        return $stmt->execute([$photoUrl, $doctorId]);
    }
    
    /**
     * Przypisuje domyślne zdjęcia lekarzom bez zdjęć
     */
    public function assignDefaultPhotos() {
        echo "Rozpoczynam przypisywanie domyślnych zdjęć...\n";
        
        $doctors = $this->getDoctorsWithoutPhotos();
        echo "Znaleziono " . count($doctors) . " lekarzy bez zdjęć\n";
        
        // Lista dostępnych domyślnych zdjęć
        $defaultPhotos = [
            '/uploads/doctors/doctor4.webp',
            '/uploads/doctors/doctor5.webp', 
            '/uploads/doctors/doctor6.webp',
            '/uploads/doctors/doctor7.webp'
        ];
        
        $updated = 0;
        $photoIndex = 0;
        
        foreach ($doctors as $doctor) {
            // Wybierz zdjęcie cyklicznie
            $photoUrl = $defaultPhotos[$photoIndex % count($defaultPhotos)];
            
            if ($this->updateDoctorPhoto($doctor['id'], $photoUrl)) {
                echo "✓ Przypisano zdjęcie {$photoUrl} dla: {$doctor['first_name']} {$doctor['last_name']}\n";
                $updated++;
            } else {
                echo "✗ Błąd podczas aktualizacji dla: {$doctor['first_name']} {$doctor['last_name']}\n";
            }
            
            $photoIndex++;
        }
        
        echo "\nPodsumowanie:\n";
        echo "- Zaktualizowano lekarzy: $updated\n";
    }
    
    /**
     * Przypisuje konkretne zdjęcie konkretnemu lekarzowi
     */
    public function assignSpecificPhoto($doctorId, $photoUrl) {
        if ($this->updateDoctorPhoto($doctorId, $photoUrl)) {
            echo "✓ Przypisano zdjęcie $photoUrl dla lekarza ID: $doctorId\n";
            return true;
        } else {
            echo "✗ Błąd podczas przypisywania zdjęcia\n";
            return false;
        }
    }
    
    /**
     * Wyświetla listę lekarzy bez zdjęć
     */
    public function listDoctorsWithoutPhotos() {
        $doctors = $this->getDoctorsWithoutPhotos();
        
        echo "Lekarze bez zdjęć:\n";
        echo "ID\tImię i nazwisko\n";
        echo "---\t----------------\n";
        
        foreach ($doctors as $doctor) {
            echo "{$doctor['id']}\t{$doctor['first_name']} {$doctor['last_name']}\n";
        }
        
        echo "\nŁącznie: " . count($doctors) . " lekarzy\n";
    }
}

// Uruchom przypisywanie jeśli skrypt jest wywołany bezpośrednio
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $assigner = new DefaultPhotoAssigner();
    
    // Sprawdź argumenty wiersza poleceń
    if (isset($argv[1]) && $argv[1] === 'list') {
        $assigner->listDoctorsWithoutPhotos();
    } else {
        $assigner->assignDefaultPhotos();
    }
}
?>
