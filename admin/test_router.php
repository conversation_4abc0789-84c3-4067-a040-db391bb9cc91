<?php
session_start();

// Autoloader dla klas
spl_autoload_register(function ($className) {
    $paths = [
        __DIR__ . '/controllers/' . $className . '.php',
        __DIR__ . '/models/' . $className . '.php',
        __DIR__ . '/core/' . $className . '.php'
    ];
    
    foreach ($paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            return;
        }
    }
});

// Inicjalizacja bazy danych
Database::init();

echo "<h1>Test Routera</h1>";
echo "<p>REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p>SCRIPT_NAME: " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p>REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD'] . "</p>";

// Test HomeController
echo "<h2>Test HomeController</h2>";
try {
    $controller = new HomeController();
    echo "HomeController utworzony pomyślnie<br>";
    
    // Test metody index
    echo "Wywołuję metodę index...<br>";
    $controller->index();
} catch (Exception $e) {
    echo "Błąd: " . $e->getMessage() . "<br>";
}

echo "<h2>Test AuthController</h2>";
try {
    $controller = new AuthController();
    echo "AuthController utworzony pomyślnie<br>";
    
    // Test metody loginForm
    echo "Wywołuję metodę loginForm...<br>";
    $controller->loginForm();
} catch (Exception $e) {
    echo "Błąd: " . $e->getMessage() . "<br>";
}
?> 