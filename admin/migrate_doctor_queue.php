<?php

require_once 'core/Database.php';
require_once 'core/Controller.php';

echo "Rozpoczynam migrację systemu kolejkowego na lekarzy...\n";

try {
    $db = Database::getInstance()->getConnection();
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Rozpocznij transakcję
    $db->beginTransaction();
    
    // 1. Dodaj kolumnę doctor_id do tabeli queue_appointments
    echo "Dodaję kolumnę doctor_id do tabeli queue_appointments...\n";
    $db->exec("ALTER TABLE queue_appointments ADD COLUMN doctor_id INTEGER");
    $db->exec("ALTER TABLE queue_appointments ADD COLUMN appointment_date DATE DEFAULT CURRENT_DATE");
    
    // 2. Dodaj kolumnę default_room_id do tabeli queue_doctors (jeśli nie istnieje)
    echo "Sprawdzam kolumnę default_room_id w tabeli queue_doctors...\n";
    try {
        $db->exec("ALTER TABLE queue_doctors ADD COLUMN default_room_id INTEGER");
    } catch (PDOException $e) {
        // Kolumna może już istnieć, to normalne
        echo "Kolumna default_room_id już istnieje lub wystąpił błąd: " . $e->getMessage() . "\n";
    }
    
    // 3. Zaktualizuj istniejące wizyty - przypisz lekarzy na podstawie gabinetów
    echo "Aktualizuję istniejące wizyty - przypisuję lekarzy...\n";
    $db->exec("
        UPDATE queue_appointments 
        SET doctor_id = (
            SELECT doctor_id 
            FROM queue_rooms 
            WHERE queue_rooms.id = queue_appointments.room_id
        )
        WHERE doctor_id IS NULL
    ");
    
    // 4. Ustaw datę wizyt dla istniejących rekordów
    echo "Ustawiam datę wizyt dla istniejących rekordów...\n";
    $db->exec("
        UPDATE queue_appointments 
        SET appointment_date = DATE(created_at)
        WHERE appointment_date IS NULL
    ");
    
    // 5. Dodaj indeksy dla lepszej wydajności
    echo "Dodaję indeksy dla lepszej wydajności...\n";
    $db->exec("CREATE INDEX IF NOT EXISTS idx_appointments_doctor_date ON queue_appointments(doctor_id, appointment_date)");
    $db->exec("CREATE INDEX IF NOT EXISTS idx_appointments_room_date ON queue_appointments(room_id, appointment_date)");
    $db->exec("CREATE INDEX IF NOT EXISTS idx_appointments_status ON queue_appointments(status)");
    
    // Zatwierdź transakcję
    $db->commit();
    
    echo "Migracja zakończona pomyślnie!\n";
    echo "System kolejkowy został zaktualizowany do pracy z lekarzami.\n";
    
} catch (PDOException $e) {
    // Cofnij transakcję w przypadku błędu
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    echo "Błąd podczas migracji: " . $e->getMessage() . "\n";
    exit(1);
}

?> 