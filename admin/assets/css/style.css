/* Główne style dla nowoczesnego wyglądu */
body {
    font-size: 0.875rem;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    background-color: #f8f9fc;
    margin: 0;
    padding: 0;
    color: #333;
}

/* Header i nawigacja */
.header {
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 60px;
}

.header.scrolled {
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.15);
}

.header-content {
    display: flex;
    align-items: center;
    height: 100%;
}

.logo {
    padding: 0 15px;
}

.logo a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #4e73df;
    font-weight: 600;
    font-size: 1.25rem;
}

.logo i {
    font-size: 1.5rem;
    margin-right: 10px;
}

/* <PERSON>ł<PERSON>na nawigacja */
.main-nav {
    flex: 1;
    display: flex;
    justify-content: center;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    height: 60px;
}

.nav-menu li {
    margin: 0;
    height: 100%;
}

.nav-menu a {
    display: flex;
    align-items: center;
    padding: 0 20px;
    color: #555;
    text-decoration: none;
    height: 100%;
    transition: background-color 0.2s;
}

.nav-menu a:hover {
    background-color: #f0f2ff;
    color: #4e73df;
}

.nav-menu a.active {
    background-color: #4e73df;
    color: #fff;
}

.nav-menu a i {
    margin-right: 8px;
    font-size: 1rem;
}

/* Menu użytkownika */
.user-menu {
    padding: 0 15px;
    height: 60px;
    display: flex;
    align-items: center;
}

.user-menu .dropdown-toggle {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #555;
    height: 100%;
}

.user-menu .dropdown-toggle::after {
    display: none;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background-color: #4e73df;
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
}

.user-name {
    font-weight: 500;
    margin-right: 5px;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 10px 0;
}

.dropdown-item {
    padding: 8px 20px;
    font-size: 0.875rem;
}

.dropdown-item:hover {
    background-color: #f8f9fc;
}

/* Przycisk do mobilnego menu */
.mobile-toggle {
    display: none;
    background: none;
    border: none;
    color: #4e73df;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0 15px;
}

/* Główny kontener */
.main-container {
    margin-top: 60px;
    padding: 20px;
    min-height: calc(100vh - 60px);
}

.content-wrapper {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

/* Nagłówek strony */
/* Usunięto .page-header – nagłówki w widokach renderowane inline w content-wrapper */

/* Karty statystyk */
.card {
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 5px;
    margin-bottom: 20px;
}

.card-header {
    background-color: #4e73df;
    color: white;
    padding: 15px 20px;
    font-weight: 500;
    border-top-left-radius: 5px !important;
    border-top-right-radius: 5px !important;
}

.card-title {
    margin: 0;
    color: white;
    font-size: 1.1rem;
}

.card-body {
    padding: 20px;
}

/* Kolory dla kart statystyk */
.border-left-primary {
    border-left: 4px solid #4e73df;
}

.border-left-success {
    border-left: 4px solid #1cc88a;
}

.border-left-info {
    border-left: 4px solid #36b9cc;
}

.border-left-warning {
    border-left: 4px solid #f6c23e;
}

.text-xs {
    font-size: 0.7rem;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

/* Przyciski */
.btn {
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 0.875rem;
    font-weight: 500;
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

.btn-success {
    background-color: #1cc88a;
    border-color: #1cc88a;
}

.btn-info {
    background-color: #36b9cc;
    border-color: #36b9cc;
}

.btn-warning {
    background-color: #f6c23e;
    border-color: #f6c23e;
}

/* Tabele */
.table {
    color: #858796;
    margin-bottom: 0;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 1px solid #e3e6f0;
    font-weight: 500;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 15px;
}

.table td {
    border-top: 1px solid #e3e6f0;
    padding: 15px;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    color: #858796;
    background-color: rgba(0, 0, 0, 0.03);
}

/* Badge'y */
.badge {
    font-size: 0.65rem;
    padding: 0.35rem 0.65rem;
    border-radius: 4px;
}

/* Formularze */
.form-control {
    font-size: 0.875rem;
    border-radius: 4px;
    border: 1px solid #d1d3e2;
    padding: 10px 15px;
}

.form-control:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Strona logowania */
.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #4e73df;
    background-image: linear-gradient(180deg, #4e73df 10%, #224abe 100%);
}

.auth-box {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    padding: 30px;
    width: 100%;
    max-width: 400px;
}

.auth-logo {
    text-align: center;
    margin-bottom: 30px;
}

.auth-logo i {
    font-size: 3rem;
    color: #4e73df;
    margin-bottom: 15px;
}

.auth-logo h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

/* Wyświetlacz reklam */
.ad-display {
    background: #000;
    width: 100%;
    height: 100vh;
    position: relative;
    overflow: hidden;
}

.ad-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ad-content img, 
.ad-content video {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.ad-info {
    position: absolute;
    bottom: 20px;
    left: 20px;
    color: white;
    background: rgba(0,0,0,0.7);
    padding: 10px;
    border-radius: 5px;
    font-size: 14px;
}

/* Progress bar dla czasu wyświetlania */
.ad-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 4px;
    background: #4e73df;
    transition: width 0.1s ease;
}

/* Status online/offline */
.status-online {
    color: #1cc88a;
}

.status-offline {
    color: #e74a3b;
}

/* Responsywność */
@media (max-width: 992px) {
    .main-nav {
        position: fixed;
        top: 60px;
        left: -100%;
        width: 250px;
        height: calc(100vh - 60px);
        background-color: #fff;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        z-index: 999;
    }
    
    .main-nav.active {
        left: 0;
    }
    
    .nav-menu {
        flex-direction: column;
        height: auto;
        padding: 20px 0;
    }
    
    .nav-menu li {
        margin: 0;
        height: auto;
    }
    
    .nav-menu a {
        padding: 12px 20px;
        height: auto;
    }
    
    .mobile-toggle {
        display: block;
    }
}

@media (max-width: 768px) {
    .header-content {
        padding: 0 10px;
    }
    
    .user-name {
        display: none;
    }
    
    .main-container {
        padding: 15px 10px;
    }
    
    .content-wrapper {
        padding: 0;
    }
    
    /* brak specyficznych stylów .page-header */
}

/* Statystyki na dashboardzie */
.stats-card {
    display: flex;
    align-items: center;
    padding: 20px;
}

.stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 1.5rem;
}

.stats-icon.primary {
    background-color: #4e73df;
    color: #fff;
}

.stats-icon.success {
    background-color: #1cc88a;
    color: #fff;
}

.stats-icon.info {
    background-color: #36b9cc;
    color: #fff;
}

.stats-icon.warning {
    background-color: #f6c23e;
    color: #fff;
}

.stats-info h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 5px;
}

.stats-info p {
    margin: 0;
    color: #858796;
    font-size: 0.75rem;
    text-transform: uppercase;
} 