body {
    margin: 0;
    padding: 0;
    overflow: hidden;
    background-color: #000;
    color: #fff;
}

.display-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
}

.display-header {
    padding: 10px 15px;
    background-color: rgba(0,0,0,0.8);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #333;
    z-index: 10;
}

.display-content {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    height: 100vh;
}

.ad-container {
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.ad-image {
    width: 100vw;
    height: 100vh;
    object-fit: contain;
    background: #000;
}

.ad-video {
    width: 100vw;
    height: 100vh;
    object-fit: contain;
    object-position: left;
    background: #000;
}

.ad-youtube {
    width: 100%;
    height: 100%;
}

.company-info {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background-color: rgba(0,0,0,0.7);
    padding: 10px;
    border-radius: 5px;
    font-size: 18px;
}

.status-badge {
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
}

.no-ads {
    text-align: center;
    font-size: 24px;
    color: #ccc;
}

.fullscreen-btn {
    background-color: rgba(0,0,0,0.5);
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    margin-left: 10px;
}

.fullscreen-btn:hover {
    background-color: rgba(0,0,0,0.8);
}

/* Styl podczas pełnego ekranu */
:fullscreen, ::backdrop {
    background-color: black;
}

/* Style dla systemu kolejkowego */
.display-with-queue {
    display: grid;
    grid-template-columns: 350px 1fr;
    grid-template-rows: 1fr;
    height: 100%;
}

.queue-sidebar {
    grid-column: 1;
    grid-row: 1;
    background-color: #1a1a1a;
    color: white;
    border-right: 1px solid #333;
    padding: 20px;
    overflow: hidden;
    width: 350px;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 10;
}

.queue-header {
    background-color: #2c3e50;
    color: white;
    padding: 10px 15px;
    margin: -15px -15px 15px -15px;
    font-size: 18px;
    font-weight: bold;
}

.queue-room {
    margin-bottom: 20px;
    border-radius: 10px;
    background-color: #222;
    padding: 15px;
}

.queue-room-header {
    text-align: center;
    margin-bottom: 10px;
}

.doctor-photo {
    width: 150px;
    height: 150px;
    margin: 0 auto 10px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid #fff;
    box-shadow: 0 0 15px rgba(0,0,0,0.5);
}

.doctor-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.doctor-name {
    font-size: 18px;
    color: #aaa;
    margin-bottom: 5px;
}

.room-name {
    font-size: 24px;
    font-weight: bold;
    color: #fff;
    margin-bottom: 10px;
}

.queue-current {
    background: rgba(0,0,0,0.3);
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 15px;
    text-align: center;
}

.queue-current .current-label {
    font-size: 16px;
    color: #aaa;
    margin-bottom: 5px;
}

.queue-current .current-number {
    font-size: 36px;
    color: #f39c12;
    font-weight: bold;
    margin-bottom: 5px;
}

.queue-current .current-patient {
    font-size: 20px;
    color: #fff;
}

.queue-waiting {
    text-align: center;
}

.queue-waiting .waiting-label {
    font-size: 16px;
    color: #aaa;
    margin-bottom: 10px;
}

.queue-number {
    background: rgba(0,0,0,0.2);
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 8px;
}

.queue-number .number {
    font-size: 24px;
    color: #f39c12;
    font-weight: bold;
    margin-bottom: 2px;
}

.queue-number .patient {
    font-size: 18px;
    color: #fff;
}

/* Czcionka systemowa dla lepszej czytelności */
.queue-sidebar * {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.queue-ad-container {
    grid-column: 2;
    grid-row: 1;
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.header-left {
    display: flex;
    align-items: center;
}

.header-center {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-grow: 1;
}

.header-right {
    display: flex;
    align-items: center;
}

.current-number-display {
    display: flex;
    align-items: center;
    font-size: 20px;
    color: #f39c12;
    margin-left: 15px;
}

.current-room-name {
    font-weight: bold;
    color: white;
    margin-right: 10px;
}

.current-number {
    font-weight: bold;
    font-size: 24px;
    color: #f39c12;
}

.queue-rooms-container {
    display: none;
}

.queue-rooms-container.active {
    display: block;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    border: 2px solid white;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 24px;
    z-index: 1000;
    transition: all 0.3s ease;
}

.play-button:hover {
    background-color: rgba(0, 0, 0, 0.9);
    transform: translate(-50%, -50%) scale(1.1);
}

.play-button.hidden {
    display: none;
}

.video-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.ad-video {
    height: 100vh;
    width: auto;
    max-width: 100vw;
    display: block;
    margin: 0 auto;
    object-fit: contain;
    object-position: center;
}

/* Animacje wjazdu dla aktualnej wizyty i elementów panelu */
.slide-in-left {
    animation: slideInLeft 0.5s;
}

.slide-in-right {
    animation: slideInRight 0.5s;
}

.slide-in-bottom {
    animation: slideInBottom 0.5s;
}

.slide-in-top {
    animation: slideInTop 0.5s;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInBottom {
    from { transform: translateY(100%); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInTop {
    from { transform: translateY(-100%); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
} 