/* Custom styles for queue system */

/* General styles for client mode */
.client-mode {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --accent-color: #f39c12;
    --dark-color: #2c3e50;
}

/* Logo styling for queue system */
.client-mode .logo a {
    color: var(--primary-color);
}

.client-mode .logo i {
    color: var(--primary-color);
}

/* Queue-specific card styles */
.border-left-primary {
    border-left: 4px solid var(--primary-color, #4e73df) !important;
}
.border-left-success {
    border-left: 4px solid var(--secondary-color, #1cc88a) !important;
}
.border-left-info {
    border-left: 4px solid #36b9cc !important;
}
.border-left-warning {
    border-left: 4px solid var(--accent-color, #f6c23e) !important;
}

/* Section headers */
.section-title {
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #e3e6f0;
    font-size: 1.25rem;
    color: var(--primary-color, #4e73df);
}

/* Status indicators for clinics and displays */
.status-indicator {
    height: 0.75rem;
    width: 0.75rem;
    border-radius: 50%;
    display: inline-block;
}

/* Queue system card headers */
.client-mode .card-header.bg-primary {
    background-color: var(--primary-color) !important;
}

.client-mode .card-header.bg-success {
    background-color: var(--secondary-color) !important;
}

.client-mode .card-header.bg-info {
    background-color: #36b9cc !important;
}

/* Queue navigation highlighting */
.client-mode .main-nav .nav-menu li:first-child a {
    color: var(--primary-color);
    font-weight: bold;
}

.client-mode .main-nav .nav-menu li:first-child a i {
    color: var(--primary-color);
}

/* Queue specific buttons */
.btn-queue {
    background-color: var(--primary-color, #4e73df);
    border-color: var(--primary-color, #4e73df);
    color: white;
}

.btn-queue:hover {
    background-color: #2980b9;
    border-color: #2980b9;
    color: white;
}

/* Appointment list styling */
.appointment-scroll::-webkit-scrollbar {
    width: 6px;
}

.appointment-scroll::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.appointment-scroll::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 3px;
}

.appointment-scroll::-webkit-scrollbar-thumb:hover {
    background: #ccc;
}

.appointment-item:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

/* Compact buttons for queue management */
.btn-compact {
    padding: 0.25rem 0.5rem;
    line-height: 1;
}

/* Queue display styling */
.queue-display {
    background-color: #2c3e50;
    color: white;
    padding: 20px;
    border-radius: 5px;
}

.queue-display .current-number {
    font-size: 72px;
    font-weight: bold;
    color: #f39c12;
}

.queue-display .waiting-numbers {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.queue-display .waiting-number {
    background-color: rgba(255, 255, 255, 0.1);
    padding: 5px 10px;
    border-radius: 3px;
}
