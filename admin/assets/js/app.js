// Główne funkcje aplikacji

// Obsługa mobilnego menu
function setupMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-toggle');
    const mainNav = document.querySelector('.main-nav');
    
    if (mobileToggle && mainNav) {
        mobileToggle.addEventListener('click', function() {
            mainNav.classList.toggle('active');
            
            // Zmiana ikony przycisku
            const icon = this.querySelector('i');
            if (icon.classList.contains('fa-bars')) {
                icon.classList.remove('fa-bars');
                icon.classList.add('fa-times');
            } else {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        });
        
        // Zamykanie menu po kliknięciu poza nim
        document.addEventListener('click', function(event) {
            if (!mainNav.contains(event.target) && !mobileToggle.contains(event.target) && mainNav.classList.contains('active')) {
                mainNav.classList.remove('active');
                const icon = mobileToggle.querySelector('i');
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        });
    }
}

// Odświeżanie statusu klientów
function refreshClientStatus() {
    location.reload();
}

// System wyświetlania reklam
if (typeof AdDisplay === 'undefined') {
    class AdDisplay {
        constructor() {
            this.currentAd = null;
            this.adContainer = document.getElementById('ad-container');
            this.isPlaying = false;
            this.clientId = this.getClientId();
            this.videoPlaybackStarted = false;
            
            if (this.clientId && this.adContainer) {
                this.init();
            }
        }
        
        getClientId() {
            const meta = document.querySelector('meta[name="client-id"]');
            return meta ? meta.content : null;
        }
        
        init() {
            this.startHeartbeat();
            this.loadNextAd();
        }
        
        startHeartbeat() {
            setInterval(() => {
                fetch('/client/heartbeat', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                    body: 'display_name=Monitor #1'
                });
            }, 30000);
        }
        
        async loadNextAd() {
            try {
                const response = await fetch(`/api/ads/${this.clientId}`);
                const data = await response.json();
                
                if (data.ads && data.ads.length > 0) {
                    this.displayAd(data.ads[0]);
                } else {
                    this.showNoAds();
                }
            } catch (error) {
                this.showError();
            }
        }
        
        displayAd(ad) {
            if (this.isPlaying) return;
            this.currentAd = ad;
            this.isPlaying = true;
            console.log('[AdDisplay] Wyświetlam reklamę:', ad);
            
            if (ad.media_type === 'video') {
                // Dla video wyświetl przycisk play lub automatyczne odtwarzanie
                this.adContainer.innerHTML = `
                    <div class="ad-content">
                        <div class="video-container" style="position: relative; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center;">
                            <video id="ad-video" src="${ad.media_url}" preload="auto" style="max-width: 100%; max-height: 100%; object-fit: contain;"></video>
                            ${!this.videoPlaybackStarted ? '<div class="play-button" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background-color: rgba(0, 0, 0, 0.7); color: white; border: 2px solid white; border-radius: 50%; width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 24px; z-index: 1000;"><i class="fas fa-play"></i></div>' : ''}
                        </div>
                    </div>
                    <div class="ad-info">
                        <strong>${ad.name}</strong><br>
                        Czas: ${ad.duration}s | Stawka: ${ad.rate_per_second} zł/s
                    </div>
                    <div class="ad-progress" style="width: 0%"></div>
                `;
                
                const video = this.adContainer.querySelector('#ad-video');
                const playButton = this.adContainer.querySelector('.play-button');
                
                if (video) {
                    if (this.videoPlaybackStarted) {
                        // Automatyczne odtwarzanie jeśli już rozpoczęto
                        video.currentTime = 0;
                        video.play().then(() => {
                            video.onended = () => {
                                console.log('[AdDisplay] Zdarzenie onended video');
                                this.finishAd();
                            };
                        }).catch(error => {
                            console.error('Błąd podczas automatycznego odtwarzania wideo:', error);
                            this.finishAd();
                        });
                    } else if (playButton) {
                        // Nasłuchuj na kliknięcie przycisku play
                        playButton.addEventListener('click', () => {
                            this.videoPlaybackStarted = true; // Oznacz że rozpoczęto odtwarzanie
                            playButton.style.display = 'none';
                            video.currentTime = 0;
                            video.play().then(() => {
                                video.onended = () => {
                                    console.log('[AdDisplay] Zdarzenie onended video');
                                    this.finishAd();
                                };
                            }).catch(error => {
                                console.error('Błąd podczas odtwarzania wideo:', error);
                                this.finishAd();
                            });
                        });
                    }
                }
            } else {
                // Dla innych typów mediów standardowe wyświetlanie
                this.adContainer.innerHTML = `
                    <div class="ad-content">
                        ${this.getMediaContent(ad)}
                    </div>
                    <div class="ad-info">
                        <strong>${ad.name}</strong><br>
                        Czas: ${ad.duration}s | Stawka: ${ad.rate_per_second} zł/s
                    </div>
                    <div class="ad-progress" style="width: 0%"></div>
                `;
                this.startTimer(ad.duration);
            }
        }
        
        getMediaContent(ad) {
            switch (ad.media_type) {
                case 'image':
                    return `<img src="${ad.media_url}" alt="${ad.name}">`;
                case 'video':
                    return `<video id="ad-video" src="${ad.media_url}" preload="auto"></video>`;
                case 'youtube':
                    return `<iframe src="https://www.youtube.com/embed/${ad.youtube_id}?autoplay=1&mute=1" 
                            width="100%" height="100%" frameborder="0"></iframe>`;
                default:
                    return `<div style="color: white; text-align: center;">Nieobsługiwany typ mediów</div>`;
            }
        }
        
        startTimer(duration) {
            const progressBar = this.adContainer.querySelector('.ad-progress');
            let elapsed = 0;
            
            const timer = setInterval(() => {
                elapsed++;
                const percentage = (elapsed / duration) * 100;
                progressBar.style.width = percentage + '%';
                
                if (elapsed >= duration) {
                    clearInterval(timer);
                    this.finishAd();
                }
            }, 1000);
        }
        
        finishAd() {
            if (!this.currentAd) return;
            console.log('[AdDisplay] finishAd wywołane dla:', this.currentAd);
            fetch('/api/ads/view', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `campaign_id=${this.currentAd.id}&client_id=${this.clientId}&duration_seconds=${this.currentAd.duration}`
            });
            this.isPlaying = false;
            this.currentAd = null;
            setTimeout(() => this.loadNextAd(), 2000);
        }
        
        showNoAds() {
            this.adContainer.innerHTML = `
                <div class="ad-content">
                    <div style="text-align: center; color: white; font-size: 2em;">
                        <i class="fas fa-tv mb-3" style="font-size: 3em; display: block;"></i>
                        <h2>Brak aktywnych reklam</h2>
                        <p>System oczekuje na nowe kampanie</p>
                    </div>
                </div>
            `;
        }
        
        showError() {
            this.adContainer.innerHTML = `
                <div class="ad-content">
                    <div style="text-align: center; color: white; font-size: 2em;">
                        <i class="fas fa-exclamation-triangle mb-3" style="font-size: 3em; color: #f39c12;"></i>
                        <h2>Błąd połączenia</h2>
                    </div>
                </div>
            `;
        }
    }
}

// Inicjalizacja
document.addEventListener('DOMContentLoaded', function() {
    if (typeof AdDisplay !== 'undefined') {
        new AdDisplay();
    }
    setupMobileMenu();
    
    // Aktywne menu
    const currentPath = window.location.pathname;
    document.querySelectorAll('.nav-menu a').forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
});

// Funkcje pomocnicze
function formatCurrency(amount) {
    return (amount || 0).toFixed(2) + ' zł';
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleString('pl-PL');
}

// Toast notifications
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container') || createToastContainer();
    
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Usuń toast po zakończeniu
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    document.body.appendChild(container);
    return container;
} 