// Zmienne globalne
let currentAdIndex = 0;
let ytPlayer = null;
let heartbeatInterval = null;
let queueCheckInterval = null;
let queueData = null;
let currentRoomIndex = 0;
let roomRotationInterval = null;
let isVideoPlaying = false;
let videoPlaybackStarted = false;

// Obsługa trybu pełnoekranowego
const displayContainer = document.getElementById("displayContainer");
const fullscreenBtn = document.getElementById("fullscreenBtn");

if (fullscreenBtn) {
  fullscreenBtn.addEventListener("click", toggleFullscreen);
}

// Funkcja do przełączania trybu pełnoekranowego
function toggleFullscreen() {
  if (
    !document.fullscreenElement &&
    !document.mozFullScreenElement &&
    !document.webkitFullscreenElement &&
    !document.msFullscreenElement
  ) {
    // Przejście do trybu pełnoekranowego
    if (displayContainer.requestFullscreen) {
      displayContainer.requestFullscreen();
    } else if (displayContainer.mozRequestFullScreen) {
      displayContainer.mozRequestFullScreen();
    } else if (displayContainer.webkitRequestFullscreen) {
      displayContainer.webkitRequestFullscreen();
    } else if (displayContainer.msRequestFullscreen) {
      displayContainer.msRequestFullscreen();
    }
    if (fullscreenBtn) {
      fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
    }
  } else {
    // Wyjście z trybu pełnoekranowego
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen();
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen();
    }
    if (fullscreenBtn) {
      fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
    }
  }
}

// Nasłuchiwanie zmiany stanu pełnoekranowego
document.addEventListener("fullscreenchange", updateFullscreenButton);
document.addEventListener("webkitfullscreenchange", updateFullscreenButton);
document.addEventListener("mozfullscreenchange", updateFullscreenButton);
document.addEventListener("MSFullscreenChange", updateFullscreenButton);

function updateFullscreenButton() {
  if (fullscreenBtn) {
    if (
      document.fullscreenElement ||
      document.webkitFullscreenElement ||
      document.mozFullScreenElement ||
      document.msFullscreenElement
    ) {
      fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
    } else {
      fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
    }
  }
}

// Funkcje API
function sendHeartbeat() {
  fetch("/client/heartbeat", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: `display_id=${display.id}`,
  });
}

function recordAdView(campaignId, durationSeconds) {
  fetch("/api/ads/view", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: `campaign_id=${campaignId}&client_id=${display.client_id}&duration_seconds=${durationSeconds}`,
  });
}

// Funkcja do sprawdzania i aktualizacji danych o kolejce
function checkQueueStatus() {
  // Zawsze sprawdzaj status kolejki - system kolejkowy zawsze włączony
  fetch(`/api/queue/${display.client_id}`)
    .then((response) => response.json())
    .then((data) => {
      const newDataStr = JSON.stringify(data);
      const oldDataStr = JSON.stringify(queueData);

      if (newDataStr !== oldDataStr) {
        queueData = data;
        updateQueueDisplay();

        // Ustawienie rotacji sal, jeśli jest więcej niż 1 sala
        if (queueData.queue.length > 1) {
          if (!roomRotationInterval) {
            roomRotationInterval = setInterval(rotateRooms, 10000);
          }
        } else {
          if (roomRotationInterval) {
            clearInterval(roomRotationInterval);
            roomRotationInterval = null;
          }
        }
      }
    })
    .catch((error) => {
      console.error("Błąd podczas pobierania danych o kolejce:", error);
    });
}

// Funkcja do rotacji sal
function rotateRooms() {
  if (!queueData || !queueData.queue || queueData.queue.length <= 1) return;

  const currentRoomContainer = document.querySelector(
    `.queue-rooms-container.active`,
  );
  if (currentRoomContainer) {
    currentRoomContainer.classList.remove("active");
  }

  currentRoomIndex = (currentRoomIndex + 1) % queueData.queue.length;

  const nextRoomContainer = document.querySelector(
    `.queue-rooms-container[data-room-index="${currentRoomIndex}"]`,
  );
  if (nextRoomContainer) {
    nextRoomContainer.classList.add("active");
  }
}

// Funkcja do aktualizacji wyświetlania kolejki
function updateQueueDisplay() {
  if (!queueData) return;

  // Sprawdź, czy istnieje panel boczny kolejki, jeśli nie - utwórz go
  let queueSidebar = document.querySelector(".queue-sidebar");
  if (!queueSidebar) {
    const contentContainer = document.getElementById("contentContainer");
    if (!contentContainer) return;

    // Przekształć kontener na układ z kolejką
    contentContainer.parentElement.classList.add("display-with-queue");

    // Utwórz panel boczny kolejki
    queueSidebar = document.createElement("div");
    queueSidebar.className = "queue-sidebar";
    contentContainer.parentElement.insertBefore(queueSidebar, contentContainer);

    // Zmień klasę kontenera reklam
    contentContainer.className = "queue-ad-container";
  }

  queueSidebar.innerHTML = "";

  queueData.queue.forEach((queueItem, index) => {
    const roomContainer = document.createElement("div");
    roomContainer.className = `queue-rooms-container ${index === currentRoomIndex ? "active" : ""}`;
    roomContainer.setAttribute("data-room-index", index);

    const room = queueItem.room;
    const current = queueItem.current;
    const waiting = queueItem.waiting;

    const roomElement = document.createElement("div");
    roomElement.className = "queue-room";

    // Nagłówek sali z informacjami o lekarzu
    const roomHeader = document.createElement("div");
    roomHeader.className = "queue-room-header";

    if (room.doctor_first_name && room.doctor_last_name) {
      roomHeader.innerHTML = `
                <div class="doctor-photo slide-in-top">
                    ${room.doctor_photo ? `<img src="${room.doctor_photo}" alt="Lekarz">` : '<i class="fas fa-user-md" style="font-size: 60px; color: #fff; line-height: 150px;"></i>'}
                </div>
                <div class="slide-in-bottom">
                    <div class="room-name">
                        ${room.name}
                    </div>
                    <div class="doctor-name">
                        ${room.doctor_first_name} ${room.doctor_last_name}
                    </div>
                </div>
            `;
    } else {
      roomHeader.innerHTML = `<div class="room-name">${room.name}</div>`;
    }
    roomElement.appendChild(roomHeader);

    // Aktualna wizyta
    const currentElement = document.createElement("div");
    currentElement.className = "queue-current slide-in-left";
    if (current) {
      const time = current.appointment_time || current.number;
      let patientName = current.patient_name
        ? current.patient_name.split(" ")[0]
        : `Numer ${current.number}`;
      if (current.patient_name) {
        const firstName = current.patient_name.split(" ")[0];
        patientName = `p. ${firstName.charAt(0).toUpperCase() + firstName.slice(1).toLowerCase()}`;
      }
      currentElement.innerHTML = `
                <div class="current-label">Aktualnie:</div>
                <div class="current-number">${time}</div>
                <div class="current-patient">${patientName}</div>
            `;
    } else {
      currentElement.innerHTML = `
                <div class="current-label">Aktualnie:</div>
                <div class="current-number">-</div>
                <div class="current-patient">Brak wizyty</div>
            `;
    }
    roomElement.appendChild(currentElement);

    // Oczekujące wizyty
    if (waiting && waiting.length > 0) {
      const waitingElement = document.createElement("div");
      waitingElement.className = "queue-waiting slide-in-right";
      waitingElement.innerHTML =
        '<div class="waiting-label">Następne wizyty:</div>';

      waiting.forEach((appointment) => {
        const appointmentElement = document.createElement("div");
        appointmentElement.className = "queue-number";

        const time =
          appointment.appointment_time || `Numer ${appointment.number}`;
        let patientName = appointment.patient_name
          ? appointment.patient_name.split(" ")[0]
          : `Pacjent ${appointment.number}`;
        if (appointment.patient_name) {
          const firstName = appointment.patient_name.split(" ")[0];
          patientName = `p. ${firstName.charAt(0).toUpperCase() + firstName.slice(1).toLowerCase()}`;
        }

        appointmentElement.innerHTML = `
                    <div class="number">${time}</div>
                    <div class="patient">${patientName}</div>
                `;
        waitingElement.appendChild(appointmentElement);
      });

      roomElement.appendChild(waitingElement);
    }

    roomContainer.appendChild(roomElement);
    queueSidebar.appendChild(roomContainer);
  });
}

// Funkcja wyświetlania reklam
function showNextAd() {
  if (campaigns.length === 0) {
    // Nie wyświetlaj żadnych komunikatów - system kolejkowy działa niezależnie
    return;
  }

  if (ytPlayer) {
    ytPlayer.stopVideo();
    ytPlayer = null;
  }

  const ad = campaigns[currentAdIndex];
  let adContainer;

  if (queueSystemEnabled) {
    const contentContainer = document.getElementById("contentContainer");

    if (!document.querySelector(".display-with-queue")) {
      contentContainer.innerHTML = `
                <div class="display-with-queue">
                    <div class="queue-sidebar">
                        <div class="queue-header">System kolejkowy</div>
                    </div>
                    <div class="queue-ad-container" id="adDisplay"></div>
                </div>
            `;

      checkQueueStatus();
      queueCheckInterval = setInterval(checkQueueStatus, 5000);
    }

    adContainer = document.getElementById("adDisplay");
  } else {
    adContainer = document.getElementById("contentContainer");
  }

  adContainer.innerHTML = "";
  adContainer.removeAttribute("style");

  let adElement = null;
  let adDuration = ad.duration || 15;

  switch (ad.media_type) {
    case "image":
      adElement = document.createElement("img");
      adElement.className = "ad-image";
      adElement.src = ad.media_url;
      adElement.alt = ad.name;
      adElement.style.display = "block";
      adElement.style.margin = "auto";

      const imageTimeout = setTimeout(function () {
        console.error("Timeout podczas ładowania obrazu:", ad.media_url);
        rotateAd();
      }, 5000);

      adElement.onerror = function () {
        clearTimeout(imageTimeout);
        console.error("Błąd podczas ładowania obrazu:", ad.media_url);
        setTimeout(function () {
          rotateAd();
        }, 1000);
      };

      adElement.onload = function () {
        clearTimeout(imageTimeout);
        if (adElement.naturalWidth > adElement.naturalHeight) {
          adElement.style.width = "calc(100vw - 300px)";
          adElement.style.height = "auto";
          adElement.style.maxHeight = "100vh";
        } else {
          adElement.style.height = "100vh";
          adElement.style.width = "auto";
          adElement.style.maxWidth = "calc(100vw - 300px)";
        }
      };

      adContainer.style.display = "flex";
      adContainer.style.justifyContent = "center";
      adContainer.style.alignItems = "center";
      adContainer.appendChild(adElement);
      break;

    case "video":
      const videoContainer = document.createElement("div");
      videoContainer.className = "video-container";
      videoContainer.style.display = "flex";
      videoContainer.style.justifyContent = "center";
      videoContainer.style.alignItems = "center";

      adElement = document.createElement("video");
      adElement.className = "ad-video";
      adElement.src = ad.media_url;
      adElement.controls = false;
      adElement.autoplay = false;
      adElement.muted = false;
      adElement.preload = "auto";
      adElement.loop = false;
      adElement.playsInline = true;
      adElement.style.display = "block";
      adElement.style.margin = "auto";

      adElement.onerror = function () {
        console.error("Błąd podczas ładowania pliku wideo:", ad.media_url);
        setTimeout(function () {
          rotateAd();
        }, 1000);
      };

      adElement.onloadedmetadata = function () {
        if (adElement.videoWidth > adElement.videoHeight) {
          adElement.style.width = "calc(100vw - 300px)";
          adElement.style.height = "auto";
          adElement.style.maxHeight = "100vh";
        } else {
          adElement.style.height = "100vh";
          adElement.style.width = "auto";
          adElement.style.maxWidth = "calc(100vw - 300px)";
        }

        // Jeśli już rozpoczęto odtwarzanie wideo, automatycznie odtwórz
        if (videoPlaybackStarted) {
          adElement.currentTime = 0;
          adElement
            .play()
            .then(function () {
              adElement.addEventListener(
                "ended",
                function () {
                  console.log("[Video] Zakończono odtwarzanie video");
                  isVideoPlaying = false;
                  recordAdView(ad.id, adDuration);
                  rotateAd();
                },
                { once: true },
              );
            })
            .catch(function (error) {
              console.error(
                "Błąd podczas automatycznego odtwarzania wideo:",
                error,
              );
              setTimeout(function () {
                rotateAd();
              }, 1000);
            });
        }
      };

      // Tylko pierwsza reklama wymaga kliknięcia play
      if (!videoPlaybackStarted) {
        const playButton = document.createElement("div");
        playButton.className = "play-button";
        playButton.innerHTML = '<i class="fas fa-play"></i>';
        playButton.style.zIndex = "1000";

        playButton.addEventListener("click", function () {
          if (isVideoPlaying) return;

          isVideoPlaying = true;
          videoPlaybackStarted = true; // Oznacz że rozpoczęto odtwarzanie

          adElement.currentTime = 0;
          adElement
            .play()
            .then(function () {
              playButton.classList.add("hidden");

              adElement.addEventListener(
                "ended",
                function () {
                  console.log("[Video] Zakończono odtwarzanie video");
                  isVideoPlaying = false;
                  recordAdView(ad.id, adDuration);
                  rotateAd();
                },
                { once: true },
              );
            })
            .catch(function (error) {
              console.error("Błąd podczas odtwarzania wideo:", error);
              isVideoPlaying = false;
              setTimeout(function () {
                rotateAd();
              }, 1000);
            });
        });

        videoContainer.appendChild(playButton);
      }

      videoContainer.appendChild(adElement);
      adContainer.style.display = "flex";
      adContainer.style.justifyContent = "center";
      adContainer.style.alignItems = "center";
      adContainer.appendChild(videoContainer);
      break;

    case "youtube":
      const youtubeContainer = document.createElement("div");
      youtubeContainer.id = "youtubePlayer";
      youtubeContainer.className = "ad-youtube";
      adContainer.appendChild(youtubeContainer);

      ytPlayer = new YT.Player("youtubePlayer", {
        height: "100%",
        width: "100%",
        videoId: ad.youtube_id,
        playerVars: {
          autoplay: 1,
          controls: 0,
          mute: 1,
          rel: 0,
        },
        events: {
          onReady: function (event) {
            event.target.playVideo();
          },
          onStateChange: function (event) {
            if (event.data === YT.PlayerState.ENDED) {
              recordAdView(ad.id, adDuration);
              rotateAd();
            }
          },
          onError: function (event) {
            console.error(
              "Błąd podczas ładowania filmu YouTube:",
              ad.youtube_id,
            );
            setTimeout(function () {
              rotateAd();
            }, 1000);
          },
        },
      });
      break;

    default:
      adElement = document.createElement("div");
      adElement.className = "ad-container";
      adElement.innerHTML = `<h2>${ad.name}</h2><p>${ad.description}</p>`;
      adContainer.appendChild(adElement);
  }

  // Jeśli to nie jest YouTube (obsługiwany przez zdarzenia) lub wideo (obsługiwane przez onloadedmetadata)
  if (ad.media_type !== "youtube" && ad.media_type !== "video") {
    setTimeout(function () {
      recordAdView(ad.id, adDuration);
      rotateAd();
    }, adDuration * 1000);
  }
}

function rotateAd() {
  if (campaigns.length === 0) return;

  currentAdIndex = (currentAdIndex + 1) % campaigns.length;
  showNextAd();
}

// Inicjalizacja
document.addEventListener("DOMContentLoaded", function () {
  // Zawsze inicjalizuj system kolejkowy
  checkQueueStatus();
  queueCheckInterval = setInterval(checkQueueStatus, 5000);

  // Pokaż reklamy tylko jeśli są dostępne
  if (campaigns.length > 0) {
    showNextAd();
  } else {
    // Jeśli nie ma reklam, przygotuj interfejs dla samego systemu kolejkowego
    const contentContainer = document.getElementById("contentContainer");
    if (contentContainer) {
      contentContainer.parentElement.classList.add("display-with-queue");

      // Utwórz panel boczny kolejki
      const queueSidebar = document.createElement("div");
      queueSidebar.className = "queue-sidebar";
      contentContainer.parentElement.insertBefore(
        queueSidebar,
        contentContainer,
      );

      // Zmień klasę kontenera treści
      contentContainer.className = "queue-ad-container";

      // Pusty kontener bez dodatkowych komunikatów
      contentContainer.innerHTML = "";
    }
  }

  sendHeartbeat();
  setInterval(sendHeartbeat, 60000);
});

// Funkcja dla YouTube API
function onYouTubeIframeAPIReady() {
  if (
    campaigns.length > 0 &&
    campaigns[currentAdIndex].media_type === "youtube"
  ) {
    showNextAd();
  }
}
