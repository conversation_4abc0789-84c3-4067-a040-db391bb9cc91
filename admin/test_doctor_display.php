<?php
require_once 'core/Database.php';
require_once 'models/ExternalDoctorMapping.php';

// Test wyświetlania lekarzy w formacie HTML
$externalDoctorMapping = new ExternalDoctorMapping();
$systemDoctors = $externalDoctorMapping->getAvailableSystemDoctors(2);

echo "=== TEST WYŚWIETLANIA LEKARZY W FORMACIE HTML ===\n\n";
echo "Liczba lekarzy: " . count($systemDoctors) . "\n\n";

echo "Symulacja HTML select options:\n";
echo "<select>\n";
echo "    <option value=\"\">Brak przypisania</option>\n";

foreach ($systemDoctors as $doctor) {
    $displayName = htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']);
    $specialization = $doctor['specialization'] ? ' (' . htmlspecialchars($doctor['specialization']) . ')' : '';
    
    echo "    <option value=\"{$doctor['id']}\">$displayName$specialization</option>\n";
}

echo "</select>\n\n";

echo "=== SPRAWDZENIE PROBLEMÓW Z WYŚWIETLANIEM ===\n";

$problems = [];

foreach ($systemDoctors as $doctor) {
    $fullName = trim($doctor['first_name'] . ' ' . $doctor['last_name']);
    
    // Sprawdź długość nazwy
    if (strlen($fullName) > 50) {
        $problems[] = "ID {$doctor['id']}: Zbyt długa nazwa ({strlen($fullName)} znaków): $fullName";
    }
    
    // Sprawdź czy nazwa jest pusta
    if (empty($fullName)) {
        $problems[] = "ID {$doctor['id']}: Pusta nazwa lekarza";
    }
    
    // Sprawdź specjalne znaki
    if (preg_match('/[<>&"\']/u', $fullName)) {
        $problems[] = "ID {$doctor['id']}: Specjalne znaki HTML w nazwie: $fullName";
    }
    
    // Sprawdź czy first_name lub last_name zawiera HTML
    if (strip_tags($doctor['first_name']) !== $doctor['first_name'] || 
        strip_tags($doctor['last_name']) !== $doctor['last_name']) {
        $problems[] = "ID {$doctor['id']}: HTML w nazwie lekarza: $fullName";
    }
}

if (empty($problems)) {
    echo "✅ Nie znaleziono problemów z wyświetlaniem\n";
} else {
    echo "⚠️  Znalezione problemy:\n";
    foreach ($problems as $problem) {
        echo "- $problem\n";
    }
}

echo "\n=== SPRAWDZENIE SORTOWANIA ===\n";

// Sprawdź czy sortowanie działa poprawnie
$sortedByFirstName = $systemDoctors;
usort($sortedByFirstName, function($a, $b) {
    return strcmp($a['first_name'], $b['first_name']);
});

echo "Pierwsze 5 lekarzy po sortowaniu:\n";
for ($i = 0; $i < min(5, count($sortedByFirstName)); $i++) {
    $doctor = $sortedByFirstName[$i];
    echo "- {$doctor['first_name']} {$doctor['last_name']}\n";
}

echo "\n=== SPRAWDZENIE DUPLIKATÓW ===\n";

$names = [];
$duplicates = [];

foreach ($systemDoctors as $doctor) {
    $fullName = trim($doctor['first_name'] . ' ' . $doctor['last_name']);
    if (isset($names[$fullName])) {
        $duplicates[] = $fullName;
    }
    $names[$fullName] = $doctor['id'];
}

if (empty($duplicates)) {
    echo "✅ Nie znaleziono duplikatów nazw\n";
} else {
    echo "⚠️  Znalezione duplikaty:\n";
    foreach ($duplicates as $duplicate) {
        echo "- $duplicate\n";
    }
}

echo "\n=== PODSUMOWANIE ===\n";
echo "Wszystkich lekarzy: " . count($systemDoctors) . "\n";
echo "Unikalnych nazw: " . count($names) . "\n";
echo "Problemów z wyświetlaniem: " . count($problems) . "\n";
echo "Duplikatów: " . count($duplicates) . "\n";
?>
