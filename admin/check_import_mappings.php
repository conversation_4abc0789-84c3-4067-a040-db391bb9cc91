<?php
require_once 'core/Database.php';
require_once 'models/ExternalDoctorMapping.php';
require_once 'models/ImportSetting.php';

echo "=== SPRAWDZENIE MAPOWAŃ IMPORTU ===\n\n";

$db = Database::getInstance()->getConnection();

// Sprawdź ustawienia importu dla klienta Sonokard
$stmt = $db->prepare("SELECT * FROM import_settings WHERE client_id = 2");
$stmt->execute();
$importSettings = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "Ustawienia importu dla klienta Sonokard (ID=2):\n";
if (empty($importSettings)) {
    echo "❌ Brak ustawień importu\n";
} else {
    foreach ($importSettings as $setting) {
        echo "- ID: {$setting['id']}, Nazwa: {$setting['name']}, Aktywne: " . ($setting['active'] ? 'TAK' : 'NIE') . "\n";
    }
}

echo "\n";

// Dla każdego ustawienia importu sprawdź mapowania
$externalDoctorMapping = new ExternalDoctorMapping();

foreach ($importSettings as $setting) {
    echo "=== MAPOWANIA DLA USTAWIENIA: {$setting['name']} (ID: {$setting['id']}) ===\n";
    
    // Pobierz mapowania
    $mappings = $externalDoctorMapping->getByImportSettingId($setting['id']);
    echo "Liczba mapowań: " . count($mappings) . "\n";
    
    // Pobierz dostępnych lekarzy systemowych
    $systemDoctors = $externalDoctorMapping->getAvailableSystemDoctors($setting['client_id']);
    echo "Dostępni lekarze systemowi: " . count($systemDoctors) . "\n";
    
    if (!empty($mappings)) {
        echo "\nMapowania:\n";
        echo str_pad("ID", 4) . str_pad("Lekarz zewnętrzny", 30) . str_pad("Lekarz systemowy", 30) . "Status\n";
        echo str_repeat("-", 80) . "\n";
        
        foreach ($mappings as $mapping) {
            $systemDoctor = $mapping['first_name'] ? 
                $mapping['first_name'] . ' ' . $mapping['last_name'] : 
                'BRAK PRZYPISANIA';
            $status = $mapping['is_mapped'] ? 'PRZYPISANY' : 'NIEPRZYPISANY';
            
            echo str_pad($mapping['id'], 4) . 
                 str_pad(substr($mapping['external_doctor_name'], 0, 28), 30) . 
                 str_pad(substr($systemDoctor, 0, 28), 30) . 
                 $status . "\n";
        }
    }
    
    echo "\n";
}

// Sprawdź czy są jakieś problemy z dostępnością lekarzy
echo "=== ANALIZA DOSTĘPNOŚCI LEKARZY ===\n";

foreach ($importSettings as $setting) {
    $systemDoctors = $externalDoctorMapping->getAvailableSystemDoctors($setting['client_id']);
    
    echo "Ustawienie: {$setting['name']}\n";
    echo "Dostępni lekarze: " . count($systemDoctors) . "\n";
    
    // Sprawdź czy są lekarze z pustymi nazwami
    $emptyNames = 0;
    foreach ($systemDoctors as $doctor) {
        if (empty(trim($doctor['first_name'] . ' ' . $doctor['last_name']))) {
            $emptyNames++;
        }
    }
    
    if ($emptyNames > 0) {
        echo "⚠️  Lekarze z pustymi nazwami: $emptyNames\n";
    } else {
        echo "✅ Wszystkie nazwy lekarzy są wypełnione\n";
    }
    
    echo "\n";
}

// Test konkretnego przypadku - symulacja wywołania kontrolera
echo "=== TEST SYMULACJI KONTROLERA ===\n";

if (!empty($importSettings)) {
    $testSetting = $importSettings[0];
    echo "Testowanie dla ustawienia: {$testSetting['name']} (ID: {$testSetting['id']})\n";
    
    // Symuluj to co robi kontroler
    $mappings = $externalDoctorMapping->getByImportSettingId($testSetting['id']);
    $systemDoctors = $externalDoctorMapping->getAvailableSystemDoctors($testSetting['client_id']);
    
    echo "Kontroler otrzymałby:\n";
    echo "- Mapowania: " . count($mappings) . "\n";
    echo "- Lekarze systemowi: " . count($systemDoctors) . "\n";
    
    // Sprawdź czy wszystkie dane są poprawne
    $allGood = true;
    
    foreach ($systemDoctors as $doctor) {
        if (!isset($doctor['id']) || !isset($doctor['first_name']) || !isset($doctor['last_name'])) {
            echo "❌ Niepełne dane dla lekarza ID: " . ($doctor['id'] ?? 'BRAK') . "\n";
            $allGood = false;
        }
    }
    
    if ($allGood) {
        echo "✅ Wszystkie dane lekarzy są kompletne\n";
    }
}

echo "\n=== PODSUMOWANIE ===\n";
echo "Problem z niepełną listą lekarzy w mapowaniu został prawdopodobnie rozwiązany\n";
echo "poprzez naprawienie pustych pól first_name dla trzech lekarzy.\n";
echo "Wszystkie 34 lekarzy powinny być teraz dostępne w liście mapowania.\n";
?>
